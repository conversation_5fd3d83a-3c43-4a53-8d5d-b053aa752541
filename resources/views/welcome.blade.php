<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ config('app.name') }}</title>
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&display=swap" rel="stylesheet">
    <link href="{{ asset('assets/css/bootstrap.css') }}" rel="stylesheet">
    <script src="{{ asset('assets/js/bootstrap.bundle.js') }}"></script>
    <script src="{{ asset('assets/js/jquery-3.6.0.min.js') }}"></script>
    <script src="{{ asset('assets/js/toastr.min.js') }}"></script>
    {{-- <script src="{{ asset('assets/js/toastr.js.map') }}"></script> --}}
    <link href="{{ asset('assets/css/app.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/css/toastr.min.css') }}" rel="stylesheet">
    <script>
        toastr.options = {
            "closeButton": true,
            "newestOnTop": false,
            "progressBar": false,
            "positionClass": "toast-top-right",
            "preventDuplicates": false,
            "onclick": null,
            "showDuration": "300",
            "hideDuration": "150",
            "timeOut": "1000",
            "extendedTimeOut": "1000",
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut"
        }

        const refresh = () => {
            $.get("/api/settings/refresh", function (data) {
                toastr.success(data.data);
            });
        };
        const migrate = () => {
            $.get("/api/settings/migrate", function (data) {
                toastr.success(data.data);
            });
        };
    </script>

</head>

<body class="antialiased">
<div class="relative items-top justify-center min-h-screen bg-gray-100  sm:items-center py-6 sm:pt-0 ">
    <div class="max-w-6xl mx-auto sm:px-6 lg:px-8">
        <div class="justify-center pt-8 sm:justify-between sm:pt-0 text-center">
            <img class="p-3 pb-0" src="{{ url('/assets/logo.png') }}" alt="Image"/>
        </div>
        <div><h6 style="text-align: center;">@version('full')</h6></div>
        <div class="mt-8 row">
            <div class="col-12 mb-3">
                <div class="card bg-transparent overflow-hidden shadow sm:rounded-lg h-100">
                    <div class="card-body">
                        <div class="float-end">
                            <a class="btn btn-warning" href="{{


    ('logs') }}">
                                Logs
                            </a>
                            <button type="button" id="refresh" onclick="refresh()" class="btn btn-outline-primary "
                                    aria-label="Close">
                                Refresh
                            </button>

                        </div>
                    </div>
                </div>
            </div>

            <div class="col-12">
                <div class="card bg-white dark:bg-gray-800 overflow-hidden shadow sm:rounded-lg h-100">
                    <div class="card-body">
                        <h5 class="card-title">
                            <div class="flex items-center">
                                <svg fill="none" stroke="currentColor" stroke-linecap="round"
                                     stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24"
                                     class="w-8 h-8 text-gray-500">
                                    <path
                                            d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253">
                                    </path>
                                </svg>
                                <div class="ml-4 text-lg leading-7 font-semibold"><span
                                            class="underline text-gray-900 dark:text-white text-uppercase">Propreties</span>
                                </div>
                            </div>
                        </h5>
                        <span class="card-text text-sm text-gray-600">
                                <ul class="list-group">
                                    <li><span><b>Current PHP Version</b></span>
                                        <span
                                                class="c-pill c-pill--{{ $has_valid_php_version ? 'success' : 'danger' }} float-end">{{ $php_version }}</span>
                                    </li>
                                    <li><span><b>SQL SERVER Extension</b></span>
                                        <span
                                                class="c-pill c-pill--{{ $has_sqlsrv ? 'success' : 'danger' }} float-end">{{ $has_sqlsrv ? 'exist' : 'not_exist' }}</span>
                                    </li>
                                    <li><span><b>PDO Extension</b></span>
                                        <span
                                                class="c-pill c-pill--{{ $has_pdo_sqlsrv ? 'success' : 'danger' }} float-end">{{ $has_pdo_sqlsrv ? 'exist' : 'not_exist' }}</span>
                                    </li>
                                    <li><span><b>Database Connection</b></span>
                                        <span
                                                class="c-pill c-pill--{{ $db ? 'success' : 'danger' }} float-end">{{ $db ? 'exist' : 'not_exist' }}</span>
                                    </li>
                                    <li> <span><b>Minimum Supported Version for DB</b></span>
                                        <span
                                                class="c-pill c-pill--{{ $has_valid_version ? 'success' : 'danger' }} float-end">{{ env('MIN_DB_VERSION') }}</span>
                                    </li>
                                    <li><span><b>Minimum Supported Version for Mobile</b></span>
                                        <span
                                                class="c-pill c-pill--grey float-end ">{{ env('MIN_VERSION_CODE') }}</span>

                                    </li>
                                    @if ($db)
                                        <li><span><b>Migration</b></span>
                                            <span class="c-pill  float-end">
                                                <button type="button" id="migrate" onclick="migrate()"
                                                        class="btn btn-outline-primary float-end" aria-label="Close">
                                                    Migrate
                                                </button>
                                            </span>
                                        </li>
                                        <li><span><b>Check Database migrations</b></span>
                                            <span class="c-pill  float-end">
                                                <button type="button" class="btn btn-outline-primary float-end"
                                                        data-bs-toggle="modal" data-bs-target="#MigrationModal">
                                                    Check
                                                </button>
                                            </span>
                                        </li>
                                    @endif
                                </ul>
                            </span>
                    </div>
                </div>
            </div>
        </div>
        <!-- Modal -->
        <div class="modal fade" id="MigrationModal" tabindex="-1" aria-labelledby="MigrationModal"
             aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel">Database migrations</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"
                                aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                            <span class="card-text text-sm text-gray-600">
                                <ul class="list-group">
                                    @foreach ($Schemas as $key => $value)
                                        <li>
                                            <span><b>{{ $key }}</b></span>
                                            <span
                                                    class="c-pill c-pill--{{ $value ? 'success' : 'danger' }} float-end">{{ $value ? 'exist' : 'not_exist' }}</span>
                                        </li>
                                    @endforeach
                                </ul>
                            </span>
                    </div>

                </div>
            </div>
        </div>

        <!-- Modal Settings -->
        {{-- <div class="modal fade" id="SettingModal" tabindex="-1" aria-labelledby="SettingModal"
            aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel">Settings</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <span class="card-text text-sm text-gray-600">
                            <form name="add-blog-post-form" id="add-blog-post-form" method="post"
                                action="{{ url('settings') }}">
                                @csrf

                                <div class="row">
                                    <div class="col-md-12">
                                        <label for="name" class="form-label">Client name</label>
                                        <input type="text" id="name" class="form-control" name="name" value="{{$settings && $settings->name ? $settings->name : ''}}">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="type" class="form-label">Type</label>
                                        <input type="text" id="type" class="form-control" name="type" value="{{$settings && $settings->type ? $settings->type : ''}}">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="theme" class="form-label">Theme</label>
                                        <input type="color" id="theme" class="form-control" name="theme" value="{{$settings && $settings->theme ? $settings->theme : ''}}">
                                    </div>

                                    <div class="col-md-6">
                                        <label for="DBVersion" class="form-label">Supported Version for
                                            DB</label>
                                        <input type="text" id="DBVersion" class="form-control" name="DBVersion" value="{{$settings && $settings->DBVersion ? $settings->DBVersion : ''}}">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="MobileVersion" class="form-label">Supported Version for
                                            Mobile</label>
                                        <input type="text" id="MobileVersion" class="form-control"
                                            name="MobileVersion" value="{{$settings && $settings->MobileVersion ? $settings->MobileVersion : ''}}">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="versioning" class="form-label">Versioning</label>
                                        <input type="text" id="versioning" class="form-control" name="versioning" value="{{$settings && $settings->versioning ? $settings->versioning : ''}}">
                                    </div>
                                    <div class=" col-md-6">
                                        <label for="status" class="form-label">Status</label>
                                        <select class="form-select" name="isActive">
                                            <option value="1" {{$settings && $settings->isActive = 1 ? 'selected' : ''}}>Enabled</option>
                                            <option value="0" {{$settings && $settings->isActive = 0 ? 'selected' : ''}}>Disabled</option>
                                          </select>
                                    </div>

                                </div>
                                <button type="submit" class="btn btn-primary mt-5">Save</button>

                                <form>
                        </span>
                    </div>
                </div>
            </div>
        </div> --}}
        <div class="text-center text-sm text-gray-500 pt-8">
            <a href="https://www.asm-tunisie.com/" class="ml-1 underline">
                ASM - ALL SOFT MULTIMEDIA</a>
        </div>
    </div>
</div>
</body>

</html>
