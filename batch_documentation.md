# Documentation Batch - Contrôleurs Restants

## Contrôleurs à documenter rapidement

### 1. StationController
Routes: `/Station/getStations`, `/Station/addStation`

### 2. CarnetController  
Routes: `/Carnet/getCarnets`, `/Carnet/addCarnet`

### 3. TVAWSMobile
Routes: `/TVA/getTVA`, `/TVA/addTVA`, `/TVA/updateTVA`, `/TVA/deleteTVA`

### 4. UniteWSMobile
Routes: `/Unite/getUnite`, `/Unite/addUnite`, `/Unite/updateUnite`, `/Unite/deleteUnite`

### 5. CouleurController
Routes: `/Couleur/getCouleurs`, `/Couleur/addCouleur`

### 6. ModeleController
Routes: `/Modele/getModeles`, `/Modele/addModele`

### 7. UtilisateurWSMobile
Routes: `/Utilisateur/getUtilisateurs`, `/Utilisateur/addUtilisateur`

### 8. ZoneController
Routes: `/Zone/getZones`, `/Zone/addZone`

### 9. ExerciceController
Routes: `/Exercice/getExercices`, `/Exercice/addExercice`

### 10. PrefixWSMobile
Routes: `/Prefix/getPrefix`

## Schémas à ajouter

- TVA
- UniteWSMobile  
- Station
- Carnet
- Zone
- Exercice
- Prefix

## Progression
- Total contrôleurs identifiés: 50+
- Contrôleurs documentés: 20+
- Contrôleurs restants: 30+
