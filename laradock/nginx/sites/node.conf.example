#server {
#    listen 80;
#    server_name node.com.co;
#    return 301 https://node.com.co$request_uri;
#}

server {

    listen 80;
    listen [::]:80;

    #listen 443;
    #listen [::]:443;

    server_name node.com.co;

    #ssl on;
    #ssl_certificate /var/certs/node.com.co/cert.pem;
    #ssl_certificate_key /var/certs/node.com.co/privkey.pem;
    #ssl_protocols       SSLv3 TLSv1 TLSv1.1 TLSv1.2;
    #ssl_ciphers         HIGH:!aNULL:!MD5;


    location / {
        proxy_pass http://node.com.co:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_cache_bypass $http_upgrade;
    }

    location ~ /\.ht {
        deny all;
    }

    location /.well-known/acme-challenge/ {
        root /var/www/letsencrypt/;
        log_not_found off;
    }

    error_log /var/log/nginx/node.com.co.local_error.log;
    access_log /var/log/nginx/node.com.co.local_access.log;
}
