###########################################################
###################### General Setup ######################
###########################################################

### Paths #################################################

# Point to the path of your applications code on your host
APP_CODE_PATH_HOST=../

# Point to where the `APP_CODE_PATH_HOST` should be in the container
APP_CODE_PATH_CONTAINER=/var/www

# You may add flags to the path `:cached`, `:delegated`. When using Docker Sync add `:nocopy`
APP_CODE_CONTAINER_FLAG=:nocopy

# Choose storage path on your machine. For all storage systems
#DATA_PATH_HOST=~/.laradock/data
DATA_PATH_HOST=~/.laradock/data
### Drivers ################################################

# All volumes driver
VOLUMES_DRIVER=local

# All Networks driver
NETWORKS_DRIVER=bridge

### Docker compose files ##################################

# Select which docker-compose files to include. If using docker-sync append `:docker-compose.sync.yml` at the end
COMPOSE_FILE=docker-compose.yml

# Change the separator from : to ; on Windows
COMPOSE_PATH_SEPARATOR=;

# Define the prefix of container names. This is useful if you have multiple projects that use laradock to have separate containers per project.
COMPOSE_PROJECT_NAME=laradock

### PHP Version ###########################################

# Select a PHP version of the Workspace and PHP-FPM containers (Does not apply to HHVM).
# Accepted values: 8.0 - 7.4 - 7.3 - 7.2 - 7.1 - 7.0 - 5.6
PHP_VERSION=8.0

### Phalcon Version ###########################################

# Select a Phalcon version of the Workspace and PHP-FPM containers (Does not apply to HHVM). Accepted values: 3.4.0+
PHALCON_VERSION=4.0.5

### PHP Interpreter #######################################

# Select the PHP Interpreter. Accepted values: hhvm - php-fpm
PHP_INTERPRETER=php-fpm

### Docker Host IP ########################################

# Enter your Docker Host IP (will be appended to /etc/hosts). Default is `*********`
DOCKER_HOST_IP=*********

### Remote Interpreter ####################################

# Choose a Remote Interpreter entry matching name. Default is `laradock`
PHP_IDE_CONFIG=serverName=laradock

### PHP DOWNGRADEOPENSSL TLS AND SECLEVEL #################

PHP_DOWNGRADE_OPENSSL_TLS_AND_SECLEVEL=false

### Windows Path ##########################################

# A fix for Windows users, to ensure the application path works
COMPOSE_CONVERT_WINDOWS_PATHS=1

### Environment ###########################################

# If you need to change the sources (i.e. to China), set CHANGE_SOURCE to true
CHANGE_SOURCE=false
# Set ORACLE INSTANT_CLIENT_MIRROR option if you want to use Intranet improve download, you can download files first
ORACLE_INSTANT_CLIENT_MIRROR=https://github.com/diogomascarenha/oracle-instantclient/raw/master/

### Docker Sync ###########################################

# If you are using Docker Sync. For `osx` use 'native_osx', for `windows` use 'unison', for `linux` docker-sync is not required
DOCKER_SYNC_STRATEGY=unison

### Install Oh My ZSH! ####################################

# If you want to use "Oh My ZSH!" with Laravel autocomplete plugin, set SHELL_OH_MY_ZSH to true.

SHELL_OH_MY_ZSH=true
SHELL_OH_MY_ZSH_AUTOSUGESTIONS=true
SHELL_OH_MY_ZSH_ALIASES=true

###########################################################
################ Containers Customization #################
###########################################################

### WORKSPACE #############################################

WORKSPACE_BASE_IMAGE_TAG_PREFIX=latest
WORKSPACE_COMPOSER_GLOBAL_INSTALL=true
WORKSPACE_COMPOSER_VERSION=2
WORKSPACE_COMPOSER_AUTH=false
WORKSPACE_COMPOSER_REPO_PACKAGIST=
WORKSPACE_NVM_NODEJS_ORG_MIRROR=
WORKSPACE_INSTALL_NODE=true
WORKSPACE_NODE_VERSION=node
WORKSPACE_NPM_REGISTRY=
WORKSPACE_NPM_FETCH_RETRIES=2
WORKSPACE_NPM_FETCH_RETRY_FACTOR=10
WORKSPACE_NPM_FETCH_RETRY_MINTIMEOUT=10000
WORKSPACE_NPM_FETCH_RETRY_MAXTIMEOUT=60000
WORKSPACE_INSTALL_PNPM=false
WORKSPACE_INSTALL_YARN=false
WORKSPACE_YARN_VERSION=latest
WORKSPACE_INSTALL_NPM_GULP=false
WORKSPACE_INSTALL_NPM_BOWER=false
WORKSPACE_INSTALL_NPM_VUE_CLI=false
WORKSPACE_INSTALL_NPM_ANGULAR_CLI=false
WORKSPACE_INSTALL_PHPREDIS=false
WORKSPACE_INSTALL_WORKSPACE_SSH=false
WORKSPACE_INSTALL_SUBVERSION=false
WORKSPACE_INSTALL_BZ2=false
WORKSPACE_INSTALL_GMP=false
WORKSPACE_INSTALL_GNUPG=false
WORKSPACE_INSTALL_XDEBUG=true
WORKSPACE_INSTALL_PCOV=false
WORKSPACE_INSTALL_PHPDBG=false
WORKSPACE_INSTALL_SSH2=false
WORKSPACE_INSTALL_LDAP=false
WORKSPACE_INSTALL_SOAP=false
WORKSPACE_INSTALL_XSL=false
WORKSPACE_INSTALL_SMB=false
WORKSPACE_INSTALL_IMAP=false
WORKSPACE_INSTALL_MONGO=false
WORKSPACE_INSTALL_AMQP=false
WORKSPACE_INSTALL_CASSANDRA=false
WORKSPACE_INSTALL_GEARMAN=false
WORKSPACE_INSTALL_MSSQL=false
WORKSPACE_INSTALL_DRUSH=false
WORKSPACE_DRUSH_VERSION=8.4.6
WORKSPACE_INSTALL_DRUPAL_CONSOLE=false
WORKSPACE_INSTALL_WP_CLI=false
WORKSPACE_INSTALL_AEROSPIKE=false
WORKSPACE_INSTALL_OCI8=false
WORKSPACE_INSTALL_V8JS=false
WORKSPACE_INSTALL_LARAVEL_ENVOY=false
WORKSPACE_INSTALL_LARAVEL_INSTALLER=true
WORKSPACE_INSTALL_DEPLOYER=false
WORKSPACE_INSTALL_PRESTISSIMO=false
WORKSPACE_INSTALL_LINUXBREW=false
WORKSPACE_INSTALL_MC=false
WORKSPACE_INSTALL_SYMFONY=false
WORKSPACE_INSTALL_PYTHON=false
WORKSPACE_INSTALL_PYTHON3=false
WORKSPACE_INSTALL_POWERLINE=false
WORKSPACE_INSTALL_SUPERVISOR=false
WORKSPACE_INSTALL_IMAGE_OPTIMIZERS=false
WORKSPACE_INSTALL_IMAGEMAGICK=false
WORKSPACE_IMAGEMAGICK_VERSION=latest
WORKSPACE_INSTALL_TERRAFORM=false
WORKSPACE_INSTALL_DUSK_DEPS=false
WORKSPACE_INSTALL_PG_CLIENT=false
WORKSPACE_INSTALL_PHALCON=false
WORKSPACE_INSTALL_SWOOLE=false
WORKSPACE_INSTALL_TAINT=false
WORKSPACE_INSTALL_LIBPNG=false
WORKSPACE_INSTALL_GRAPHVIZ=false
WORKSPACE_INSTALL_IONCUBE=false # PHP 8.0 is not supported yet. Reference: https://forum.ioncube.com/viewtopic.php?t=4592
WORKSPACE_INSTALL_MYSQL_CLIENT=false
WORKSPACE_INSTALL_PING=false
WORKSPACE_INSTALL_SSHPASS=false
WORKSPACE_INSTALL_INOTIFY=false
WORKSPACE_INSTALL_FSWATCH=false
WORKSPACE_INSTALL_YAML=false
WORKSPACE_INSTALL_RDKAFKA=false
WORKSPACE_INSTALL_MAILPARSE=false
WORKSPACE_INSTALL_XMLRPC=false
WORKSPACE_PUID=1000
WORKSPACE_PGID=1000
WORKSPACE_CHROME_DRIVER_VERSION=2.42
WORKSPACE_TIMEZONE=UTC
WORKSPACE_SSH_PORT=2222
WORKSPACE_INSTALL_FFMPEG=false
WORKSPACE_INSTALL_AUDIOWAVEFORM=false
WORKSPACE_INSTALL_WKHTMLTOPDF=false
WORKSPACE_INSTALL_GNU_PARALLEL=false
WORKSPACE_INSTALL_AST=true
WORKSPACE_AST_VERSION=1.0.10
WORKSPACE_BROWSERSYNC_HOST_PORT=3000
WORKSPACE_BROWSERSYNC_UI_HOST_PORT=3001
WORKSPACE_VUE_CLI_SERVE_HOST_PORT=8080
WORKSPACE_VUE_CLI_UI_HOST_PORT=8001
WORKSPACE_ANGULAR_CLI_SERVE_HOST_PORT=4200
WORKSPACE_INSTALL_GIT_PROMPT=false
WORKSPACE_INSTALL_DOCKER_CLIENT=false
WORKSPACE_INSTALL_LNAV=false
WORKSPACE_INSTALL_PROTOC=false
WORKSPACE_INSTALL_PHPDECIMAL=false
WORKSPACE_INSTALL_ZOOKEEPER=false
WORKSPACE_INSTALL_SSDB=false
WORKSPACE_INSTALL_TRADER=false
WORKSPACE_PROTOC_VERSION=latest
WORKSPACE_INSTALL_MEMCACHED=true

### PHP_FPM ###############################################

PHP_FPM_BASE_IMAGE_TAG_PREFIX=20210814
PHP_FPM_INSTALL_BCMATH=true
PHP_FPM_INSTALL_MYSQLI=true
PHP_FPM_INSTALL_INTL=true
PHP_FPM_INSTALL_IMAGEMAGICK=true
PHP_FPM_IMAGEMAGICK_VERSION=latest
PHP_FPM_INSTALL_OPCACHE=true
PHP_FPM_INSTALL_IMAGE_OPTIMIZERS=true
PHP_FPM_INSTALL_PHPREDIS=true
PHP_FPM_INSTALL_MEMCACHED=false
PHP_FPM_INSTALL_BZ2=false
PHP_FPM_INSTALL_ENCHANT=false
PHP_FPM_INSTALL_GMP=false
PHP_FPM_INSTALL_GNUPG=false
PHP_FPM_INSTALL_XDEBUG=true
PHP_FPM_INSTALL_PCOV=false
PHP_FPM_INSTALL_XHPROF=false
PHP_FPM_INSTALL_PHPDBG=false
PHP_FPM_INSTALL_SMB=false
PHP_FPM_INSTALL_IMAP=false
PHP_FPM_INSTALL_MONGO=false
PHP_FPM_INSTALL_AMQP=false
PHP_FPM_INSTALL_CASSANDRA=false
PHP_FPM_INSTALL_GEARMAN=false
PHP_FPM_INSTALL_MSSQL=true
PHP_FPM_INSTALL_SSH2=false
PHP_FPM_INSTALL_SOAP=false
PHP_FPM_INSTALL_XSL=false
PHP_FPM_INSTALL_EXIF=false
PHP_FPM_INSTALL_AEROSPIKE=false
PHP_FPM_INSTALL_OCI8=false
PHP_FPM_INSTALL_PGSQL=false
PHP_FPM_INSTALL_GHOSTSCRIPT=false
PHP_FPM_INSTALL_LDAP=false
PHP_FPM_INSTALL_PHALCON=false
PHP_FPM_INSTALL_SWOOLE=false
PHP_FPM_INSTALL_TAINT=false
PHP_FPM_INSTALL_PG_CLIENT=false
PHP_FPM_INSTALL_POSTGIS=false
PHP_FPM_INSTALL_PCNTL=false
PHP_FPM_INSTALL_CALENDAR=false
PHP_FPM_INSTALL_FAKETIME=false
PHP_FPM_INSTALL_IONCUBE=false # PHP 8.0 is not supported yet. Reference: https://forum.ioncube.com/viewtopic.php?t=4592
PHP_FPM_INSTALL_RDKAFKA=false
PHP_FPM_INSTALL_GETTEXT=false
PHP_FPM_INSTALL_XMLRPC=false
PHP_FPM_FAKETIME=-0
PHP_FPM_INSTALL_APCU=false
PHP_FPM_INSTALL_CACHETOOL=false
PHP_FPM_INSTALL_YAML=false
PHP_FPM_INSTALL_ADDITIONAL_LOCALES=false
PHP_FPM_INSTALL_MYSQL_CLIENT=false
PHP_FPM_INSTALL_PING=false
PHP_FPM_INSTALL_SSHPASS=false
PHP_FPM_INSTALL_MAILPARSE=false
PHP_FPM_INSTALL_WKHTMLTOPDF=false
PHP_FPM_INSTALL_PHPDECIMAL=false
PHP_FPM_INSTALL_ZOOKEEPER=false
PHP_FPM_INSTALL_SSDB=false
PHP_FPM_INSTALL_TRADER=false
PHP_FPM_FFMPEG=false
PHP_FPM_AUDIOWAVEFORM=false
PHP_FPM_ADDITIONAL_LOCALES="en_US.UTF-8 es_ES.UTF-8 fr_FR.UTF-8"
PHP_FPM_INSTALL_DOCKER_CLIENT=false
PHP_FPM_DEFAULT_LOCALE=POSIX
PHP_FPM_XDEBUG_PORT=9003

PHP_FPM_PUID=1000
PHP_FPM_PGID=1000

### PHP_FPM_NEW_RELIC #####################################

PHP_FPM_NEW_RELIC=false
PHP_FPM_NEW_RELIC_KEY=0000
PHP_FPM_NEW_RELIC_APP_NAME=app_name

### PHP_WORKER ############################################

PHP_WORKER_INSTALL_BZ2=false
PHP_WORKER_INSTALL_GD=false
PHP_WORKER_INSTALL_IMAGEMAGICK=false
PHP_WORKER_IMAGEMAGICK_VERSION=latest
PHP_WORKER_INSTALL_GMP=false
PHP_WORKER_INSTALL_GNUPG=false
PHP_WORKER_INSTALL_LDAP=false
PHP_WORKER_INSTALL_PGSQL=false
PHP_WORKER_INSTALL_MONGO=false
PHP_WORKER_INSTALL_BCMATH=false
PHP_WORKER_INSTALL_MEMCACHED=false
# PHP_WORKER_INSTALL_OCI8 Does not work in php5.6 version
PHP_WORKER_INSTALL_OCI8=false
PHP_WORKER_INSTALL_MSSQL=false
PHP_WORKER_INSTALL_PHALCON=false
PHP_WORKER_INSTALL_SOAP=false
PHP_WORKER_INSTALL_ZIP_ARCHIVE=false
PHP_WORKER_INSTALL_MYSQL_CLIENT=false
PHP_WORKER_INSTALL_AMQP=false
PHP_WORKER_INSTALL_GHOSTSCRIPT=false
PHP_WORKER_INSTALL_SWOOLE=false
PHP_WORKER_INSTALL_TAINT=false
PHP_WORKER_INSTALL_FFMPEG=false
PHP_WORKER_INSTALL_AUDIOWAVEFORM=false
PHP_WORKER_INSTALL_CASSANDRA=false
PHP_WORKER_INSTALL_GEARMAN=false
PHP_WORKER_INSTALL_REDIS=false
PHP_WORKER_INSTALL_IMAP=false
PHP_WORKER_INSTALL_XMLRPC=false
PHP_WORKER_INSTALL_SSDB=false

PHP_WORKER_PUID=1000
PHP_WORKER_PGID=1000

### LARAVEL_HORIZON ################################################

LARAVEL_HORIZON_INSTALL_BZ2=false
LARAVEL_HORIZON_INSTALL_GD=false
LARAVEL_HORIZON_INSTALL_GMP=false
LARAVEL_HORIZON_INSTALL_GNUPG=false
LARAVEL_HORIZON_INSTALL_LDAP=false
LARAVEL_HORIZON_INSTALL_IMAGEMAGICK=false
LARAVEL_HORIZON_IMAGEMAGICK_VERSION=latest
LARAVEL_HORIZON_INSTALL_SOCKETS=false
LARAVEL_HORIZON_INSTALL_YAML=false
LARAVEL_HORIZON_INSTALL_ZIP_ARCHIVE=false
LARAVEL_HORIZON_INSTALL_PHPREDIS=false
LARAVEL_HORIZON_INSTALL_MONGO=false
LARAVEL_HORIZON_INSTALL_CASSANDRA=false
LARAVEL_HORIZON_INSTALL_FFMPEG=false
LARAVEL_HORIZON_INSTALL_AUDIOWAVEFORM=false
LARAVEL_HORIZON_PGID=1000
LARAVEL_HORIZON_PUID=1000

### REDIS #################################################

REDIS_PORT=6379

### CADDY #################################################

CADDY_HOST_HTTP_PORT=80
CADDY_HOST_HTTPS_PORT=443
CADDY_HOST_LOG_PATH=./logs/caddy
CADDY_CONFIG_PATH=./caddy/caddy
CADDY_SSL_PATH=./caddy/ssl/

### LARAVEL ECHO SERVER ###################################

LARAVEL_ECHO_SERVER_PORT=6001

### NGINX #################################################

NGINX_HOST_HTTP_PORT=80
NGINX_HOST_HTTPS_PORT=443
NGINX_HOST_LOG_PATH=./logs/nginx/
NGINX_SITES_PATH=./nginx/sites/
NGINX_PHP_UPSTREAM_CONTAINER=php-fpm
NGINX_PHP_UPSTREAM_PORT=9000
NGINX_SSL_PATH=./nginx/ssl/
