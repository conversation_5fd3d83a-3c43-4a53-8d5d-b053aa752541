<?php

use App\Http\Controllers\Http\DuxInventory\BaseController;
use App\Http\Controllers\Http\DuxInventory\BonEntreeController;
use App\Http\Controllers\Http\DuxInventory\BonLivraisonController;
use App\Http\Controllers\Http\DuxInventory\BonTransfertController;
use App\Http\Controllers\Http\DuxInventory\E_EtatDocumentController;
use App\Http\Controllers\Http\DuxInventory\E_EtatTierController;
use App\Http\Controllers\Http\DuxInventory\E_SuiviStockController;
use App\Http\Controllers\Http\DuxInventory\E_TypeArticleController;
use App\Http\Controllers\Http\DuxInventory\E_TypeUserController;
use App\Http\Controllers\Http\DuxInventory\FournisseurController;
use App\Http\Controllers\Http\DuxInventory\L_LigneInventaireController;
use App\Http\Controllers\Http\DuxInventory\LigneBonEntreeController;
use App\Http\Controllers\Http\DuxInventory\LigneBonLivraisonController;
use App\Http\Controllers\Http\DuxInventory\LigneBonTransfertController;
use App\Http\Controllers\Http\DuxInventory\M_DocumentController;
use App\Http\Controllers\Http\DuxInventory\M_InventaireController;
use App\Http\Controllers\Http\DuxInventory\P_Article_UniteController;
use App\Http\Controllers\Http\DuxInventory\P_ArticleController;
use App\Http\Controllers\Http\DuxInventory\P_CelluleController;
use App\Http\Controllers\Http\DuxInventory\P_CouleurController;
use App\Http\Controllers\Http\DuxInventory\P_DeviseController;
use App\Http\Controllers\Http\DuxInventory\P_ExerciceController;
use App\Http\Controllers\Http\DuxInventory\P_FamilleController;
use App\Http\Controllers\Http\DuxInventory\P_MarqueController;
use App\Http\Controllers\Http\DuxInventory\P_ModeleController;
use App\Http\Controllers\Http\DuxInventory\P_PrefixeController;
use App\Http\Controllers\Http\DuxInventory\P_StationController;
use App\Http\Controllers\Http\DuxInventory\P_taxesController;
use App\Http\Controllers\Http\DuxInventory\P_tierController;
use App\Http\Controllers\Http\DuxInventory\P_TvaController;
use App\Http\Controllers\Http\DuxInventory\P_UniteController;
use App\Http\Controllers\Http\DuxInventory\P_UtilisateurController;
use App\Http\Controllers\Http\DuxInventory\TicketRayonController;
use Illuminate\Support\Facades\Route;

Route::prefix('duxinventory')->group(function () {

    Route::middleware('db.connection')->group(function () {

        Route::prefix('marque')->group(function () {
            Route::post('getAll', [P_MarqueController::class, 'index']);
            Route::post('add', [P_MarqueController::class, 'add']);
        });

        Route::prefix('famille')->group(function () {
            Route::post('getAll', [P_FamilleController::class, 'index']);
            Route::post('add', [P_FamilleController::class, 'add']);
        });

        Route::prefix('unite')->group(function () {
            Route::post('getAll', [P_UniteController::class, 'index']);
            Route::post('add', [P_UniteController::class, 'add']);
        });

        Route::prefix('couleur')->group(function () {
            Route::post('getAll', [P_CouleurController::class, 'index']);
            Route::post('add', [P_CouleurController::class, 'add']);
        });

        Route::prefix('modele')->group(function () {
            Route::post('getAll', [P_ModeleController::class, 'index']);
            Route::post('add', [P_ModeleController::class, 'add']);
        });

        Route::prefix('cellule')->group(function () {
            Route::post('getAll', [P_CelluleController::class, 'index']);
            Route::post('add', [P_CelluleController::class, 'add']);
        });

        Route::prefix('type-article')->group(function () {
            Route::post('getAll', [E_TypeArticleController::class, 'index']);
            Route::post('add', [E_TypeArticleController::class, 'add']);
        });

        Route::prefix('prefixe')->group(function () {
            Route::post('getAll', [P_PrefixeController::class, 'index']);
            Route::post('add', [P_PrefixeController::class, 'add']);
        });

        Route::prefix('suivi-stock')->group(function () {
            Route::post('getAll', [E_SuiviStockController::class, 'index']);
        });

        Route::prefix('station')->group(function () {
            Route::post('getAll', [P_StationController::class, 'index']);
            Route::post('getAllStationActive', [P_StationController::class, 'getAllStationActive']);
        });

        Route::prefix('fournisseur')->group(function () {
            Route::post('getAll', [FournisseurController::class, 'getFournisseurs']);
            Route::post('add', [FournisseurController::class, 'add']);
        });

        Route::prefix('taxe')->group(function () {
            Route::post('getAll', [P_taxesController::class, 'index']);
        });

        Route::prefix('images')->group(function () {
            Route::post('upload', [BaseController::class, 'uploadImage']);
        });

        Route::prefix('etat-tier')->group(function () {
            Route::post('getAll', [E_EtatTierController::class, 'index']);
        });

        Route::prefix('inventaire')->group(function () {
            Route::post('getInventaires', [M_InventaireController::class, 'getInventaires']);
            Route::post('add', [M_InventaireController::class, 'addParentAndChildren']);
            Route::post('getEtatInventaire', [M_InventaireController::class, 'getEtatInventaire']);
            //Route::post('addBatchInventairesWithLines', [M_InventaireController::class, 'addBatchInventairesWithLines']);
        });

        Route::prefix('ligne-bonentree')->group(function () {
            Route::post('getLigneBonEntrees', [LigneBonEntreeController::class, 'getDocumentsOuLignes']);
        });

        Route::prefix('ligne-bon-transfert')->group(function () {
            Route::post('getLignes', [LigneBonTransfertController::class, 'getDocumentsOuLignes']);
        });

        Route::prefix('ligne-bonlivraison')->group(function () {
            Route::post('getLigneBonLivraison', [LigneBonLivraisonController::class, 'getDocumentsOuLignes']);
        });

        Route::prefix('ticket-rayon')->group(function () {
            Route::post('add', [TicketRayonController::class, 'add']);
        });

        Route::prefix('bon-entree')->group(function () {
            Route::post('getBonEntrees', [BonEntreeController::class, 'getDocumentsOuLignes']);
            Route::post('addBatchBonEntrees', [BonEntreeController::class, 'add']);
            Route::post('addBatchBonEntreesWithLines', [BonEntreeController::class, 'addParentAndChildren']);
        });

        Route::prefix('bon-livraison')->group(function () {
            Route::post('getBonLivraison', [BonLivraisonController::class, 'getDocumentsOuLignes']);
            Route::post('addBatchBonLivraisonWithLines', [BonLivraisonController::class, 'addParentAndChildren']);
        });

        Route::prefix('bon-transfert')->group(function () {
            Route::post('getBonTransfert', [BonTransfertController::class, 'getDocumentsOuLignes']);
            Route::post('addBatchBonTransfertWithLines', [BonTransfertController::class, 'addParentAndChildren']);
        });

        Route::prefix('ligne-inventaire')->group(function () {
            Route::post('getLigneInventaires/{idInventaire}', [L_LigneInventaireController::class, 'getLigneInventaires']);
        });

        Route::prefix('utilisateur')->group(function () {
            Route::post('authentification', [P_UtilisateurController::class, 'authentification']);
            Route::post('getUtilisateurs', [P_UtilisateurController::class, 'getUtilisateurs']);
            Route::post('getUsersTechnicien', [P_UtilisateurController::class, 'getUsersTechnicien']);
        });

        Route::prefix('tva')->group(function () {
            Route::post('getTVA', [P_TvaController::class, 'index']);
        });

        Route::prefix('exercice')->group(function () {
            Route::post('getExercice', [P_ExerciceController::class, 'getExercice']);
        });

        Route::prefix('unite-article')->group(function () {
            Route::post('getUniteArticles', [P_Article_UniteController::class, 'getUniteArticles']);
        });

        Route::prefix('article')->group(function () {
            Route::post('getArticles', [P_ArticleController::class, 'getArticles']);
            Route::post('add', [P_ArticleController::class, 'add']);
            Route::post('getCountArticle', [P_ArticleController::class, 'getCountArticle']);
            Route::post('/etatStock', [P_ArticleController::class, 'etatStock']);
            Route::post('/allStockArticle', [P_ArticleController::class, 'allStockArticle']);
            Route::post('/allQteTheoriqueArticle', [P_ArticleController::class, 'allQteTheoriqueArticle']);
        });
        Route::prefix('E_TypeUser')->group(function () {
            Route::post('getTypeUser', [E_TypeUserController::class, 'index']);
        });

        Route::prefix('tier')->group(function () {
            Route::post('findByTypeTier/{idTypeTier}', [P_tierController::class, 'findByTypeTier']);
            Route::post('getAllTierApporteur', [P_tierController::class, 'getAllTierApporteur']);
        });
        Route::prefix('EtatDocument')->group(function () {
            Route::post('getAll', [E_EtatDocumentController::class, 'index']);

        });
        Route::prefix('P_Devise')->group(function () {
            Route::post('getAll', [P_DeviseController::class, 'index']);

        });
    });


});
