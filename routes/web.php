<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
 */


use App\Helpers\DB;
use App\Http\Controllers\SessionCaisseWSMobile;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

require_once __DIR__ . '/dux-inventory.php';

Route::get('/', function () {
    return view('welcome');
});

Route::get('/menu', function () {


    if (!Schema::hasTable('settings_mobile')) {
        Schema::create('settings_mobile', function (Blueprint $table) {
            $table->bigInteger('id')->primary();
            $table->string('name');
            $table->string('type');
            $table->boolean('isActive')->default(true);
            $table->timestamps();
        });

        DB::table('settings_mobile')->insert([
            'id' => **********,
            'name' => 'inventory_drawer_item',
            'type' => 'main_menu',
            'isActive' => true,
        ]);

        DB::table('settings_mobile')->insert([
            'id' => 1201584442,
            'name' => 'transfert_drawer_item',
            'type' => 'main_menu',
            'isActive' => true,
        ]);

        DB::table('settings_mobile')->insert([
            'id' => 1306420261,
            'name' => 'purchase_drawer_item',
            'type' => 'main_menu',
            'isActive' => true,
        ]);

        DB::table('settings_mobile')->insert([
            'id' => 1402573242,
            'name' => 'scanner_drawer_item',
            'type' => 'main_menu',
            'isActive' => true,
        ]);

        DB::table('settings_mobile')->insert([
            'id' => 1503142697,
            'name' => 'article_drawer_item',
            'type' => 'main_menu',
            'isActive' => true,
        ]);

        DB::table('settings_mobile')->insert([
            'id' => 1604405923,
            'name' => 'ticket_rayon_drawer_item',
            'type' => 'main_menu',
            'isActive' => true,
        ]);
    }

    return response()->json(DB::table('settings_mobile')->where('isActive', true)->where('type', 'main_menu')->orderBy('id')->get());
});

/*
Route::get('/menu', function () {
   echo 'article_drawer_item    '.hexdec(bin2hex('article_drawer_item'));

});*/


Route::get('/records', function () {
    return view('filemanager');
});

Route::get('/', 'HomeController@index');


Route::get('logs', '\Rap2hpoutre\LaravelLogViewer\LogViewerController@index')->name('logs');

Route::group(['middleware' => 'record'], function () {

    Route::post('Client/addBatchClient', array('as' => 'addBatchClient', 'uses' => 'ClientWSMobile@addBatchClient'));
    Route::post('Ticket/addBatchTicketWithLignesTicketAndPayment', array('as' => 'addBatchTicketWithLignesTicketAndPayment', 'uses' => 'TicketWSMobile@addBatchTicketWithLignesTicketAndPayment'));

    Route::post('BonLivraison/addBatchBonLivraison', 'BonLivraisonController@addBatchBonLivraison');
    Route::post('BonEntree/addBatchBonEntreesWithLines', 'BonEntreeWSMobile@addBatchBonEntreesWithLines');
    Route::post('ReglementCaisse/addBatchPayments', 'ReglementCaisseWSMobile@addBatchPayments');
    Route::post('LigneCommande/addBatchLigneCommande', 'LigneBonCommandeWSMobile@addBatchLigneBonCommande');
    Route::post('LigneCommande/addBatchLigneBonCommandeTest', 'LigneBonCommandeWSMobile@addBatchLigneBonCommandeTest');
    Route::post('LigneRetour/addBatchLigneRetour', 'LigneBonRetourWSMobile@addBatchLigneBonRetour');
    Route::post('Commande/addBatchBonCommande', 'BonCommandeWSMobile@addBatchBonCommande');
    Route::post('Retour/addBatchBonRetour', 'BonRetourWSMobile@addBatchBonRetour');
    Route::post('BonEntree/addBatchBonEntrees', 'BonEntreeWSMobile@addBatchBonEntrees');
    Route::post('LigneBonEntree/addBatchLigneBonEntrees', 'LigneBonEntreeWSMobile@addBatchLigneBonEntrees');
    Route::post('facture/addBatchFactureWithLines', 'FactureMobileController@addBatchFactureWithLines');
    Route::post('LigneBonLivraison/addBatchLigneBonLivraison', 'LigneBonLivraisonController@addBatchLigneBonLivraison');
    Route::post('LigneBonLivraison/addBatchLigneBonLivraisonMobile', 'LigneBonLivraisonController@addBatchLigneBonLivraisonMobile');

    Route::post('BonLivraison/addBatchBonLivraisonWithLines', 'BonLivraisonController@addBatchBonLivraisonWithLines');

    Route::post('Inventaire/addBatchInventaires', 'InventaireWSMobile@addBatchInventaires');
    Route::post('Inventaire/addBatchInventairesWithLines', 'InventaireWSMobile@addBatchInventairesWithLines');

    Route::post('LigneInventaire/addBatchLigneInventaires', 'LigneInventaireWSMobile@addBatchLigneInventaires');
    Route::post('LigneInventaire/addBatchInventairesWithLines', 'addBatchInventairesWithLines@addBatchLigneInventaires');

    Route::post('OrdreMission/AddOrdreMission', 'OrdreMissionController@AddOrdreMission');

    Route::post('VConcu/addBatchDataVConcu', array('as' => 'addBatchDataVConcu', 'uses' => 'VConcuController@addBatchDataVConcu'));



    Route::any('test1', function () {
        return DB::table('article')->get();
    });

    Route::any('test', function () {
        DB::table('test')->insert(["test" => uniqid()]);
    });

});

//Table Couleur

Route::get('Couleur/getCouleurs', 'CouleurWSMobile@getCouleur');

Route::post('Couleur/getCouleurByCode', array('as' => 'getCouleurByCode', 'uses' => 'CouleurWSMobile@getCouleurByCode'));

Route::post('Couleur/getCouleurByX', array('as' => 'getCouleurByX', 'uses' => 'CouleurWSMobile@getCouleurByX'));

Route::post('Couleur/addCouleur', array('as' => 'addCouleur', 'uses' => 'CouleurWSMobile@addCouleur'));

Route::put('Couleur/updateCouleur', array('as' => 'updateCouleur', 'uses' => 'CouleurWSMobile@updateCouleur'));

Route::delete('Couleur/deleteCouleur', array('as' => 'deleteCouleur', 'uses' => 'CouleurWSMobile@deleteCouleur'));

//Table article_code_bar

Route::post('Article/getCodeTest', 'ArticleWSMobile@getCodeTest');
Route::post('Article/getCodeBarPagination', 'ArticleWSMobile@getCodeBarPagination');

Route::post('Article/addArticleCodeBarMobile', 'ArticleCodeBarWSMobile@addArticleCodeBarMobile');

Route::post('Article/getArticleClientPrix', 'ArticleWSMobile@getArticleClientPrix');

Route::get('Article_Code_bar/getArticle_Code_bar', 'Article_Code_barWSMobile@getArticle_Code_bar');

Route::post('Article_Code_bar/getArticle_Code_barByCode', array('as' => 'getArticle_Code_barByCode', 'uses' => 'Article_Code_barWSMobile@getArticle_Code_barByCode'));

Route::get('ArticleCodebar/getArticleCodeBar', 'ArticleCodeBarWSMobile@getArticleCodeBar');

Route::post('ArticleCodebar/getArticleCodeBarByCode', array('as' => 'getArticleCodeBarByCode', 'uses' => 'Article_Code_barWSMobile@getArticleCodeBarByCode'));

Route::post('Article/getArticleByBarCode', array('as' => 'getArticleByBarCode', 'uses' => 'ArticleWSMobile@getArticleByBarCode'));
Route::post('Article/getArticleByName', array('as' => 'getArticleByName', 'uses' => 'ArticleWSMobile@getArticleByName'));

Route::post('Article_Code_bar/getArticle_Code_barByX', array('as' => 'getArticle_Code_barByX', 'uses' => 'Article_Code_barWSMobile@getArticle_Code_barByX'));

Route::post('Article_Code_bar/addArticle_Code_bar', array('as' => 'addArticle_Code_bar', 'uses' => 'Article_Code_barWSMobile@addArticle_Code_bar'));

Route::put('Article_Code_bar/updateArticle_Code_bar', array('as' => 'updateArticle_Code_bar', 'uses' => 'Article_Code_barWSMobile@updateArticle_Code_bar'));

Route::delete('Article_Code_bar/deleteArticle_Code_bar', array('as' => 'deleteArticle_Code_bar', 'uses' => 'Article_Code_barWSMobile@deleteArticle_Code_bar'));

//Table article_classe_remise
Route::post('ArticleClasseRemise/getRemises', 'ArticleClasseRemiseController@getArticleClasseRemise');

Route::post('ArticleClasseRemise/addArticleClasseRemiseMobile', 'ArticleClasseRemiseController@addArticleClasseRemiseMobile');

//Table taille
Route::post('Taille/getTailles', 'TailleController@getTailles');

Route::post('Taille/addTailleMobile', 'TailleController@addTailleMobile');
//Table marque
Route::post('Marque/getMarques', 'MarqueController@getMarques');

Route::post('Marque/addMarqueMobile', 'MarqueController@addMarqueMobile');

//Table couleur
Route::post('Couleur/getCouleurs', 'CouleurController@getCouleurs');

Route::post('Couleur/addCouleurMobile', 'CouleurController@addCouleurMobile');
//Table classe remise
Route::post('ClasseRemise/getClasseRemises', 'ClasseRemiseController@getClasseRemises');

Route::post('ClasseRemise/addclasseRemiseMobile', 'ClasseRemiseController@addclasseRemiseMobile');
//Table Unite
Route::post('Unite/getUnites', 'UniteController@getUnites');


Route::post('Unite/addUniteMobile', 'UniteController@addUniteMobile');
//Table Unite article
Route::post('UniteArticle/getUniteArticles', 'UniteArticleController@getUniteArticles');
Route::post('UniteArticle/getUniteArticlesPagination', 'UniteArticleController@getUniteArticlesPagination');

Route::post('UniteArticle/addUniteArticleMobile', 'UniteArticleController@addUniteArticleMobile');

//Table article_code_bar

Route::get('Taille/getTaille', 'TailleWSMobile@getTaille');

Route::post('Taille/getTailleByCode', array('as' => 'getTailleByCode', 'uses' => 'TailleWSMobile@getTailleByCode'));

Route::post('Taille/getTailleByX', array('as' => 'getTailleByX', 'uses' => 'TailleWSMobile@getTailleByX'));

Route::post('Taille/addTaille', array('as' => 'addTaille', 'uses' => 'TailleWSMobile@addTaille'));

Route::put('Taille/updateTaille', array('as' => 'updateTaille', 'uses' => 'TailleWSMobile@updateTaille'));

Route::delete('Taille/deleteTaille', array('as' => 'deleteTaille', 'uses' => 'TailleWSMobile@deleteTaille'));

//Table marque

Route::post('Marque/getMarque', 'MarqueWSMobile@getMarque');

//Route::post('Marque/addMarqueMobile','MarqueWSMobile@addMarqueMobile');

Route::post('Marque/getMarqueByCode', array('as' => 'getMarqueByCode', 'uses' => 'MarqueWSMobile@getMarqueByCode'));

Route::post('Marque/getMarqueByX', array('as' => 'getMarqueByX', 'uses' => 'MarqueWSMobile@getMarqueByX'));

Route::post('Marque/addMarque', array('as' => 'addMarque', 'uses' => 'MarqueWSMobile@addMarque'));

Route::put('/updateMarque', array('as' => 'updateMarque', 'uses' => 'MarqueWSMobile@updateMarque'));

Route::delete('/deleteMarque', array('as' => 'deleteMarque', 'uses' => 'MarqueWSMobile@deleteMarque'));

//Table famille

Route::post('Famille/getFamilles', 'FamilleWSMobile@getFamille');

Route::post('Famille/addFamilleMobile', 'FamilleWSMobile@addFamilleMobile');

Route::post('Famille/getFamilleByCode', array('as' => 'getFamilleByCode', 'uses' => 'FamilleWSMobile@getFamilleByCode'));

Route::post('Famille/getFamilleByX', array('as' => 'getFamilleByX', 'uses' => 'FamilleWSMobile@getFamilleByX'));

Route::post('Famille/addFamille', array('as' => 'addFamille', 'uses' => 'FamilleWSMobile@addFamille'));

Route::put('Famille/updateFamille', array('as' => 'updateFamille', 'uses' => 'FamilleWSMobile@updateFamille'));

Route::delete('Famille/deleteFamille', array('as' => 'deleteFamille', 'uses' => 'FamilleWSMobile@deleteFamille'));

//Table Unite_article

Route::post('Unite_article/getUnite_article', 'Unite_articleWSMobile@getUnite_article');

Route::post('Unite_article/getUnite_articleByCode', array('as' => 'getUnite_articleByCode', 'uses' => 'Unite_articleWSMobile@getUnite_articleByCode'));

Route::post('Unite_article/getUnite_articleByX', array('as' => 'getUnite_articleByX', 'uses' => 'Unite_articleWSMobile@getUnite_articleByX'));

Route::post('Unite_article/addUnite_article', array('as' => 'addUnite_article', 'uses' => 'Unite_articleWSMobile@addUnite_article'));

Route::put('Unite_article/updateUnite_article', array('as' => 'updateUnite_article', 'uses' => 'Unite_articleWSMobile@updateUnite_article'));

Route::delete('Unite_article/deleteUnite_article', array('as' => 'deleteUnite_article', 'uses' => 'Unite_articleWSMobile@deleteUnite_article'));

//Table TVA

Route::post('TVA/getTVA', 'TVAWSMobile@getTVA');

Route::post('TVA/getTVAByCode', array('as' => 'getTVAByCode', 'uses' => 'TVAWSMobile@getTVAByCode'));

Route::post('TVA/getTVAByX', array('as' => 'getTVAByX', 'uses' => 'TVAWSMobile@getTVAByX'));

Route::post('TVA/addTVA', array('as' => 'addTVA', 'uses' => 'TVAWSMobile@addTVA'));

Route::put('TVA/updateTVA', array('as' => 'updateTVA', 'uses' => 'TVAWSMobile@updateTVA'));

Route::delete('TVA/deleteTVA', array('as' => 'deleteTVA', 'uses' => 'TVAWSMobile@deleteTVA'));

//Table Unite

Route::get('Unite/getUnite', 'UniteWSMobile@getUnite');

Route::post('Unite/getUniteByCode', array('as' => 'getUniteByCode', 'uses' => 'UniteWSMobile@getUniteByCode'));

Route::post('Unite/getUniteByX', array('as' => 'getUniteByX', 'uses' => 'UniteWSMobile@getUniteByX'));

Route::post('Unite/addUnite', array('as' => 'addUnite', 'uses' => 'UniteWSMobile@addUnite'));

Route::put('Unite/updateUnite', array('as' => 'updateUnite', 'uses' => 'UniteWSMobile@updateUnite'));

Route::delete('Unite/deleteUnite', array('as' => 'deleteUnite', 'uses' => 'UniteWSMobile@deleteUnite'));

//Table Article

Route::post('Article/getArticles', 'ArticleWSMobile@getArticle');
Route::post('Article/getArticleWithPagination', 'ArticleWSMobile@getArticleWithPagination');

Route::post('Article/getCountArticle', 'ArticleWSMobile@getCountArticle');


Route::post('Article/getAll', 'ArticleWSMobile@getArticle');

Route::post('Article/ArticleWithTransaction', 'ArticleWSMobile@ArticleWithTransaction');

Route::post('Article/getArticlesMobile', 'ArticleWSMobile@getArticles');

Route::post('Article/addArticleMobile', 'ArticleWSMobile@addArticleMobile');

Route::post('Article/getArticlesByUser', array('as' => 'getArticlesByUser', 'uses' => 'ArticleWSMobile@getArticlesByUser'));

Route::post('Article/getArticlesByStation', 'ArticleWSMobile@getArticleByStation');

Route::post('Visite/file/store', 'PhotoController@store'); //new for arem
Route::post('Visite/file/select', 'PhotoController@getVisitePhoto'); //new for arem
Route::post('Visite/file/insert', 'PhotoController@addStationPhoto'); //new for arem

Route::post('Article/getArticleByCode', array('as' => 'getArticleByCode', 'uses' => 'ArticleWSMobile@getArticleByCode'));

Route::post('Article/getArticleByX', array('as' => 'getArticleByX', 'uses' => 'ArticleWSMobile@getArticleByX'));

Route::post('Article/addArticle', array('as' => 'addArticle', 'uses' => 'ArticleWSMobile@addArticle'));

Route::post('Article/addArticle', array('as' => 'addArticle', 'uses' => 'ArticleWSMobile@addArticleMobile'));

Route::put('Article/updateArticle', array('as' => 'updateArticle', 'uses' => 'ArticleWSMobile@updateArticle'));

Route::delete('Article/deleteArticle', array('as' => 'deleteArticle', 'uses' => 'ArticleWSMobile@deleteArticle'));

Route::post('Article/addarticlev2', array('as' => 'addArticle', 'uses' => 'ArticleWSMobile@addArticleMobileV2'));

Route::post('Article/getArticleFamille', array('as' => 'getArticleFamille', 'uses' => 'ArticleWSMobile@getArticleFamille'));

//Table Client

Route::post('Client/getClients', 'ClientWSMobile@getClient');

Route::post('Client/getClientByCode', array('as' => 'getClientByCode', 'uses' => 'ClientWSMobile@getClientByCode'));

Route::post('Client/getClientByX', array('as' => 'getClientByX', 'uses' => 'ClientWSMobile@getClientByX'));

Route::post('Client/addClient', array('as' => 'addClient', 'uses' => 'ClientWSMobile@addClient'));

Route::put('Client/updateClient', array('as' => 'updateClient', 'uses' => 'ClientWSMobile@updateClient'));

Route::delete('Client/deleteClient', array('as' => 'deleteClient', 'uses' => 'ClientWSMobile@deleteClient'));


//Table Utilisateur

Route::post('Utilisateur/getUtilisateurs', 'UtilisateurWSMobile@getUtilisateur');

Route::post('Utilisateur/Authentification', 'UtilisateurWSMobile@Authentification');
Route::post('Utilisateur/insertUserToken', 'UtilisateurWSMobile@insertUserToken');

//Table SessionCaisse

Route::post('SessionCaisse/getSessionCaisses', 'SessionCaisseWSMobile@getSessionCaisse');

Route::post('SessionCaisse/getSessionCaisseByDate', 'SessionCaisseWSMobile@getSessionCaisseByDate');

Route::post('SessionCaisse/getSessionCaisseByX', array('as' => 'getSessionCaisseByX', 'uses' => 'SessionCaisseWSMobile@getSessionCaisseByX'));

Route::post('SessionCaisse/addSessionCaisse', array('as' => 'addSessionCaisse', 'uses' => 'SessionCaisseWSMobile@addSessionCaisse'));

Route::post('SessionCaisse/addSessionVendeur', array('as' => 'addSessionVendeur', 'uses' => 'SessionCaisseWSMobile@addSessionVendeur'));

Route::post('SessionCaisse/closeSessionVendeur', array('as' => 'closeSessionVendeur', 'uses' => 'SessionCaisseWSMobile@closeSessionVendeur'));

Route::post('Consultation/getRecapVente', 'CaisseWSMobile@getRecapVente');

Route::post('SessionCaisse/getSessionCaisseByUser', array('as' => 'getSessionCaisseByUser', 'uses' => 'SessionCaisseWSMobile@getSessionCaisseByUser'));

Route::put('SessionCaisse/updateSessionCaisse', array('as' => 'updateSessionCaisse', 'uses' => 'SessionCaisseWSMobile@updateSessionCaisse'));

Route::delete('/deleteSessionCaisse', array('as' => 'deleteSessionCaisse', 'uses' => 'SessionCaisseWSMobile@deleteSessionCaisse'));

Route::post('SessionCaisse/addSession',[SessionCaisseWSMobile::class,"addSession"]);

//Table Caisse
Route::post('Caisse/getCaisses', 'CaisseWSMobile@getCaisse');


Route::post('Caisse/getCaisseByX', array('as' => 'getCaisseByX', 'uses' => 'CaisseWSMobile@getCaisseByX'));

Route::post('Caisse/addCaisse', array('as' => 'addCaisse', 'uses' => 'CaisseWSMobile@addCaisse'));

Route::post('calculsommevente', array('as' => 'addCaisse', 'uses' => 'CaisseWSMobile@CalculSommeVente'));

Route::post('CalculSommeTicket', array('as' => 'addCaisse', 'uses' => 'CaisseWSMobile@CalculSommeTicket'));

//Table Tiket

Route::post('Ticket/getTickets', 'TicketWSMobile@getTicket');

Route::post('Ticket/getCAparMarque', 'TicketWSMobile@getCAparMarque');

Route::post('Ticket/getCAparFamille', 'TicketWSMobile@getCAparFamille');
Route::post('Ticket/getCAparFournisseur', 'TicketWSMobile@getCAparFournisseur');
Route::post('Ticket/getCAparClient', 'TicketWSMobile@getCAparClient');
Route::post('Ticket/getMaxNumTicket', 'TicketWSMobile@getMaxNumTicket');


Route::post('Ticket/getCAparArticle', 'TicketWSMobile@getCAparArticle');

Route::post('Ticket/getTicketByX', array('as' => 'getTicketByX', 'uses' => 'TicketWSMobile@getTicketByX'));

Route::post('Ticket/addTicketWithLignesTicket', array('as' => 'addTicketWithLignesTicket', 'uses' => 'TicketWSMobile@addTicketWithLignesTicket'));

Route::post('Ticket/addTicketWithLignesTicketAndPayment', array('as' => 'addTicketWithLignesTicketAndPayment', 'uses' => 'TicketWSMobile@addTicketWithLignesTicketAndPayment'));


Route::post('Ticket/getTicketsByCaisseId', array('as' => 'getTicketsByCaisseId', 'uses' => 'TicketWSMobile@getTicketsByCaisseId'));

Route::post('Ticket/addTicket', array('as' => 'addTicket', 'uses' => 'TicketWSMobile@addTicket'));

Route::delete('Ticket/deleteTicket', array('as' => 'deleteTicket', 'uses' => 'TicketWSMobile@deleteTicket'));

Route::put('Ticket/updateTicket', array('as' => 'updateTicket', 'uses' => 'TicketWSMobile@updateTicket'));

//Table LigneTicket
Route::post('LigneTicket/getLigneTicketByTickets', 'LigneTicketWSMobile@getLigneTicketByTickets');

Route::post('LigneTicket/getLigneTicketByCarnetId', 'LigneTicketWSMobile@getLigneTicketByCarnetId');

Route::post('LigneTicket/getLigneTicketByTicket', 'LigneTicketWSMobile@getLigneTicketByTicket');

Route::post('LigneTicket/getLigneTickets', 'LigneTicketWSMobile@getLigneTicket');

Route::post('LigneTicket/getLigneTicketByX', array('as' => 'getLigneTicketByX', 'uses' => 'LigneTicketWSMobile@getLigneTicketByX'));

Route::post('LigneTicket/addLigneTicket', array('as' => 'addLigneTicket', 'uses' => 'LigneTicketWSMobile@addLigneTicket'));

Route::delete('LigneTicket/deleteLigneTicket', array('as' => 'deleteLigneTicket', 'uses' => 'LigneTicketWSMobile@deleteLigneTicket'));

Route::put('LigneTicket/updateLigneTicket', array('as' => 'updateLigneTicket', 'uses' => 'LigneTicketWSMobile@updateLigneTicket'));

//Table devise

Route::post('Devise/getDevises', 'DeviseWSMobile@getDevise');

Route::post('Devise/getDeviseByX', array('as' => 'getDeviseByX', 'uses' => 'DeviseWSMobile@getDeviseByX'));

Route::post('Devise/getDeviseByActive', array('as' => 'getDeviseByActive', 'uses' => 'DeviseWSMobile@getDeviseByActive'));

//Table exercice

Route::post('Exercice/getExercice', 'ExerciceWSMobile@getExercice');


Route::post('Price/getPricesByStation', 'PriceController@getPricesByStation');


//Table retour

Route::post('Retour/getRetour', 'BonRetourWSMobile@getBonRetour');
Route::post('Retour/getRetours', 'BonLivraisonController@getBonRetour');

//Table commande

Route::post('Commande/getCommande', 'BonCommandeWSMobile@getBonCommande');
Route::post('Commande/getBonCommandeWithPagination', 'BonCommandeWSMobile@getBonCommandeWithPagination');

//Table Ligne Retour

Route::post('LigneRetour/getLigneRetour', 'LigneBonRetourWSMobile@getLigneBonRetour');

//Table Ligne Commande

Route::post('LigneCommande/getLigneCommande', 'LigneBonCommandeWSMobile@getLigneBonCommande');

//Table ReglementCaisse
Route::post('ReglementCaisse/getReglementCaisseByTickets', 'ReglementCaisseWSMobile@getReglementCaisseByTickets');

Route::post('ReglementCaisse/getReglementCaisseByTicket', 'ReglementCaisseWSMobile@getReglementCaisseByTicket');


Route::post('ReglementCaisse/getReglementCaisseBySession', 'ReglementCaisseWSMobile@getReglementCaisseBySession');

//Table ChequeCaisse
Route::post('ChequeCaisse/getChequeCaisseByReglements', 'ChequeCaisseWSMobile@getChequeCaisseByReglements');

Route::post('ChequeCaisse/getChequeCaisseByReglement', 'ChequeCaisseWSMobile@getChequeCaisseByReglement');

//Table TraiteCaisse
Route::post('TraiteCaisse/getTraiteCaisseByReglements', 'TraiteCaisseWSMobile@getTraiteCaisseByReglements');

Route::post('TraiteCaisse/getTraiteCaisseByReglement', 'TraiteCaisseWSMobile@getTraiteCaisseByReglement');

//Table banque

Route::post('Banque/getBanques', 'BanqueWSMobile@getBanque');

//Table prefixe

Route::post('Prefixe/getPrefixes', 'PrefixWSMobile@getPrefixes');
//Table carte resto

Route::post('CarteResto/getCartesResto', 'CarteRestoWSMobile@getCarteResto');

Route::post('CarteResto/getCarteRestoByCode', 'CarteRestoWSMobile@getCarteRestoByCode');

Route::post('Statistics/getReglementStatistics', 'MiscWSMobile@getReglementStatistics');

Route::post('Statistics/getVenteStatistics', 'MiscWSMobile@getVenteStatistics');

Route::post('Statistics/getTopNClients', 'MiscWSMobile@getTopNClients');

Route::post('Carnet/getCarnetByID', 'CarnetWSMobile@getCarnetByID');

Route::post('Miscs/Statistics/getStatistics', 'MiscWSMobile@getStatistics');


Route::post('Miscs/ZIP/downloadZip', 'MiscWSMobile@downloadZip');


//Table Visite

Route::post('Visite/getVisits', 'VisiteWSMobile@getVisite');

Route::post('Visite/getVisitsByCaisseId', 'VisiteWSMobile@getVisitesByCaisseId');

Route::post('Visite/addVisiteWithLignesVisite', 'VisiteWSMobile@addVisiteWithLignesVisite');

Route::post('Visite/updateTicket', 'VisiteWSMobile@updateVisite');

//Table LigneVisite
Route::post('LigneVisite/getLigneVisiteByVisites', 'LigneVisiteWSMobile@getLigneVisiteByVisites');

Route::post('LigneVisite/getLigneVisiteByCarnetId', 'LigneVisiteWSMobile@getLigneVisiteByCarnetId');

Route::post('LigneVisite/getLigneTicketByTicket', 'LigneTicketWSMobile@getLigneVisiteByVisite');

Route::post('LigneVisite/getLigneVisites', 'LigneVisiteWSMobile@getLigneVisite');

Route::post('LigneVisite/addLigneVisite', array('as' => 'addLigneVisite', 'uses' => 'LigneVisiteWSMobile@addLigneVisite'));

Route::delete('LigneVisite/deleteLigneVisite', array('as' => 'deleteLigneVisite', 'uses' => 'LigneVisiteWSMobile@deleteLigneVisite'));

Route::put('LigneVisite/updateLigneVisite', array('as' => 'updateLigneVisite', 'uses' => 'LigneVisiteWSMobile@updateLigneVisite'));

//Table Etablisement

Route::post('Etablisement/getEtablisements', 'EtablisementWSMobile@getEtablisements');

//Table Station

Route::post('Station/getStations', 'StationWSMobile@getStations');
Route::post('Station/getStationByCode', 'StationWSMobile@getStationByCode');

Route::post('Station/getClientStations', 'StationWSMobile@getClientStations');

Route::post('Station/getClientStationsByUser', 'StationWSMobile@getClientStationsByUser');
Route::post('Station/getstockArticleByStation', 'StationWSMobile@getstockArticleByStation');
Route::post('Station/getstockArticle', 'StationArticleController@getstockArticle');
Route::post('Station/getstockArticlePagination', 'StationArticleController@getstockArticlePagination');

//Table bon entree
Route::post('BonEntree/getBonEntrees', 'BonEntreeWSMobile@getBonEntrees');


Route::post('LigneBonEntree/getLigneBonEntrees', 'LigneBonEntreeWSMobile@getLigneBonEntrees');
Route::post('LigneBonEntree/updateBatchLigneBonEntrees', 'LigneBonEntreeWSMobile@updateBatchLigneBonEntrees');
Route::post('LigneBonEntree/deleteBatchLigneBonEntrees', 'LigneBonEntreeWSMobile@deleteBatchLigneBonEntrees');
Route::post('LigneBonEntree/test', 'LigneBonEntreeWSMobile@test');


// Table Facture
Route::post('facture/getFacture', 'FactureMobileController@getFacture');


//Table bon transfer
Route::post('BonLivraison/getBonLivraison', 'BonLivraisonController@getBonLivraison');


Route::post('LigneBonLivraison/getLigneBonLivraison', 'LigneBonLivraisonController@getLigneBonLivraison');

Route::post('LigneBonLivraison/updateBatchLigneBonLivraison', 'LigneBonLivraisonController@updateBatchLigneBonLivraison');
Route::post('LigneBonLivraison/deleteBatchLigneBonLivraison', 'LigneBonLivraisonController@deleteBatchLigneBonLivraison');

//Table inventaire
Route::post('Inventaire/getInventaires', 'InventaireWSMobile@getInventaires');

//Table inventaire
Route::post('LigneInventaire/getLigneInventaires/{LG_INV_Code_Inv}', 'LigneInventaireWSMobile@getLigneInventaires');

Route::post('LigneInventaire/updateBatchLigneInventaires', 'LigneInventaireWSMobile@updateBatchLigneInventaires');
Route::post('LigneInventaire/deleteBatchLigneInventaires', 'LigneInventaireWSMobile@deleteBatchLigneInventaires');
Route::post('LigneBonEntree/test', 'LigneBonEntreeWSMobile@test');
//Table fournisseur
Route::post('Fournisseur/getFournisseurs', 'FournisseurWSMobile@getFournisseurs');

// Table Recette
Route::post('Recette/getRecetteByReglement', 'RecetteWSMobile@getRecetteByReglement');

Route::post('Recette/getDetailRecette', 'RecetteWSMobile@getDetailRecette');

Route::post('Recette/getNbrePassageParDate', 'RecetteWSMobile@getNbrePassageParDate');

// Table Depense

Route::post('Depense/getDepenseByDay', 'DepenseWSMobile@getDepenseByDay');
Route::post('Depense/getTypeDepense', 'DepenseWSMobile@getTypeDepence');
Route::post('Depense/addBatchDepense', 'DepenseWSMobile@addBatchDepense');
Route::post('Depense/getAllDepense', 'DepenseWSMobile@getAllDepense');
Route::post('Depense/deleteDepense', 'DepenseWSMobile@deleteDepense');
Route::post('Depense/addBatchTypeDepense', 'DepenseWSMobile@addBatchTypeDepense');
Route::post('Depense/getDepenseByIdCaisse', 'DepenseWSMobile@getDepenseByIdCaisse');


// Table RecapVente

Route::post('RecapVente/getRecapVente', 'RecapVenteController@getRecapVente');



// Table Reclamation

Route::post('Reclamation/getReclamations', 'ReclamationWSMobile@getReclamation');

Route::post('Reclamation/addBatchReclamation', 'ReclamationWSMobile@addBatchReclamation');

Route::post('Reclamation/uploadReclamationFile', 'ReclamationWSMobile@uploadReclamationFile');

//Table parametrage

Route::post('Parametrage/getParametrage', 'ParametrageWSMobile@getParametrage');

Route::post('Parametrage/getAuthorisation', 'ParametrageWSMobile@getAuthorisation');

Route::post('StationArticle/getCount', 'StationArticleController@getArticleCountInStationOUt');

// Table Type_PrixUnitaireHT
Route::post('TypePrixUnitaire/getTypePrixUnitaireHT', 'TypePrixUnitaireController@getTypePrixUnitaireHT');


// Table Ticket_Rayon
Route::post('TicketRayon/addTicketRayon', 'TicketRayonController@addTicketRayon');
Route::post('Article/addarticlev3', array('as' => 'addArticle', 'uses' => 'ArticleWSMobile@addArticleMobileV3'));


Route::post('displaydetailvente', 'CaisseWSMobile@DisplayDetailVente');
Route::post('calculsommeventev1ByStation', 'CaisseWSMobile@CalculSommeVentev1ByStation');
Route::post('displaytotalbycaissier', 'CaisseWSMobile@DisplayTotalByCaissier');

Route::post('Consultation/calcultotalremiseHT', 'CaisseWSMobile@CalculTotalRemiseHT');

Route::post('Consultation/displaynombreticket', 'TicketWSMobile@DisplayNombreTicket');
Route::post('Consultation/displaynombreticketbycaissier', 'TicketWSMobile@DisplayNombreTicketByCaissier');
Route::post('Consultation/displaydetailarticlesupprimee', 'TicketWSMobile@DisplayDetailArticleSupprimee');
Route::post('Consultation/displaydetailarticlesupprimeebycaissier', 'TicketWSMobile@DisplayDetailArticleSupprimeeByCaissier');
Route::post('Consultation/displaydetailticketvalidee', 'TicketWSMobile@DisplayDetailTicketValidee');
Route::post('Consultation/displaydetailticketannule', 'TicketWSMobile@DisplayDetailTicketAnnule');
Route::post('Consultation/getcaissier', 'UtilisateurWSMobile@getCaissier');


Route::post('Consultation/getArticleActif', 'ArticleWSMobile@getArticleActif');
Route::post('Consultation/displaytoparticlebystation', 'ArticleWSMobile@DisplayTopArticleByStation');
Route::post('Consultation/getArticleEnRuptureDeStock', 'ArticleWSMobile@getArticleEnRuptureDeStock');
Route::post('Consultation/displayarticlerepturestockbystation', 'ArticleWSMobile@DisplayArticleEnReptureStockByStation');
Route::post('Consultation/displaytotalvaleurstockbystation', 'ArticleWSMobile@DisplayTotalValeurStockByStation');
Route::post('Consultation/displaytotalvaleurstock', 'ArticleWSMobile@DisplayValeurStockByAllStation');
Route::post('Consultation/getProducts', 'ArticleWSMobile@getProducts');
Route::post('Consultation/displayproduitbystation', 'ArticleWSMobile@DisplayProduitByStation');

Route::post('Consultation/getArticleAnomalie', 'ArticleWSMobile@getArticleAnomalie');

Route::post('Consultation/calcultotaldepense', 'CaisseWSMobile@CalculTotalDepense');
Route::post('Consultation/displaybenificestation', 'CaisseWSMobile@DisplayBenificeStation');
Route::post('Consultation/getCountsOfProducts', 'ArticleWSMobile@get_counts_of_products');
Route::post('Consultation/get_count_ArticleAnomalie', 'ArticleWSMobile@get_count_getArticleAnomalie');
Route::post('Consultation/get_count_ArticleEnRuptureDeStock', 'ArticleWSMobile@get_count_getArticleEnRuptureDeStock');
Route::post('Consultation/get_count_Products', 'ArticleWSMobile@get_count_getProducts');
Route::post('Consultation/get_count_ArticleActif', 'ArticleWSMobile@get_count_getArticleActif');



// Table Traking
Route::post('Traking/insert', 'TrakingController@InsertTraking');

//Table Ordre Mission
Route::post('OrdreMission/displayetatordremission', 'OrdreMissionController@DisplayEtatOrdreMission');
Route::post('OrdreMission/displayordre', 'OrdreMissionController@DisplayOrdre');
Route::post('OrdreMission/updateetatordremission', 'OrdreMissionController@UpdateEtatOrdreMission');
Route::post('OrdreMission/BatchUpdateOrdreMission', 'OrdreMissionController@BatchUpdateOrdreMission');
//Table Ville
Route::post('Ville/getville', 'VilleController@getVilles');

//Table Timbre
Route::post('Timbre/getAll', 'TimbreWSMobile@getAll');

//// Table(VCAutre,VCPromo,VCPrix,VCLancementNP)
Route::post('VConcu/getDataVConcu/{table}', 'VConcuController@getDataVConcu');
Route::post('VConcu/getDataVConcuByCode/{table}', 'VConcuController@getDataVConcuByCode');
Route::post('VConcu/addDataVConcu/{table}', array('as' => 'addDataVConcu', 'uses' => 'VConcuController@addDataVConcu'));
Route::delete('VConcu/deleteDataVConcu', array('as' => 'deleteDataVConcu', 'uses' => 'VConcuController@deleteDataVConcu'));
Route::post('VConcu/getVCListeConcurrent', 'VConcuController@getVCListeConcurrent');
Route::post('VConcu/getVCTypeCommunication', 'VConcuController@getVCTypeCommunication');
Route::post('VConcu/getVCImage', 'VConcuController@getVCImage');
Route::post('VConcu/upload', 'VConcuController@upload');
Route::post('VConcu/getImages', 'VConcuController@getImages');

Route::post('Visite/getAllFamille', 'VisiteController@getAllFamille');
Route::post('Visite/getAllTypePVente', 'VisiteController@getAllTypePVente');
Route::post('Visite/getAllSuperficie', 'VisiteController@getAllSuperficie');
Route::post('Visite/getAllTypeService', 'VisiteController@getAllTypeService');
Route::post('Visite/addBatchVisite', 'VisiteController@addBatchVisite');
Route::delete('Visite/deleteVisite', 'VisiteController@deleteVisite');
Route::post('Visite/getAllVisite', 'VisiteController@getAllVisite');
Route::post('Visite/getVisiteByCode', 'VisiteController@getVisiteByCode');
Route::post('Visite/getAllLigneVisite', 'VisiteController@getAllLigneVisite');


Route::group(['prefix' => 'Config/'], function () {
    Route::post('getCollation', 'ConfigWSMobile@getCollation');
    Route::post('getDateTimeFormat', 'ConfigWSMobile@getDateTimeFormat');
    Route::post('', 'ConfigWSMobile@getConfig');
});

Route::post('Commande/controleInventaire','BonCommandeWSMobile@controle_inventaire');
//License
Route::post('License/getLicensesUrl','BaseConfigController@get_licenses_services_url');
Route::post('License/put_url_licenses','BaseConfigController@put_url_licenses');
Route::post('License/get_backup_url','BaseConfigController@get_backup_url');
Route::post('License/encrypt_baseConfig','BaseConfigController@encrypt_baseConfig');

Route::post('Article/getArticleByChunk','ArticleWSMobile@getArticleByChunk');

Route::post('checkLicense/{idDevice}','LicenseController@CheckLicense');

//immobilisation
Route::post('getImmobilisation','ImmobilisationController@getImmobilisation');
Route::post('getBatimentByUser','ImmobilisationController@getBatimentByUser');
Route::post('AffectCodeBareBatiment','ClientWSMobile@AffectCodeBareBatiment');
Route::post('getAllTypeMouvement','BonCommandeWSMobile@getAllTypeMouvement');
Route::post('getAllDeplacememntOutByUser','BonCommandeWSMobile@getAllDeplacememntOutByUser');

// Upload piece jointe Immobilisation
Route::post('LigneCommande/upload', 'LigneBonCommandeWSMobile@upload');
Route::post('LigneCommande/getImages', 'LigneBonCommandeWSMobile@getImages');


// DCrypt BaseConfig
Route::post('DcryptBaseConfig', 'UtilisateurWSMobile@DcryptBaseConfig');






