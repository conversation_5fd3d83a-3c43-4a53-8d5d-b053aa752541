{"openapi": "3.0.0", "info": {"title": "ProCaisse API Documentation", "description": "Documentation complète des APIs du système ProCaisse pour la gestion de caisse et d'inventaire", "contact": {"name": "Support ProCaisse", "email": "<EMAIL>"}, "version": "1.0.0"}, "servers": [{"url": "http://127.0.0.1:8000/api", "description": "Serveur ProCaisse"}], "paths": {"/ArticleClasseRemise/getRemises": {"post": {"tags": ["Article Classe Remise"], "summary": "Récupérer toutes les classes de remise des articles", "description": "Retourne la liste complète des classes de remise associées aux articles", "operationId": "6d0d453161685a460396afeeb6580c54", "requestBody": {"description": "Données de connexion et paramètres de requête", "required": true, "content": {"application/json": {"schema": {"properties": {"connexion": {"description": "Paramètres de connexion à la base de données", "properties": {"id_base_config": {"type": "string", "example": "1562"}, "key_base": {"type": "string", "example": "661-47J-J4P"}, "dbName": {"type": "string", "example": "681f857273fa321692a64d730af8412b"}, "dbIpAddress": {"type": "string", "example": "17d94b77f3ffb1fedc4176d9916c9265"}, "adresse_ip": {"type": "string", "example": "*************"}, "port": {"type": "string", "example": "2222"}, "username": {"type": "string", "example": "2148ed972ff6ca8e0bf0484a9bcdc179"}, "password": {"type": "string", "example": "454193ea003c0c8ea858f1501fccbd3c"}, "designation_base": {"type": "string", "example": "ChahiaNewPatrimoine"}, "produit": {"type": "string", "example": "ProCaisse Mobility"}, "id_entreprise": {"type": "string", "example": "980"}, "date_creation": {"type": "string", "example": "26-03-2024 12:09:26"}, "licences": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 29904}, "activat": {"type": "string", "example": "true"}, "datef": {"type": "string", "example": "2025-09-23"}, "demo": {"type": "string", "example": "false"}, "device": {"type": "string", "example": "promobile"}, "etablissement": {"type": "string", "example": "nader"}, "id_device": {"type": "string", "example": "79fc8999cc3a379e"}, "produit": {"type": "string", "example": "ProCaisse Mobility"}, "version": {"type": "string", "example": "v13"}}, "type": "object"}}}, "type": "object"}, "object": {"description": "Paramètres de pagination", "properties": {"page": {"type": "string", "example": "1"}, "limit": {"type": "string", "example": "2000"}}, "type": "object"}}, "type": "object"}}}}, "responses": {"200": {"description": "Liste des classes de remise récupérée avec succès", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"ART_CLASS_REM_Code": {"description": "Code de la classe de remise", "type": "string", "example": "REM001"}, "ART_CLASS_REM_ArtCode": {"description": "Code de l'article", "type": "string", "example": "ART001"}, "ART_CLASS_REM_DDm": {"nullable": true, "description": "Date de début", "type": "string", "example": "2024-01-01"}, "ART_CLASS_REM_export": {"nullable": true, "description": "Statut d'export", "type": "string", "example": "Y"}, "ART_CLASS_REM_station": {"nullable": true, "description": "Code station", "type": "string", "example": "ST001"}, "ART_CLASS_REM_user": {"nullable": true, "description": "Code utilisateur", "type": "string", "example": "USER001"}}, "type": "object"}}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"properties": {"error": {"type": "string", "example": "Erreur de connexion à la base de données"}}, "type": "object"}}}}}}}, "/ArticleClasseRemise/addArticleClasseRemiseMobile": {"post": {"tags": ["Article Classe Remise"], "summary": "A<PERSON>ter ou mettre à jour des classes de remise d'articles", "description": "Permet d'ajouter de nouvelles classes de remise ou de mettre à jour celles existantes pour les articles", "operationId": "19de5b112ef8b585986f84f306b75659", "requestBody": {"description": "Données des classes de remise à ajouter/modifier", "required": true, "content": {"application/json": {"schema": {"properties": {"connexion": {"description": "Paramètres de connexion à la base de données", "properties": {"id_base_config": {"type": "string", "example": "1562"}, "key_base": {"type": "string", "example": "661-47J-J4P"}, "dbName": {"type": "string", "example": "681f857273fa321692a64d730af8412b"}, "dbIpAddress": {"type": "string", "example": "17d94b77f3ffb1fedc4176d9916c9265"}, "adresse_ip": {"type": "string", "example": "*************"}, "port": {"type": "string", "example": "2222"}, "username": {"type": "string", "example": "2148ed972ff6ca8e0bf0484a9bcdc179"}, "password": {"type": "string", "example": "454193ea003c0c8ea858f1501fccbd3c"}, "designation_base": {"type": "string", "example": "ChahiaNewPatrimoine"}, "produit": {"type": "string", "example": "ProCaisse Mobility"}, "id_entreprise": {"type": "string", "example": "980"}, "date_creation": {"type": "string", "example": "26-03-2024 12:09:26"}, "licences": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 29904}, "activat": {"type": "string", "example": "true"}, "datef": {"type": "string", "example": "2025-09-23"}, "demo": {"type": "string", "example": "false"}, "device": {"type": "string", "example": "promobile"}, "etablissement": {"type": "string", "example": "nader"}, "id_device": {"type": "string", "example": "79fc8999cc3a379e"}, "produit": {"type": "string", "example": "ProCaisse Mobility"}, "version": {"type": "string", "example": "v13"}}, "type": "object"}}}, "type": "object"}, "object": {"properties": {"articleClasseRemises": {"type": "array", "items": {"properties": {"ART_CLASS_REM_Code": {"description": "Code de la classe de remise", "type": "string", "example": "REM001"}, "ART_CLASS_REM_ArtCode": {"description": "Code de l'article", "type": "string", "example": "ART001"}, "ART_CLASS_REM_DDm": {"nullable": true, "description": "Date de début (peut être 'NULL')", "type": "string", "example": "2024-01-01"}, "ART_CLASS_REM_export": {"nullable": true, "description": "Statut d'export (peut être 'NULL')", "type": "string", "example": "Y"}, "ART_CLASS_REM_station": {"nullable": true, "description": "Code station (peut être 'NULL')", "type": "string", "example": "ST001"}, "ART_CLASS_REM_user": {"nullable": true, "description": "Code utilisateur (peut être 'NULL')", "type": "string", "example": "USER001"}}, "type": "object"}}}, "type": "object"}}, "type": "object"}}}}, "responses": {"200": {"description": "Classes de remise ajoutées/mises à jour avec succès", "content": {"application/json": {"schema": {"description": "Résultat de l'opération d'insertion/mise à jour", "type": "boolean"}, "example": true}}}, "400": {"description": "Données invalides ou manquantes", "content": {"application/json": {"schema": {"type": "boolean"}, "example": false}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"properties": {"error": {"type": "string", "example": "Erreur lors de l'insertion/mise à jour"}}, "type": "object"}}}}}}}, "/ArticleCodebar/getArticleCodeBarByCode": {"post": {"tags": ["Article Code Barres Mobile"], "summary": "Récupérer un code à barres par codes parent et fils", "description": "Retourne un code à barres d'article spécifique en utilisant les codes parent et fils", "operationId": "efb132a2f8ee913c969fcd674a9246f8", "requestBody": {"description": "Codes parent et fils du code à barres", "required": true, "content": {"application/json": {"schema": {"properties": {"Parent_CodeBar": {"description": "Code à barres parent", "type": "string", "example": "PARENT001"}, "Fils_CodeBar": {"description": "Code à barres fils", "type": "string", "example": "FILS001"}}, "type": "object"}}}}, "responses": {"200": {"description": "Code à barres trouvé avec succès", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ArticleCodeBar"}}}}, "404": {"description": "Code à barres non trouvé", "content": {"application/json": {"schema": {"type": "null"}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/ArticleCodebar/getArticleCodeBar": {"get": {"tags": ["Article Code Barres Mobile"], "summary": "Récupérer tous les codes à barres d'articles", "description": "Retourne la liste complète des codes à barres d'articles avec leurs informations", "operationId": "6675bfc0c3672d74335a0d4af6325cfe", "responses": {"200": {"description": "Liste des codes à barres récupérée avec succès", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ArticleCodeBarSimple"}}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/ArticleCodebar/getArticleCodeBarByX": {"post": {"tags": ["Article Code Barres Mobile"], "summary": "Rechercher des codes à barres par champ dynamique", "description": "Permet de rechercher des codes à barres en spécifiant un champ et une valeur", "operationId": "725ca9fe377edf2fd5b4f48a27057c95", "requestBody": {"description": "Champ et valeur de recherche", "required": true, "content": {"application/json": {"schema": {"properties": {"field": {"description": "Nom du champ à rechercher", "type": "string", "example": "Parent_CodeBar"}, "value": {"description": "Valeur à rechercher", "type": "string", "example": "PARENT001"}}, "type": "object"}}}}, "responses": {"200": {"description": "Codes à barres trouvés avec succès", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ArticleCodeBar"}}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/ArticleCodebar/addArticleCodeBar": {"post": {"tags": ["Article Code Barres Mobile"], "summary": "Ajouter un nouveau code à barres d'article", "description": "Crée un nouveau code à barres d'article dans la base de données", "operationId": "7a6d82bb748551de01990c9f7896d1bc", "requestBody": {"description": "Données du code à barres à créer", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ArticleCodeBarInput"}}}}, "responses": {"200": {"description": "Code à barres créé avec succès", "content": {"application/json": {"schema": {"type": "boolean"}, "example": true}}}, "400": {"description": "Données invalides ou manquantes", "content": {"application/json": {"schema": {"type": "boolean"}, "example": false}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/ArticleCodebar/addArticleCodeBarMobile": {"post": {"tags": ["Article Code Barres Mobile"], "summary": "Ajouter ou mettre à jour des codes à barres depuis mobile", "description": "Permet d'ajouter de nouveaux codes à barres ou de mettre à jour ceux existants depuis l'application mobile", "operationId": "cd20599977afca193cf297375515c58a", "requestBody": {"description": "Données des codes à barres à ajouter/modifier", "required": true, "content": {"application/json": {"schema": {"properties": {"connexion": {"$ref": "#/components/schemas/DatabaseConnection"}, "object": {"properties": {"articleCodeBars": {"type": "array", "items": {"$ref": "#/components/schemas/ArticleCodeBarInput"}}}, "type": "object"}}, "type": "object"}}}}, "responses": {"200": {"description": "Codes à barres ajoutés/mis à jour avec succès", "content": {"application/json": {"schema": {"type": "boolean"}, "example": true}}}, "400": {"description": "Données invalides ou manquantes", "content": {"application/json": {"schema": {"type": "boolean"}, "example": false}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/ArticleCodebar/updateArticleCodeBar": {"post": {"tags": ["Article Code Barres Mobile"], "summary": "Mettre à jour un code à barres d'article", "description": "Met à jour les informations d'un code à barres d'article existant", "operationId": "45430f5c5a169b2ee3d8d8b867075ae6", "requestBody": {"description": "Données du code à barres à mettre à jour", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ArticleCodeBarInput"}}}}, "responses": {"200": {"description": "Code à barres mis à jour avec succès", "content": {"application/json": {"schema": {"type": "string"}, "example": "Data modified"}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/ArticleCodebar/deleteArticleCodeBar": {"post": {"tags": ["Article Code Barres Mobile"], "summary": "Supprimer un code à barres d'article", "description": "Supprime définitivement un code à barres d'article de la base de données", "operationId": "c5e5660b3215ef522aefd26f1b313ca1", "requestBody": {"description": "Codes parent et fils du code à barres à supprimer", "required": true, "content": {"application/json": {"schema": {"properties": {"Parent_CodeBar": {"description": "Code à barres parent", "type": "string", "example": "PARENT001"}, "Fils_CodeBar": {"description": "Code à barres fils", "type": "string", "example": "FILS001"}}, "type": "object"}}}}, "responses": {"200": {"description": "Code à barres supprimé avec succès", "content": {"application/json": {"schema": {"type": "string"}, "example": "Data Deleted"}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/Article/getArticleByBarCode": {"post": {"tags": ["Articles Mobile"], "summary": "Rechercher un article par code à barres", "description": "<PERSON><PERSON>ne les détails d'un article en recherchant par code à barres ou nom", "operationId": "e2839fe3b5a202e7cf815ab162d1bedd", "requestBody": {"description": "Paramètres de connexion et code à barres", "required": true, "content": {"application/json": {"schema": {"properties": {"connexion": {"$ref": "#/components/schemas/DatabaseConnection"}, "object": {"description": "Code à barres ou nom de l'article à rechercher", "type": "string", "example": "1234567890123"}}, "type": "object"}}}}, "responses": {"200": {"description": "Article trouvé avec succès", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ArticleDetail"}}}}, "404": {"description": "Article non trouvé", "content": {"application/json": {"schema": {"type": "null"}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/Article/getArticles": {"post": {"tags": ["Articles Mobile"], "summary": "Récupérer la liste des articles avec filtres avancés", "description": "Retourne la liste des articles avec gestion des autorisations, stations, et types d'utilisateurs", "operationId": "745742d86f4441d5cee76f540de33365", "parameters": [{"name": "user", "in": "header", "description": "Code utilisateur pour autorisations", "required": false, "schema": {"type": "string", "example": "USER001"}}, {"name": "station", "in": "query", "description": "Code station pour filtrer", "required": false, "schema": {"type": "string", "example": "ST001"}}, {"name": "limit", "in": "query", "description": "Limite de pagination", "required": false, "schema": {"type": "integer", "example": 50}}, {"name": "ddm", "in": "query", "description": "Date de dernière modification", "required": false, "schema": {"type": "string", "format": "date", "example": "2024-01-01"}}], "requestBody": {"description": "Paramètres de connexion", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StandardRequest"}}}}, "responses": {"200": {"description": "Liste des articles récupérée avec succès", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ArticleDetail"}}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/Article/addArticleMobile": {"post": {"tags": ["Articles Mobile"], "summary": "A<PERSON>ter ou mettre à jour des articles depuis mobile", "description": "Permet d'ajouter de nouveaux articles ou de mettre à jour ceux existants depuis l'application mobile", "operationId": "e3432eceb32d3eee00c2e93821db97e4", "requestBody": {"description": "Données des articles à ajouter/modifier", "required": true, "content": {"application/json": {"schema": {"properties": {"connexion": {"$ref": "#/components/schemas/DatabaseConnection"}, "object": {"properties": {"articles": {"type": "array", "items": {"$ref": "#/components/schemas/ArticleInput"}}}, "type": "object"}}, "type": "object"}}}}, "responses": {"200": {"description": "Articles ajoutés/mis à jour avec succès", "content": {"application/json": {"schema": {"type": "boolean"}, "example": true}}}, "400": {"description": "Données invalides ou manquantes", "content": {"application/json": {"schema": {"type": "boolean"}, "example": false}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/Banque/getBanques": {"post": {"tags": ["Banques Mobile"], "summary": "Récup<PERSON>rer toutes les banques", "description": "Retourne la liste complète des banques disponibles", "operationId": "23976f94b7499fb9a5fe521f1e7ddfa0", "requestBody": {"description": "Paramètres de connexion", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StandardRequest"}}}}, "responses": {"200": {"description": "Liste des banques récupérée avec succès", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Banque"}}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/License/getLicensesUrl": {"post": {"tags": ["Configuration Base"], "summary": "Récupérer les URLs des services de licences", "description": "Retourne la liste des URLs des services de licences depuis le fichier de configuration", "operationId": "28361b440f2e60f6b357c761d68630a8", "responses": {"200": {"description": "URLs des licences récupérées avec succès", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LicenseUrl"}}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/Commande/getCommande": {"post": {"tags": ["Bons de Commande Mobile"], "summary": "Récupérer les bons de commande", "description": "Retourne la liste des bons de commande avec filtres par mois/année et gestion des zones", "operationId": "485429e306e92d299ec26790537b03f2", "parameters": [{"name": "zone", "in": "query", "description": "Filtrer par zone utilisateur", "required": false, "schema": {"type": "boolean", "example": true}}, {"name": "user", "in": "header", "description": "Code utilisateur pour filtrage par zone", "required": false, "schema": {"type": "string", "example": "USER001"}}], "requestBody": {"description": "Paramètres de connexion et filtres optionnels", "required": true, "content": {"application/json": {"schema": {"properties": {"connexion": {"$ref": "#/components/schemas/DatabaseConnection"}, "object": {"properties": {"month": {"description": "<PERSON><PERSON> (optionnel, défaut: mois actuel)", "type": "integer", "example": 1}, "year": {"description": "<PERSON><PERSON> (<PERSON>nel, défaut: année actuelle)", "type": "integer", "example": 2024}}, "type": "object"}}, "type": "object"}}}}, "responses": {"200": {"description": "Liste des bons de commande récupérée avec succès", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BonCommande"}}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/Caisse/getCaisses": {"post": {"tags": ["Caisses Mobile"], "summary": "Ré<PERSON><PERSON>rer toutes les caisses", "description": "Retourne la liste complète des caisses disponibles", "operationId": "2e9f865158b10d61578dd8d7c437721f", "requestBody": {"description": "Paramètres de connexion", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StandardRequest"}}}}, "responses": {"200": {"description": "Liste des caisses récupérée avec succès", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Caisse"}}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/Client/getClientByCode": {"post": {"tags": ["Clients Mobile"], "summary": "Récupérer un client par son code", "description": "Re<PERSON>ne les détails d'un client spécifique en utilisant son code", "operationId": "892a23a68c518166f9764e844dbcb136", "requestBody": {"description": "Paramètres de connexion et code client", "required": true, "content": {"application/json": {"schema": {"properties": {"connexion": {"$ref": "#/components/schemas/DatabaseConnection"}, "object": {"description": "Code du client à rechercher", "type": "string", "example": "CLI001"}}, "type": "object"}}}}, "responses": {"200": {"description": "Client trouvé avec succès", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Client"}}}}, "404": {"description": "Client non trouvé", "content": {"application/json": {"schema": {"type": "null"}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/Client/getClients": {"post": {"tags": ["Clients Mobile"], "summary": "Récupérer la liste des clients", "description": "Retourne la liste des clients avec gestion des autorisations par utilisateur, station et zone", "operationId": "9b577c26e992c71be2f8070110b18d31", "parameters": [{"name": "user", "in": "header", "description": "Code utilisateur pour les autorisations", "required": true, "schema": {"type": "string", "example": "USER001"}}], "requestBody": {"description": "Paramètres de connexion et filtres optionnels", "required": true, "content": {"application/json": {"schema": {"properties": {"connexion": {"$ref": "#/components/schemas/DatabaseConnection"}, "CLI_DDm": {"description": "Date de début pour filtrer les clients (optionnel)", "type": "string", "format": "date", "example": "2024-01-01"}}, "type": "object"}}}}, "responses": {"200": {"description": "Liste des clients récupérée avec succès", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ClientWithSolde"}}}}}, "401": {"description": "Utilisateur non autorisé", "content": {"application/json": {"schema": {"properties": {"error": {"type": "string", "example": "Utilisateur non trouvé ou non autorisé"}}, "type": "object"}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/Client/addClient": {"post": {"tags": ["Clients Mobile"], "summary": "Ajouter un nouveau client", "description": "Crée un nouveau client dans la base de données", "operationId": "964a65d407d562044d267bd68e774828", "requestBody": {"description": "Données du client à créer", "required": true, "content": {"application/json": {"schema": {"properties": {"connexion": {"$ref": "#/components/schemas/DatabaseConnection"}, "object": {"$ref": "#/components/schemas/ClientInput"}}, "type": "object"}}}}, "responses": {"200": {"description": "Client créé avec succès", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientInput"}}}}, "400": {"description": "Données invalides ou manquantes", "content": {"application/json": {"schema": {"type": "null"}}}}, "500": {"description": "<PERSON><PERSON><PERSON> lors de la création", "content": {"application/json": {"schema": {"type": "null"}}}}}}}, "/Client/updateClient": {"post": {"tags": ["Clients Mobile"], "summary": "Mettre à jour un client existant", "description": "Met à jour les informations d'un client existant", "operationId": "74989362513655704bf9e6654eae475f", "requestBody": {"description": "Données du client à mettre à jour", "required": true, "content": {"application/json": {"schema": {"properties": {"connexion": {"$ref": "#/components/schemas/DatabaseConnection"}, "CLI_Code": {"description": "Code du client à modifier", "type": "string", "example": "CLI001"}, "CLI_Nom": {"description": "Nouveau nom", "type": "string", "example": "<PERSON><PERSON>"}, "CLI_Prenom": {"description": "Nouveau prénom", "type": "string", "example": "<PERSON>"}, "CLI_Telephone": {"description": "Nouveau téléphone", "type": "string", "example": "+33123456789"}}, "type": "object"}}}}, "responses": {"200": {"description": "Client mis à jour avec succès", "content": {"application/json": {"schema": {"type": "boolean"}, "example": true}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/Client/deleteClient": {"post": {"tags": ["Clients Mobile"], "summary": "Supprimer un client", "description": "Supprime définitivement un client de la base de données", "operationId": "e53cb60ca40449afa9f8404f392b7a00", "requestBody": {"description": "Code du client à supprimer", "required": true, "content": {"application/json": {"schema": {"properties": {"connexion": {"$ref": "#/components/schemas/DatabaseConnection"}, "object": {"description": "Code du client à supprimer", "type": "string", "example": "CLI001"}}, "type": "object"}}}}, "responses": {"200": {"description": "Client supprimé avec succès", "content": {"application/json": {"schema": {"type": "string"}, "example": "Data Deleted"}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/Famille/getFamilles": {"post": {"tags": ["Familles Mobile"], "summary": "<PERSON><PERSON><PERSON><PERSON><PERSON> toutes les familles d'articles", "description": "Retourne la liste complète des familles d'articles avec tous leurs détails", "operationId": "ed55fcc0c5a6555cbc37079362734a35", "requestBody": {"description": "Paramètres de connexion", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StandardRequest"}}}}, "responses": {"200": {"description": "Liste des familles récupérée avec succès", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FamilleMobile"}}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/duxinventory/type-article/getAll": {"post": {"tags": ["Types Article DuxInventory"], "summary": "Récupérer tous les types d'articles", "description": "Retourne la liste complète des types d'articles disponibles", "requestBody": {"description": "Paramètres de connexion", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StandardRequest"}}}}, "responses": {"200": {"description": "Liste des types d'articles récupérée avec succès", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TypeArticle"}}}, "type": "object"}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/duxinventory/type-article/add": {"post": {"tags": ["Types Article DuxInventory"], "summary": "A<PERSON>ter ou mettre à jour des types d'articles", "description": "Permet d'ajouter de nouveaux types d'articles ou de mettre à jour ceux existants", "requestBody": {"description": "Données des types d'articles à ajouter/modifier", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StandardRequestWithData"}}}}, "responses": {"200": {"description": "Types d'articles ajoutés/mis à jour avec succès", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TypeArticle"}}}, "type": "object"}}}}, "400": {"description": "Données invalides", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/duxinventory/fournisseur/getAll": {"post": {"tags": ["Fournisseurs DuxInventory"], "summary": "Récupérer tous les fournisseurs", "description": "Retourne la liste complète des fournisseurs (tiers de type débit)", "operationId": "c5721579e68b948d358f65be705941d8", "requestBody": {"description": "Paramètres de connexion", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StandardRequest"}}}}, "responses": {"200": {"description": "Liste des fournisseurs récupérée avec succès", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Fournisseur"}}}, "type": "object"}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/duxinventory/fournisseur/add": {"post": {"tags": ["Fournisseurs DuxInventory"], "summary": "A<PERSON>ter ou mettre à jour des fournisseurs", "description": "Permet d'ajouter de nouveaux fournisseurs ou de mettre à jour ceux existants", "requestBody": {"description": "Données des fournisseurs à ajouter/modifier", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StandardRequestWithData"}}}}, "responses": {"200": {"description": "Fournisseurs ajoutés/mis à jour avec succès", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Fournisseur"}}}, "type": "object"}}}}, "400": {"description": "Données invalides", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/duxinventory/inventaire/getInventaires": {"post": {"tags": ["Inventaires DuxInventory"], "summary": "Récupérer les inventaires de l'exercice actif", "description": "Retourne la liste des inventaires pour l'exercice en cours", "operationId": "d0066faf046ac56e5ea36316bda21db4", "requestBody": {"description": "Paramètres de connexion", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StandardRequest"}}}}, "responses": {"200": {"description": "Liste des inventaires récupérée avec succès", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Inventaire"}}}, "type": "object"}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/duxinventory/inventaire/getEtatInventaire": {"post": {"tags": ["Inventaires DuxInventory"], "summary": "Récupérer les états d'inventaire", "description": "Retourne la liste des états possibles pour les inventaires", "operationId": "b46e9eed3a266e523fde060f297f110b", "requestBody": {"description": "Paramètres de connexion", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StandardRequest"}}}}, "responses": {"200": {"description": "Liste des états d'inventaire récupérée avec succès", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/EtatInventaire"}}}, "type": "object"}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/duxinventory/article/getArticles": {"post": {"tags": ["Articles DuxInventory"], "summary": "Récupérer la liste des articles avec pagination", "description": "Retourne la liste paginée des articles actifs et stockables avec leurs codes à barres", "operationId": "e015858bdfa65ac92d003d98c659337f", "requestBody": {"description": "Paramètres de connexion et de pagination", "required": true, "content": {"application/json": {"schema": {"properties": {"connexion": {"$ref": "#/components/schemas/DatabaseConnection"}, "object": {"properties": {"limit": {"description": "Nombre d'articles par page", "type": "integer", "example": 15}}, "type": "object"}}, "type": "object"}}}}, "responses": {"200": {"description": "Liste des articles récupérée avec succès", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"properties": {"current_page": {"type": "integer", "example": 1}, "per_page": {"type": "integer", "example": 15}, "total": {"type": "integer", "example": 150}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Article"}}}, "type": "object"}}, "type": "object"}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/duxinventory/article/getCountArticle": {"post": {"tags": ["Articles DuxInventory"], "summary": "Compter le nombre total d'articles", "description": "Retourne le nombre total d'articles actifs et stockables", "operationId": "6f433ec2e915b5127232898ee3898a75", "requestBody": {"description": "Paramètres de connexion", "required": true, "content": {"application/json": {"schema": {"properties": {"connexion": {"$ref": "#/components/schemas/DatabaseConnection"}}, "type": "object"}}}}, "responses": {"200": {"description": "Nombre d'articles récupéré avec succès", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"description": "Nombre total d'articles", "type": "integer", "example": 1250}}, "type": "object"}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/duxinventory/article/etatStock": {"post": {"tags": ["Articles DuxInventory"], "summary": "Récupérer l'état du stock des articles", "description": "Retourne l'état détaillé du stock des articles avec informations de mouvement et valorisation", "operationId": "d09e4291e01d0bf6b8dfd6d1fc389a4c", "requestBody": {"description": "Paramètres de connexion et filtres", "required": true, "content": {"application/json": {"schema": {"properties": {"connexion": {"$ref": "#/components/schemas/DatabaseConnection"}, "object": {"properties": {"idStation": {"description": "ID de la station ('all' pour toutes)", "type": "string", "example": "ST001"}, "limit": {"description": "Nombre d'éléments par page", "type": "integer", "example": 50}}, "type": "object"}}, "type": "object"}}}}, "responses": {"200": {"description": "État du stock récupéré avec succès", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"properties": {"current_page": {"type": "integer", "example": 1}, "per_page": {"type": "integer", "example": 50}, "total": {"type": "integer", "example": 500}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/EtatStock"}}}, "type": "object"}}, "type": "object"}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/duxinventory/cellule/getAll": {"post": {"tags": ["Cellules DuxInventory"], "summary": "Ré<PERSON><PERSON><PERSON> toutes les cellules", "description": "Retourne la liste complète des cellules de stockage disponibles", "requestBody": {"description": "Paramètres de connexion", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StandardRequest"}}}}, "responses": {"200": {"description": "Liste des cellules récupérée avec succès", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Cellule"}}}, "type": "object"}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/duxinventory/cellule/add": {"post": {"tags": ["Cellules DuxInventory"], "summary": "A<PERSON>ter ou mettre à jour des cellules", "description": "Permet d'ajouter de nouvelles cellules ou de mettre à jour celles existantes", "requestBody": {"description": "Données des cellules à ajouter/modifier", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StandardRequestWithData"}}}}, "responses": {"200": {"description": "Cellules ajoutées/mises à jour avec succès", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Cellule"}}}, "type": "object"}}}}, "400": {"description": "Données invalides", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/duxinventory/couleur/getAll": {"post": {"tags": ["Couleurs DuxInventory"], "summary": "R<PERSON><PERSON><PERSON><PERSON> toutes les couleurs", "description": "Retourne la liste complète des couleurs disponibles pour les articles", "requestBody": {"description": "Paramètres de connexion", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StandardRequest"}}}}, "responses": {"200": {"description": "Liste des couleurs récupérée avec succès", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Couleur"}}}, "type": "object"}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/duxinventory/couleur/add": {"post": {"tags": ["Couleurs DuxInventory"], "summary": "A<PERSON>ter ou mettre à jour des couleurs", "description": "Permet d'ajouter de nouvelles couleurs ou de mettre à jour celles existantes", "requestBody": {"description": "Données des couleurs à ajouter/modifier", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StandardRequestWithData"}}}}, "responses": {"200": {"description": "Couleurs ajoutées/mises à jour avec succès", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Couleur"}}}, "type": "object"}}}}, "400": {"description": "Données invalides", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/duxinventory/famille/getAll": {"post": {"tags": ["Familles DuxInventory"], "summary": "<PERSON><PERSON><PERSON><PERSON><PERSON> toutes les familles d'articles", "description": "Retourne la liste complète des familles d'articles disponibles", "requestBody": {"description": "Paramètres de connexion", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StandardRequest"}}}}, "responses": {"200": {"description": "Liste des familles récupérée avec succès", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Famille"}}}, "type": "object"}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/duxinventory/famille/add": {"post": {"tags": ["Familles DuxInventory"], "summary": "A<PERSON>ter ou mettre à jour des familles", "description": "Permet d'ajouter de nouvelles familles ou de mettre à jour celles existantes", "requestBody": {"description": "Données des familles à ajouter/modifier", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StandardRequestWithData"}}}}, "responses": {"200": {"description": "Familles ajoutées/mises à jour avec succès", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Famille"}}}, "type": "object"}}}}, "400": {"description": "Données invalides", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/duxinventory/marque/getAll": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Ré<PERSON><PERSON>rer toutes les marques", "description": "Retourne la liste complète des marques disponibles", "requestBody": {"description": "Paramètres de connexion", "required": true, "content": {"application/json": {"schema": {"properties": {"connexion": {"$ref": "#/components/schemas/DatabaseConnection"}}, "type": "object"}}}}, "responses": {"200": {"description": "Liste des marques récupérée avec succès", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Marque"}}}, "type": "object"}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/duxinventory/marque/add": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "A<PERSON>ter ou mettre à jour des marques", "description": "Permet d'ajouter de nouvelles marques ou de mettre à jour celles existantes", "requestBody": {"description": "Données des marques à ajouter/modifier", "required": true, "content": {"application/json": {"schema": {"properties": {"connexion": {"$ref": "#/components/schemas/DatabaseConnection"}, "object": {"type": "array", "items": {"$ref": "#/components/schemas/MarqueInput"}}}, "type": "object"}}}}, "responses": {"200": {"description": "<PERSON><PERSON> a<PERSON>/mises à jour avec succès", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Marque"}}}, "type": "object"}}}}, "400": {"description": "Données invalides", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/duxinventory/modele/getAll": {"post": {"tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Ré<PERSON><PERSON>rer tous les modèles", "description": "Retourne la liste complète des modèles disponibles pour les articles", "requestBody": {"description": "Paramètres de connexion", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StandardRequest"}}}}, "responses": {"200": {"description": "Liste des modèles récupérée avec succès", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Modele"}}}, "type": "object"}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/duxinventory/modele/add": {"post": {"tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "A<PERSON>ter ou mettre à jour des modèles", "description": "Permet d'ajouter de nouveaux modèles ou de mettre à jour ceux existants", "requestBody": {"description": "Données des modèles à ajouter/modifier", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StandardRequestWithData"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>/mis à jour avec succès", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Modele"}}}, "type": "object"}}}}, "400": {"description": "Données invalides", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/duxinventory/station/getAll": {"post": {"tags": ["Stations DuxInventory"], "summary": "Récupérer toutes les stations", "description": "Retourne la liste complète des stations/points de vente", "requestBody": {"description": "Paramètres de connexion", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StandardRequest"}}}}, "responses": {"200": {"description": "Liste des stations récupérée avec succès", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Station"}}}, "type": "object"}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/duxinventory/station/getAllStationActive": {"post": {"tags": ["Stations DuxInventory"], "summary": "Récupérer toutes les stations actives", "description": "Retourne la liste des stations actives avec leurs informations essentielles", "operationId": "b4977a4fd361f70384f47b5207b48f55", "requestBody": {"description": "Paramètres de connexion", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StandardRequest"}}}}, "responses": {"200": {"description": "Liste des stations actives récupérée avec succès", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/StationActive"}}}, "type": "object"}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/duxinventory/unite/getAll": {"post": {"tags": ["Unités DuxInventory"], "summary": "Récupérer toutes les unités de mesure", "description": "Retourne la liste complète des unités de mesure disponibles", "requestBody": {"description": "Paramètres de connexion", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StandardRequest"}}}}, "responses": {"200": {"description": "Liste des unités récupérée avec succès", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Unite"}}}, "type": "object"}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/duxinventory/unite/add": {"post": {"tags": ["Unités DuxInventory"], "summary": "A<PERSON>ter ou mettre à jour des unités", "description": "Permet d'ajouter de nouvelles unités ou de mettre à jour celles existantes", "requestBody": {"description": "Données des unités à ajouter/modifier", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StandardRequestWithData"}}}}, "responses": {"200": {"description": "Unités ajoutées/mises à jour avec succès", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Unite"}}}, "type": "object"}}}}, "400": {"description": "Données invalides", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/duxinventory/utilisateur/authentification": {"post": {"tags": ["Utilisateurs DuxInventory"], "summary": "Authentifier un utilisateur", "description": "Authentifie un utilisateur avec login et mot de passe", "operationId": "d50daeefc5c016ca588fcf6fc126625c", "requestBody": {"description": "Données d'authentification", "required": true, "content": {"application/json": {"schema": {"properties": {"connexion": {"$ref": "#/components/schemas/DatabaseConnection"}, "object": {"properties": {"login": {"description": "Login utilisateur", "type": "string", "example": "admin"}, "password": {"description": "Mot de passe", "type": "string", "example": "password123"}}, "type": "object"}}, "type": "object"}}}}, "responses": {"200": {"description": "Authentification réussie", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"$ref": "#/components/schemas/Utilisateur"}}, "type": "object"}}}}, "401": {"description": "Identifiants invalides", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Invalid credentials or user not active"}}, "type": "object"}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/Marque/getMarques": {"post": {"tags": ["Marques Mobile"], "summary": "Ré<PERSON><PERSON>rer toutes les marques", "description": "Retourne la liste complète des marques avec ajout automatique d'une marque 'Autre'", "operationId": "edd291c3a34839ff08dadc7f92de92f6", "requestBody": {"description": "Paramètres de connexion", "required": true, "content": {"application/json": {"schema": {"properties": {"connexion": {"$ref": "#/components/schemas/DatabaseConnection"}}, "type": "object"}}}}, "responses": {"200": {"description": "Liste des marques récupérée avec succès", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MarqueMobile"}}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/SessionCaisse/addSession": {"post": {"tags": ["Sessions Caisse Mobile"], "summary": "<PERSON><PERSON>er une nouvelle session de caisse", "description": "Crée une nouvelle session de caisse en fermant d'abord toute session existante", "operationId": "fc8404d1c974c5b8317c58c08ed42d4c", "requestBody": {"description": "Donn<PERSON> de la session à créer", "required": true, "content": {"application/json": {"schema": {"properties": {"connexion": {"$ref": "#/components/schemas/DatabaseConnection"}, "object": {"properties": {"utilisateur": {"description": "Code utilisateur", "type": "string", "example": "USER001"}, "caisse": {"description": "Code caisse", "type": "string", "example": "CAISSE001"}, "carnet": {"description": "Code carnet", "type": "string", "example": "CARNET001"}, "station": {"description": "Code station", "type": "string", "example": "ST001"}, "fondCaisse": {"description": "Fond de caisse initial", "type": "number", "format": "float", "example": 1000}, "nomMachine": {"description": "Nom de la machine", "type": "string", "example": "MACHINE001"}}, "type": "object"}, "SC_IdSCaisse": {"description": "ID session à fermer (optionnel)", "type": "string", "example": "SC001"}}, "type": "object"}}}}, "responses": {"200": {"description": "Session créée avec succès", "content": {"application/json": {"schema": {"properties": {"error": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "INSERTED"}, "data": {"$ref": "#/components/schemas/SessionCaisse"}}, "type": "object"}}}}, "400": {"description": "<PERSON><PERSON><PERSON> lors de la création", "content": {"application/json": {"schema": {"properties": {"error": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "NOT INSERTED"}}, "type": "object"}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/SessionCaisse/getSessionCaisses": {"post": {"tags": ["Sessions Caisse Mobile"], "summary": "Récupérer les sessions de caisse", "description": "Retourne la liste des sessions de caisse avec filtres optionnels par utilisateur et station", "operationId": "076f42e11d245f1c96a3a6fd090b1125", "parameters": [{"name": "user", "in": "header", "description": "Code utilisateur pour filtrer les sessions", "required": false, "schema": {"type": "string", "example": "USER001"}}, {"name": "station", "in": "query", "description": "Code station pour filtrer les sessions", "required": false, "schema": {"type": "string", "example": "ST001"}}], "requestBody": {"description": "Paramètres de connexion", "required": true, "content": {"application/json": {"schema": {"properties": {"connexion": {"$ref": "#/components/schemas/DatabaseConnection"}}, "type": "object"}}}}, "responses": {"200": {"description": "Liste des sessions récupérée avec succès", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SessionCaisse"}}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/Taille/getTailles": {"post": {"tags": ["Tai<PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON><PERSON> toutes les tailles", "description": "Retourne la liste complète des tailles disponibles", "operationId": "424cc61c305e3220aa35bd5796aa3060", "requestBody": {"description": "Paramètres de connexion", "required": true, "content": {"application/json": {"schema": {"properties": {"connexion": {"$ref": "#/components/schemas/DatabaseConnection"}}, "type": "object"}}}}, "responses": {"200": {"description": "Liste des tailles récupérée avec succès", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Taille"}}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/Taille/addTailleMobile": {"post": {"tags": ["Tai<PERSON>"], "summary": "A<PERSON>ter ou mettre à jour des tailles mobiles", "description": "Permet d'ajouter de nouvelles tailles ou de mettre à jour celles existantes depuis l'application mobile", "operationId": "3935a9788be25224aabd3cac316adfe2", "requestBody": {"description": "Données des tailles à ajouter/modifier", "required": true, "content": {"application/json": {"schema": {"properties": {"connexion": {"$ref": "#/components/schemas/DatabaseConnection"}, "object": {"type": "array", "items": {"$ref": "#/components/schemas/TailleInput"}}}, "type": "object"}}}}, "responses": {"200": {"description": "Tailles ajoutées/mises à jour avec succès", "content": {"application/json": {"schema": {"description": "Résultat de l'opération", "type": "boolean"}, "example": true}}}, "400": {"description": "Données invalides ou manquantes", "content": {"application/json": {"schema": {"type": "boolean"}, "example": false}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/Ticket/getTickets": {"post": {"tags": ["Tickets Mobile"], "summary": "Récupérer la liste des tickets", "description": "Retourne la liste des tickets avec gestion optionnelle des zones utilisateur", "operationId": "40eadea2fe40793a9b8eb59d62e32303", "parameters": [{"name": "zone", "in": "query", "description": "Filtrer par zone utilisateur", "required": false, "schema": {"type": "boolean", "example": true}}, {"name": "user", "in": "header", "description": "Code utilisateur pour filtrage par zone", "required": false, "schema": {"type": "string", "example": "USER001"}}], "requestBody": {"description": "Paramètres de connexion", "required": true, "content": {"application/json": {"schema": {"properties": {"connexion": {"$ref": "#/components/schemas/DatabaseConnection"}}, "type": "object"}}}}, "responses": {"200": {"description": "Liste des tickets récupérée avec succès", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Ticket"}}}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/Ticket/addTicketWithLignesTicket": {"post": {"tags": ["Tickets Mobile"], "summary": "Ajouter un ticket avec ses lignes", "description": "Crée un nouveau ticket de vente avec ses lignes et met à jour automatiquement les stocks", "operationId": "cdc384efd8a680855f8cafcc1142bbb3", "requestBody": {"description": "Données du ticket et de ses lignes", "required": true, "content": {"application/json": {"schema": {"properties": {"connexion": {"$ref": "#/components/schemas/DatabaseConnection"}, "ticket": {"$ref": "#/components/schemas/TicketInput"}, "ligneTicket": {"type": "array", "items": {"$ref": "#/components/schemas/LigneTicketInput"}}}, "type": "object"}}}}, "responses": {"200": {"description": "Ticket créé avec succès", "content": {"application/json": {"schema": {"type": "boolean"}, "example": true}}}, "400": {"description": "Données invalides ou échec de création", "content": {"application/json": {"schema": {"type": "boolean"}, "example": false}}}, "500": {"description": "<PERSON><PERSON><PERSON> serveur", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}}, "components": {"schemas": {"ErrorResponse": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Une erreur s'est produite"}, "errors": {"type": "object"}}, "type": "object"}, "SuccessResponse": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object"}, "message": {"type": "string", "example": "Opération réussie"}}, "type": "object"}, "DatabaseConnection": {"description": "Paramètres de connexion à la base de données ProCaisse", "properties": {"id_base_config": {"description": "Identifiant de configuration de base", "type": "string", "example": "1562"}, "key_base": {"description": "Clé de base", "type": "string", "example": "661-47J-J4P"}, "dbName": {"description": "Nom de base de données crypté", "type": "string", "example": "681f857273fa321692a64d730af8412b"}, "dbIpAddress": {"description": "Adresse IP cryptée", "type": "string", "example": "17d94b77f3ffb1fedc4176d9916c9265"}, "adresse_ip": {"description": "Adresse IP du serveur", "type": "string", "example": "*************"}, "port": {"description": "Port de connexion", "type": "string", "example": "2222"}, "username": {"description": "Nom d'utilisateur crypté", "type": "string", "example": "2148ed972ff6ca8e0bf0484a9bcdc179"}, "password": {"description": "Mot de passe crypté", "type": "string", "example": "454193ea003c0c8ea858f1501fccbd3c"}, "designation_base": {"description": "Désignation de la base", "type": "string", "example": "ChahiaNewPatrimoine"}, "produit": {"description": "Nom du produit", "type": "string", "example": "ProCaisse Mobility"}, "id_entreprise": {"description": "Identifiant de l'entreprise", "type": "string", "example": "980"}, "date_creation": {"description": "Date de création", "type": "string", "example": "26-03-2024 12:09:26"}, "licences": {"description": "Liste des licences", "type": "array", "items": {"$ref": "#/components/schemas/Licence"}}}, "type": "object"}, "Licence": {"description": "Informations de licence", "properties": {"id": {"description": "Identifiant de la licence", "type": "integer", "example": 29904}, "activat": {"description": "Statut d'activation", "type": "string", "example": "true"}, "datef": {"description": "Date de fin", "type": "string", "example": "2025-09-23"}, "dater": {"description": "<PERSON><PERSON><PERSON> restante", "type": "number", "example": 81}, "demo": {"description": "Mode démo", "type": "string", "example": "false"}, "device": {"description": "Type d'appareil", "type": "string", "example": "promobile"}, "email": {"description": "Email", "type": "string", "example": "<EMAIL>"}, "etablissement": {"description": "Établissement", "type": "string", "example": "nader"}, "id_device": {"description": "Identifiant de l'appareil", "type": "string", "example": "79fc8999cc3a379e"}, "produit": {"description": "Nom du produit", "type": "string", "example": "ProCaisse Mobility"}, "version": {"description": "Version", "type": "string", "example": "v13"}}, "type": "object"}, "PaginationRequest": {"description": "Paramètres de pagination", "properties": {"page": {"description": "Numéro de <PERSON>", "type": "string", "example": "1"}, "limit": {"description": "Nombre d'éléments par page", "type": "string", "example": "2000"}}, "type": "object"}, "StandardRequest": {"description": "Structure standard de requête ProCaisse", "properties": {"connexion": {"$ref": "#/components/schemas/DatabaseConnection"}, "object": {"$ref": "#/components/schemas/PaginationRequest"}}, "type": "object"}, "StandardRequestWithData": {"description": "Structure standard de requête ProCaisse avec données", "properties": {"connexion": {"$ref": "#/components/schemas/DatabaseConnection"}, "object": {"description": "<PERSON><PERSON><PERSON> traiter", "type": "object"}}, "type": "object"}, "ArticleClasseRemise": {"description": "Classe de remise d'article", "properties": {"ART_CLASS_REM_Code": {"description": "Code de la classe de remise", "type": "string", "example": "REM001"}, "ART_CLASS_REM_ArtCode": {"description": "Code de l'article", "type": "string", "example": "ART001"}, "ART_CLASS_REM_DDm": {"nullable": true, "description": "Date de début", "type": "string", "example": "2024-01-01"}, "ART_CLASS_REM_export": {"nullable": true, "description": "Statut d'export", "type": "string", "example": "Y"}, "ART_CLASS_REM_station": {"nullable": true, "description": "Code station", "type": "string", "example": "ST001"}, "ART_CLASS_REM_user": {"nullable": true, "description": "Code utilisateur", "type": "string", "example": "USER001"}}, "type": "object"}, "Article": {"description": "Article du système ProCaisse", "properties": {"id": {"description": "Identifiant unique", "type": "string", "example": "123e4567-e89b-12d3-a456-426614174000"}, "code": {"description": "Code de l'article", "type": "string", "example": "ART001"}, "libelle": {"description": "Libellé de l'article", "type": "string", "example": "Article de test"}, "libelleCourte": {"description": "Libellé court", "type": "string", "example": "Art test"}, "photo": {"nullable": true, "description": "Photo de l'article", "type": "string", "example": "article_001.jpg"}, "active": {"description": "Statut actif (1=actif, 0=inactif)", "type": "string", "example": "1"}, "isStockable": {"description": "Article stockable (1=oui, 0=non)", "type": "string", "example": "1"}, "prixuht": {"description": "Prix unitaire HT", "type": "number", "format": "float", "example": 25.5}, "tauxTva": {"description": "Taux de TVA", "type": "number", "format": "float", "example": 20}, "codeFamille": {"nullable": true, "description": "Code famille", "type": "string", "example": "FAM001"}, "libelleFamille": {"nullable": true, "description": "Libellé famille", "type": "string", "example": "Famille test"}, "codeMarque": {"nullable": true, "description": "Code marque", "type": "string", "example": "MAR001"}, "libellleMarque": {"nullable": true, "description": "Libellé marque", "type": "string", "example": "Marque test"}, "codeabare": {"nullable": true, "description": "Code à barres", "type": "string", "example": "1234567890123"}}, "type": "object"}, "Marque": {"description": "<PERSON>que d'article", "properties": {"id": {"description": "Identifiant unique", "type": "string", "example": "123e4567-e89b-12d3-a456-426614174000"}, "code": {"description": "Code de la marque", "type": "string", "example": "MAR001"}, "libelle": {"description": "Libellé de la marque", "type": "string", "example": "Nike"}, "active": {"description": "Statut actif", "type": "boolean", "example": true}, "isSync": {"description": "Statut de synchronisation", "type": "boolean", "example": false}, "created_at": {"type": "string", "format": "date-time", "example": "2024-01-01T10:00:00Z"}, "updated_at": {"type": "string", "format": "date-time", "example": "2024-01-01T10:00:00Z"}}, "type": "object"}, "MarqueInput": {"description": "Données d'entrée pour une marque", "properties": {"id": {"nullable": true, "description": "ID pour mise à jour (optionnel pour création)", "type": "string", "example": "123e4567-e89b-12d3-a456-426614174000"}, "idMobile": {"nullable": true, "description": "ID mobile pour synchronisation", "type": "string", "example": "mobile_123"}, "code": {"description": "Code de la marque", "type": "string", "example": "MAR001"}, "libelle": {"description": "Libellé de la marque", "type": "string", "example": "Nike"}, "active": {"description": "Statut actif", "type": "boolean", "example": true}}, "type": "object"}, "EtatStock": {"description": "État détaillé du stock d'un article", "properties": {"code": {"description": "Code de l'article", "type": "string", "example": "ART001"}, "libelle": {"description": "Libellé de l'article", "type": "string", "example": "Article test"}, "libelleCourte": {"description": "Libellé court", "type": "string", "example": "Art test"}, "photo": {"nullable": true, "description": "Photo de l'article", "type": "string", "example": "article_001.jpg"}, "qte": {"description": "Quantité en stock", "type": "number", "format": "float", "example": 100.5}, "qtee": {"description": "Quantité entrée", "type": "number", "format": "float", "example": 50}, "qtAV": {"description": "Quantité avant vente", "type": "number", "format": "float", "example": 25}, "qts": {"description": "Quantité sortie", "type": "number", "format": "float", "example": 75}, "prixuht": {"description": "Prix unitaire HT", "type": "number", "format": "float", "example": 25.5}, "tauxTva": {"description": "Taux de TVA", "type": "number", "format": "float", "example": 20}, "cmpArt": {"description": "<PERSON><PERSON>t moyen pond<PERSON>", "type": "number", "format": "float", "example": 20}, "tot_cmp": {"description": "Total coût moyen pondéré", "type": "number", "format": "float", "example": 2010}, "tot_pa": {"description": "Total prix d'achat", "type": "number", "format": "float", "example": 1800}, "tot_pv": {"description": "Total prix de vente", "type": "number", "format": "float", "example": 2562.75}, "codestation": {"description": "Code de la station", "type": "string", "example": "ST001"}, "stationArt": {"description": "Libellé de la station", "type": "string", "example": "Station principale"}, "codeabare": {"nullable": true, "description": "Code à barres", "type": "string", "example": "1234567890123"}, "libelleFamille": {"nullable": true, "description": "Libellé famille", "type": "string", "example": "Famille test"}, "libelleMarque": {"nullable": true, "description": "Libellé marque", "type": "string", "example": "Marque test"}}, "type": "object"}, "Taille": {"description": "<PERSON><PERSON> d'article", "properties": {"TAI_Code": {"description": "Code de la taille", "type": "string", "example": "TAI001"}, "TAI_Taille": {"description": "<PERSON><PERSON><PERSON> de <PERSON>", "type": "string", "example": "M"}, "TAI_Station": {"nullable": true, "description": "Code station", "type": "string", "example": "ST001"}, "TAI_User": {"nullable": true, "description": "Code utilisateur", "type": "string", "example": "USER001"}, "DDm": {"nullable": true, "description": "Date de début", "type": "string", "example": "2024-01-01"}, "export": {"nullable": true, "description": "Statut d'export", "type": "string", "example": "Y"}}, "type": "object"}, "TailleInput": {"description": "Données d'entrée pour une taille", "properties": {"TAI_Taille": {"description": "<PERSON><PERSON><PERSON> de <PERSON>", "type": "string", "example": "M"}, "TAI_Station": {"nullable": true, "description": "Code station", "type": "string", "example": "ST001"}, "TAI_User": {"nullable": true, "description": "Code utilisateur", "type": "string", "example": "USER001"}, "T_user": {"nullable": true, "description": "Utilisateur de traitement", "type": "string", "example": "USER001"}, "T_station": {"nullable": true, "description": "Station de traitement", "type": "string", "example": "ST001"}, "DDm": {"nullable": true, "description": "Date de début (peut être 'NULL')", "type": "string", "example": "2024-01-01"}, "export": {"nullable": true, "description": "Statut d'export (peut être 'NULL')", "type": "string", "example": "Y"}, "T_export": {"nullable": true, "description": "Statut d'export de traitement (peut être 'NULL')", "type": "string", "example": "Y"}, "T_DDm": {"nullable": true, "description": "Date de début de traitement (peut être 'NULL')", "type": "string", "example": "2024-01-01"}}, "type": "object"}, "Famille": {"description": "Famille d'articles", "properties": {"id": {"description": "Identifiant unique", "type": "string", "example": "123e4567-e89b-12d3-a456-426614174000"}, "code": {"description": "Code de la famille", "type": "string", "example": "FAM001"}, "libelle": {"description": "Libellé de la famille", "type": "string", "example": "Électronique"}, "active": {"description": "Statut actif", "type": "boolean", "example": true}, "isSync": {"description": "Statut de synchronisation", "type": "boolean", "example": false}, "created_at": {"type": "string", "format": "date-time", "example": "2024-01-01T10:00:00Z"}, "updated_at": {"type": "string", "format": "date-time", "example": "2024-01-01T10:00:00Z"}}, "type": "object"}, "FamilleInput": {"description": "Données d'entrée pour une famille", "properties": {"id": {"nullable": true, "description": "ID pour mise à jour (optionnel pour création)", "type": "string", "example": "123e4567-e89b-12d3-a456-426614174000"}, "idMobile": {"nullable": true, "description": "ID mobile pour synchronisation", "type": "string", "example": "mobile_123"}, "code": {"description": "Code de la famille", "type": "string", "example": "FAM001"}, "libelle": {"description": "Libellé de la famille", "type": "string", "example": "Électronique"}, "active": {"description": "Statut actif", "type": "boolean", "example": true}}, "type": "object"}, "MarqueMobile": {"description": "Marque mobile", "properties": {"MAR_Code": {"description": "Code de la marque", "type": "string", "example": "MAR001"}, "MAR_Designation": {"description": "Désignation de la marque", "type": "string", "example": "Nike"}, "Mar_Station": {"nullable": true, "description": "Code station", "type": "string", "example": "ST001"}, "Mar_User": {"nullable": true, "description": "Code utilisateur", "type": "string", "example": "USER001"}}, "type": "object"}, "ArticleDetail": {"description": "<PERSON><PERSON><PERSON> complets d'un article", "properties": {"ART_Code": {"description": "Code de l'article", "type": "string", "example": "ART001"}, "ART_CodeBar": {"nullable": true, "description": "Code à barres", "type": "string", "example": "1234567890123"}, "ART_Designation": {"description": "Désignation de l'article", "type": "string", "example": "Article de test"}, "ART_PrixUnitaireHT": {"description": "Prix unitaire HT", "type": "number", "format": "float", "example": 25.5}, "ART_TVA": {"description": "Taux de TVA", "type": "number", "format": "float", "example": 20}, "ART_QteStock": {"description": "Quantité en stock", "type": "number", "format": "float", "example": 100.5}, "pvttc": {"description": "Prix de vente TTC", "type": "number", "format": "float", "example": 30.6}, "photo_Path": {"nullable": true, "description": "<PERSON><PERSON><PERSON> de <PERSON>", "type": "string", "example": "/images/article_001.jpg"}, "MAR_Designation": {"nullable": true, "description": "Désignation de la marque", "type": "string", "example": "Nike"}, "PrixSolde": {"nullable": true, "description": "Prix soldé", "type": "number", "format": "float", "example": 20}, "TauxSolde": {"description": "<PERSON><PERSON> de solde", "type": "number", "format": "float", "example": 0}, "FAM_Lib": {"nullable": true, "description": "Libellé de la famille", "type": "string", "example": "Électronique"}}, "type": "object"}, "Client": {"description": "Client ProCaisse", "properties": {"CLI_Code": {"description": "Code du client", "type": "string", "example": "CLI001"}, "CLI_Nom": {"description": "Nom du client", "type": "string", "example": "<PERSON><PERSON>"}, "CLI_Prenom": {"nullable": true, "description": "Prénom du client", "type": "string", "example": "<PERSON>"}, "CLI_Adresse": {"nullable": true, "description": "<PERSON><PERSON><PERSON>", "type": "string", "example": "123 Rue de la Paix"}, "CLI_Telephone": {"nullable": true, "description": "Téléphone du client", "type": "string", "example": "+33123456789"}, "CLI_Email": {"nullable": true, "description": "<PERSON><PERSON> du <PERSON>", "type": "string", "example": "<EMAIL>"}, "CLI_Type": {"description": "Type de client", "type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "CLI_Station": {"nullable": true, "description": "Code station", "type": "string", "example": "ST001"}, "CLI_DDm": {"description": "Date de dernière modification", "type": "string", "format": "date-time", "example": "2024-01-01T10:00:00Z"}, "Clt_Info1": {"nullable": true, "description": "Information complémentaire 1", "type": "string", "example": "info"}}, "type": "object"}, "ClientWithSolde": {"description": "Client avec informations de solde", "properties": {"CLI_Code": {"description": "Code du client", "type": "string", "example": "CLI001"}, "CLI_Nom": {"description": "Nom du client", "type": "string", "example": "<PERSON><PERSON>"}, "CLI_Prenom": {"nullable": true, "description": "Prénom du client", "type": "string", "example": "<PERSON>"}, "CodeClient": {"description": "Code client (référence)", "type": "string", "example": "CLI001"}, "SoldeClient": {"description": "Solde du client", "type": "number", "format": "float", "example": 1250.75}, "CreditAutorise": {"description": "Crédit autorisé", "type": "number", "format": "float", "example": 5000}, "SoldeDisponible": {"description": "Solde disponible", "type": "number", "format": "float", "example": 3749.25}}, "type": "object"}, "ClientInput": {"description": "Données d'entrée pour un client", "properties": {"CLI_Code": {"description": "Code du client", "type": "string", "example": "CLI001"}, "CLI_Nom": {"description": "Nom du client", "type": "string", "example": "<PERSON><PERSON>"}, "CLI_Prenom": {"nullable": true, "description": "Prénom du client", "type": "string", "example": "<PERSON>"}, "CLI_Adresse": {"nullable": true, "description": "<PERSON><PERSON><PERSON>", "type": "string", "example": "123 Rue de la Paix"}, "CLI_Telephone": {"nullable": true, "description": "Téléphone du client", "type": "string", "example": "+33123456789"}, "CLI_Email": {"nullable": true, "description": "<PERSON><PERSON> du <PERSON>", "type": "string", "example": "<EMAIL>"}, "CLI_Type": {"description": "Type de client", "type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "CLI_Station": {"nullable": true, "description": "Code station", "type": "string", "example": "ST001"}, "CLI_Code_M": {"nullable": true, "description": "Code mobile pour synchronisation", "type": "string", "example": "MOBILE001"}, "Clt_Info1": {"nullable": true, "description": "Information complémentaire 1", "type": "string", "example": "Information complémentaire"}, "CLI_DDm": {"description": "Date de dernière modification", "type": "string", "format": "date-time", "example": "2024-01-01T10:00:00Z"}}, "type": "object"}, "Ticket": {"description": "Ticket de vente ProCaisse", "properties": {"TIK_IdCarnet": {"description": "Identifiant du carnet", "type": "string", "example": "CARNET001"}, "TIK_NumTicket": {"description": "Numéro du ticket", "type": "integer", "example": 1001}, "TIK_NumTicket_M": {"description": "Numéro ticket mobile", "type": "string", "example": "TIK001"}, "TIK_CodClt": {"description": "Code client", "type": "string", "example": "CLI001"}, "TIK_DateHeureTicket": {"description": "Date et heure du ticket", "type": "string", "format": "date-time", "example": "2024-01-01T14:30:00Z"}, "TIK_MtHT": {"description": "Montant HT", "type": "number", "format": "float", "example": 100}, "TIK_MtTTC": {"description": "Montant TTC", "type": "number", "format": "float", "example": 120}, "TIK_MtTVA": {"description": "Montant TVA", "type": "number", "format": "float", "example": 20}, "TIK_station": {"description": "Code station", "type": "string", "example": "ST001"}, "TIK_Exerc": {"description": "Exercice", "type": "string", "example": "2024"}, "TIK_Source": {"description": "Source du ticket", "type": "string", "example": "MOBILE"}, "TIK_Etat": {"description": "État du ticket", "type": "string", "example": "VALIDE"}}, "type": "object"}, "TicketInput": {"description": "Données d'entrée pour un ticket", "properties": {"TIK_IdCarnet": {"description": "Identifiant du carnet", "type": "string", "example": "CARNET001"}, "TIK_NumTicket_M": {"description": "Numéro ticket mobile", "type": "string", "example": "TIK001"}, "TIK_CodClt": {"description": "Code client", "type": "string", "example": "CLI001"}, "TIK_DateHeureTicket": {"description": "Date et heure du ticket", "type": "string", "format": "date-time", "example": "2024-01-01T14:30:00Z"}, "TIK_MtHT": {"description": "Montant HT", "type": "number", "format": "float", "example": 100}, "TIK_MtTTC": {"description": "Montant TTC", "type": "number", "format": "float", "example": 120}, "TIK_MtTVA": {"description": "Montant TVA", "type": "number", "format": "float", "example": 20}, "TIK_station": {"description": "Code station", "type": "string", "example": "ST001"}, "TIK_Exerc": {"description": "Exercice", "type": "string", "example": "2024"}, "TIK_Source": {"description": "Source du ticket", "type": "string", "example": "MOBILE"}, "TIK_Etat": {"description": "État du ticket", "type": "string", "example": "VALIDE"}}, "type": "object"}, "LigneTicketInput": {"description": "Ligne de ticket de vente", "properties": {"LT_CodArt": {"description": "Code article", "type": "string", "example": "ART001"}, "LT_Qte": {"description": "Quantité", "type": "number", "format": "float", "example": 2}, "LT_PrixVente": {"description": "Prix de vente unitaire", "type": "number", "format": "float", "example": 25.5}, "LT_PrixEncaisse": {"description": "Prix encaissé", "type": "number", "format": "float", "example": 25.5}, "LT_MtHT": {"description": "Montant HT", "type": "number", "format": "float", "example": 51}, "LT_MtTTC": {"description": "Montant TTC", "type": "number", "format": "float", "example": 61.2}, "LT_PACHAT": {"description": "Prix d'achat", "type": "number", "format": "float", "example": 20}, "LT_Remise": {"description": "Remise appliquée", "type": "number", "format": "float", "example": 0}, "LT_NumLigne": {"description": "Numéro de ligne", "type": "integer", "example": 1}}, "type": "object"}, "Unite": {"description": "Unité de mesure", "properties": {"id": {"description": "Identifiant unique", "type": "string", "example": "123e4567-e89b-12d3-a456-426614174000"}, "code": {"description": "Code de l'unité", "type": "string", "example": "KG"}, "libelle": {"description": "Libellé de l'unité", "type": "string", "example": "Kilogramme"}, "active": {"description": "Statut actif", "type": "boolean", "example": true}, "isSync": {"description": "Statut de synchronisation", "type": "boolean", "example": false}, "created_at": {"type": "string", "format": "date-time", "example": "2024-01-01T10:00:00Z"}, "updated_at": {"type": "string", "format": "date-time", "example": "2024-01-01T10:00:00Z"}}, "type": "object"}, "Couleur": {"description": "Couleur d'article", "properties": {"id": {"description": "Identifiant unique", "type": "string", "example": "123e4567-e89b-12d3-a456-426614174000"}, "code": {"description": "Code de la couleur", "type": "string", "example": "ROUGE"}, "libelle": {"description": "Libell<PERSON> de <PERSON> couleur", "type": "string", "example": "Rouge"}, "active": {"description": "Statut actif", "type": "boolean", "example": true}, "isSync": {"description": "Statut de synchronisation", "type": "boolean", "example": false}, "created_at": {"type": "string", "format": "date-time", "example": "2024-01-01T10:00:00Z"}, "updated_at": {"type": "string", "format": "date-time", "example": "2024-01-01T10:00:00Z"}}, "type": "object"}, "Modele": {"description": "Mod<PERSON><PERSON> d'article", "properties": {"id": {"description": "Identifiant unique", "type": "string", "example": "123e4567-e89b-12d3-a456-426614174000"}, "code": {"description": "Code du modèle", "type": "string", "example": "MOD001"}, "libelle": {"description": "Libellé du modèle", "type": "string", "example": "Modèle Standard"}, "active": {"description": "Statut actif", "type": "boolean", "example": true}, "isSync": {"description": "Statut de synchronisation", "type": "boolean", "example": false}, "created_at": {"type": "string", "format": "date-time", "example": "2024-01-01T10:00:00Z"}, "updated_at": {"type": "string", "format": "date-time", "example": "2024-01-01T10:00:00Z"}}, "type": "object"}, "Cellule": {"description": "Cellule de stockage", "properties": {"id": {"description": "Identifiant unique", "type": "string", "example": "123e4567-e89b-12d3-a456-426614174000"}, "code": {"description": "Code de la cellule", "type": "string", "example": "CELL001"}, "libelle": {"description": "Libellé de la cellule", "type": "string", "example": "Cellule A1"}, "active": {"description": "Statut actif", "type": "boolean", "example": true}, "isSync": {"description": "Statut de synchronisation", "type": "boolean", "example": false}, "created_at": {"type": "string", "format": "date-time", "example": "2024-01-01T10:00:00Z"}, "updated_at": {"type": "string", "format": "date-time", "example": "2024-01-01T10:00:00Z"}}, "type": "object"}, "TypeArticle": {"description": "Type d'article", "properties": {"id": {"description": "Identifiant unique", "type": "string", "example": "123e4567-e89b-12d3-a456-426614174000"}, "code": {"description": "Code du type", "type": "string", "example": "PROD"}, "libelle": {"description": "Libellé du type", "type": "string", "example": "Produit"}, "active": {"description": "Statut actif", "type": "boolean", "example": true}, "isSync": {"description": "Statut de synchronisation", "type": "boolean", "example": false}, "created_at": {"type": "string", "format": "date-time", "example": "2024-01-01T10:00:00Z"}, "updated_at": {"type": "string", "format": "date-time", "example": "2024-01-01T10:00:00Z"}}, "type": "object"}, "Station": {"description": "Station/Point de vente", "properties": {"id": {"description": "Identifiant unique", "type": "string", "example": "123e4567-e89b-12d3-a456-426614174000"}, "code": {"description": "Code de la station", "type": "string", "example": "ST001"}, "libelle": {"description": "Libellé de la station", "type": "string", "example": "Station Principale"}, "active": {"description": "Statut actif", "type": "boolean", "example": true}, "alertQteTransfere": {"description": "Seuil d'alerte pour transfert", "type": "number", "format": "float", "example": 10}, "adresse": {"nullable": true, "description": "Adresse de la station", "type": "string", "example": "123 Rue de la Station"}, "telephone": {"nullable": true, "description": "Téléphone", "type": "string", "example": "+33123456789"}, "created_at": {"type": "string", "format": "date-time", "example": "2024-01-01T10:00:00Z"}, "updated_at": {"type": "string", "format": "date-time", "example": "2024-01-01T10:00:00Z"}}, "type": "object"}, "StationActive": {"description": "Station active (version simplifiée)", "properties": {"id": {"description": "Identifiant unique", "type": "string", "example": "123e4567-e89b-12d3-a456-426614174000"}, "libelle": {"description": "Libellé de la station", "type": "string", "example": "Station Principale"}, "alertQteTransfere": {"description": "Seuil d'alerte pour transfert", "type": "number", "format": "float", "example": 10}}, "type": "object"}, "Fournisseur": {"description": "Fournisseur (tiers)", "properties": {"id": {"description": "Identifiant unique", "type": "string", "example": "123e4567-e89b-12d3-a456-426614174000"}, "code": {"description": "Code du fournisseur", "type": "string", "example": "FOUR001"}, "libelle": {"description": "Libellé du fournisseur", "type": "string", "example": "Fournisseur ABC"}, "adresse": {"nullable": true, "description": "<PERSON><PERSON><PERSON>", "type": "string", "example": "123 Rue du Commerce"}, "telephone": {"nullable": true, "description": "Téléphone", "type": "string", "example": "+33123456789"}, "email": {"nullable": true, "description": "Email", "type": "string", "example": "<EMAIL>"}, "idTypeTier": {"description": "Type de tiers", "type": "string", "example": "TYPE001"}, "active": {"description": "Statut actif", "type": "boolean", "example": true}, "created_at": {"type": "string", "format": "date-time", "example": "2024-01-01T10:00:00Z"}, "updated_at": {"type": "string", "format": "date-time", "example": "2024-01-01T10:00:00Z"}}, "type": "object"}, "Inventaire": {"description": "Inventaire", "properties": {"id": {"description": "Identifiant unique", "type": "string", "example": "123e4567-e89b-12d3-a456-426614174000"}, "code": {"description": "Code de l'inventaire", "type": "string", "example": "INV001"}, "libelle": {"description": "Libellé de l'inventaire", "type": "string", "example": "Inventaire Janvier 2024"}, "dateInventaire": {"description": "Date de l'inventaire", "type": "string", "format": "date", "example": "2024-01-15"}, "P_codeExercice": {"description": "Code exercice", "type": "string", "example": "2024"}, "etat": {"description": "État de l'inventaire", "type": "string", "example": "EN_COURS"}, "created_at": {"type": "string", "format": "date-time", "example": "2024-01-01T10:00:00Z"}, "updated_at": {"type": "string", "format": "date-time", "example": "2024-01-01T10:00:00Z"}}, "type": "object"}, "EtatInventaire": {"description": "État d'inventaire", "properties": {"id": {"description": "Identifiant unique", "type": "string", "example": "123e4567-e89b-12d3-a456-426614174000"}, "code": {"description": "Code de l'état", "type": "string", "example": "EN_COURS"}, "libelle": {"description": "Libellé de l'état", "type": "string", "example": "En cours"}, "active": {"description": "Statut actif", "type": "boolean", "example": true}}, "type": "object"}, "Utilisateur": {"description": "Utilisateur du système", "properties": {"id": {"description": "Identifiant unique", "type": "string", "example": "123e4567-e89b-12d3-a456-426614174000"}, "login": {"description": "Login utilisateur", "type": "string", "example": "admin"}, "nom": {"description": "Nom de l'utilisateur", "type": "string", "example": "Administrateur"}, "prenom": {"nullable": true, "description": "Prénom de l'utilisateur", "type": "string", "example": "Système"}, "email": {"nullable": true, "description": "Email", "type": "string", "example": "<EMAIL>"}, "active": {"description": "Statut actif", "type": "boolean", "example": true}, "j_ddm": {"description": "Date de dernière modification", "type": "string", "format": "date-time", "example": "2024-01-01T10:00:00Z"}}, "type": "object"}, "SessionCaisse": {"description": "Session de caisse", "properties": {"SC_IdSCaisse": {"description": "Identifiant de la session", "type": "string", "example": "SC001"}, "SC_CodeCaisse": {"description": "Code de la caisse", "type": "string", "example": "CAISSE001"}, "SC_CodeUtilisateur": {"description": "Code utilisateur", "type": "string", "example": "USER001"}, "SC_CodeCarnet": {"description": "Code carnet", "type": "string", "example": "CARNET001"}, "SC_Station": {"description": "Code station", "type": "string", "example": "ST001"}, "SC_FondCaisse": {"description": "Fond de caisse", "type": "number", "format": "float", "example": 1000}, "SC_DateHeureCrea": {"description": "Date/heure de création", "type": "string", "format": "date-time", "example": "2024-01-01T08:00:00Z"}, "SC_DateHeureOuv": {"description": "Date/heure d'ouverture", "type": "string", "format": "date-time", "example": "2024-01-01T08:00:00Z"}, "SC_DateHeureClot": {"nullable": true, "description": "Date/heure de clôture", "type": "string", "format": "date-time", "example": "2024-01-01T18:00:00Z"}, "SC_ClotCaisse": {"description": "Statut de clôture (0=ouverte, 1=fermée)", "type": "integer", "example": 0}, "SC_NomMachine": {"description": "Nom de la machine", "type": "string", "example": "MACHINE001"}, "SC_TotalVente": {"description": "Total des ventes", "type": "number", "format": "float", "example": 2500}, "SC_TotalEncaisse": {"description": "Total encaissé", "type": "number", "format": "float", "example": 2500}}, "type": "object"}, "Caisse": {"description": "Caisse ProCaisse", "properties": {"CAI_Code": {"description": "Code de la caisse", "type": "string", "example": "CAISSE001"}, "CAI_Designation": {"description": "Désignation de la caisse", "type": "string", "example": "Caisse Principale"}, "CAI_Station": {"description": "Code station", "type": "string", "example": "ST001"}, "CAI_User": {"description": "Code utilisateur", "type": "string", "example": "USER001"}, "CAI_DDm": {"description": "Date de dernière modification", "type": "string", "format": "date-time", "example": "2024-01-01T10:00:00Z"}, "CAI_export": {"nullable": true, "description": "Statut d'export", "type": "string", "example": "Y"}, "CAI_Etat": {"description": "État de la caisse", "type": "string", "example": "Actif"}}, "type": "object"}, "FamilleMobile": {"description": "Famille d'articles mobile", "properties": {"FAM_Code": {"description": "Code de la famille", "type": "string", "example": "FAM001"}, "FAM_Lib": {"description": "Libellé de la famille", "type": "string", "example": "Électronique"}, "FAM_User": {"nullable": true, "description": "Code utilisateur", "type": "string", "example": "USER001"}, "FAM_Station": {"nullable": true, "description": "Code station", "type": "string", "example": "ST001"}, "FAM_IsTaktile": {"nullable": true, "description": "Famille tactile", "type": "string", "example": "Y"}, "FAM_Couleur": {"nullable": true, "description": "Couleur de la famille", "type": "string", "example": "#FF0000"}, "FAM_DesgCourte": {"nullable": true, "description": "Désignation courte", "type": "string", "example": "Elec"}, "FAM_NumOrdre": {"nullable": true, "description": "Numéro d'ordre", "type": "integer", "example": 1}, "FAM_export": {"nullable": true, "description": "Statut d'export", "type": "string", "example": "Y"}, "FAM_DDm": {"description": "Date de dernière modification", "type": "string", "format": "date-time", "example": "2024-01-01T10:00:00Z"}, "photo_Path": {"nullable": true, "description": "<PERSON><PERSON><PERSON> de <PERSON>", "type": "string", "example": "/images/famille_001.jpg"}, "FAM_CodeM": {"nullable": true, "description": "Code mobile", "type": "string", "example": "MOBILE001"}}, "type": "object"}, "ArticleCodeBar": {"description": "Code à barres d'article", "properties": {"Parent_CodeBar": {"description": "Code à barres parent", "type": "string", "example": "PARENT001"}, "Fils_CodeBar": {"description": "Code à barres fils", "type": "string", "example": "FILS001"}, "cod_b_user": {"nullable": true, "description": "Code utilisateur", "type": "string", "example": "USER001"}, "cod_b_station": {"nullable": true, "description": "Code station", "type": "string", "example": "ST001"}, "cod_b_export": {"nullable": true, "description": "Statut d'export", "type": "string", "example": "Y"}, "cod_b_DDm": {"nullable": true, "description": "Date de dernière modification", "type": "string", "format": "date-time", "example": "2024-01-01T10:00:00Z"}}, "type": "object"}, "ArticleCodeBarSimple": {"description": "Code à barres d'article (version simplifiée)", "properties": {"Parent_CodeBar": {"description": "Code à barres parent", "type": "string", "example": "PARENT001"}, "Fils_CodeBar": {"description": "Code à barres fils", "type": "string", "example": "FILS001"}, "cod_b_user": {"nullable": true, "description": "Code utilisateur", "type": "string", "example": "USER001"}, "cod_b_station": {"nullable": true, "description": "Code station", "type": "string", "example": "ST001"}}, "type": "object"}, "ArticleCodeBarInput": {"description": "Données d'entrée pour un code à barres d'article", "properties": {"Parent_CodeBar": {"description": "Code à barres parent", "type": "string", "example": "PARENT001"}, "Fils_CodeBar": {"description": "Code à barres fils", "type": "string", "example": "FILS001"}, "cod_b_user": {"nullable": true, "description": "Code utilisateur", "type": "string", "example": "USER001"}, "cod_b_station": {"nullable": true, "description": "Code station", "type": "string", "example": "ST001"}, "cod_b_export": {"nullable": true, "description": "Statut d'export (peut être 'NULL')", "type": "string", "example": "Y"}, "cod_b_DDm": {"nullable": true, "description": "Date de dernière modification", "type": "string", "format": "date-time", "example": "2024-01-01T10:00:00Z"}}, "type": "object"}, "ArticleInput": {"description": "Données d'entrée pour un article", "properties": {"ART_Code": {"description": "Code de l'article", "type": "string", "example": "ART001"}, "ART_Designation": {"description": "Désignation de l'article", "type": "string", "example": "Article de test"}, "ART_PrixUnitaireHT": {"description": "Prix unitaire HT", "type": "number", "format": "float", "example": 25.5}, "ART_TVA": {"description": "Taux de TVA", "type": "number", "format": "float", "example": 20}, "ART_Famille": {"nullable": true, "description": "Code famille", "type": "string", "example": "FAM001"}, "ART_Marque": {"nullable": true, "description": "Code marque", "type": "string", "example": "MAR001"}, "ART_CodeBar": {"nullable": true, "description": "Code à barres", "type": "string", "example": "1234567890123"}, "ART_QteStock": {"description": "Quantité en stock", "type": "number", "format": "float", "example": 100}, "photo_Path": {"nullable": true, "description": "<PERSON><PERSON><PERSON> de <PERSON>", "type": "string", "example": "/images/article_001.jpg"}}, "type": "object"}, "Banque": {"description": "Banque ProCaisse", "properties": {"BAN_Code": {"description": "Code de la banque", "type": "string", "example": "BAN001"}, "BAN_Designation": {"description": "Désignation de la banque", "type": "string", "example": "Banque Populaire"}, "BAN_Adresse": {"nullable": true, "description": "<PERSON><PERSON><PERSON> de la banque", "type": "string", "example": "123 Rue de la Banque"}, "BAN_Telephone": {"nullable": true, "description": "Téléphone", "type": "string", "example": "+33123456789"}, "BAN_Email": {"nullable": true, "description": "Email", "type": "string", "example": "<EMAIL>"}, "BAN_RIB": {"nullable": true, "description": "RIB", "type": "string", "example": "12345678901234567890"}, "BAN_Station": {"nullable": true, "description": "Code station", "type": "string", "example": "ST001"}, "BAN_User": {"nullable": true, "description": "Code utilisateur", "type": "string", "example": "USER001"}}, "type": "object"}, "LicenseUrl": {"description": "URL de service de licence", "properties": {"id": {"description": "Identifiant", "type": "integer", "example": 1}, "name": {"description": "Nom du service", "type": "string", "example": "Service Principal"}, "url": {"description": "URL du service", "type": "string", "example": "https://license.procaisse.com"}, "active": {"description": "Statut actif", "type": "boolean", "example": true}, "description": {"nullable": true, "description": "Description", "type": "string", "example": "Service principal de licences"}}, "type": "object"}, "BonCommande": {"description": "<PERSON> de commande (Devi<PERSON>)", "properties": {"DEV_Code": {"description": "Code du devis", "type": "string", "example": "DEV001"}, "DEV_CodeClient": {"description": "Code client", "type": "string", "example": "CLI001"}, "DEV_Date": {"description": "Date du devis", "type": "string", "format": "date-time", "example": "2024-01-01T10:00:00Z"}, "DEV_MontantHT": {"description": "Montant HT", "type": "number", "format": "float", "example": 1000}, "DEV_MontantTTC": {"description": "Montant TTC", "type": "number", "format": "float", "example": 1200}, "DEV_MontantTVA": {"description": "Montant TVA", "type": "number", "format": "float", "example": 200}, "DEV_Etat": {"description": "État du devis", "type": "string", "example": "EN_COURS"}, "DEV_Station": {"description": "Code station", "type": "string", "example": "ST001"}, "DEV_User": {"description": "Code utilisateur", "type": "string", "example": "USER001"}}, "type": "object"}}, "securitySchemes": {"bearerAuth": {"type": "http", "description": "Authentification par token Bearer JWT", "bearerFormat": "JWT", "scheme": "bearer"}}}, "tags": [{"name": "Article Classe Remise", "description": "Gestion des classes de remise des articles"}, {"name": "Article Code Barres Mobile", "description": "Gestion des codes à barres d'articles depuis l'application mobile ProCaisse"}, {"name": "Articles Mobile", "description": "Gestion des articles depuis l'application mobile ProCaisse"}, {"name": "Banques Mobile", "description": "Gestion des banques depuis l'application mobile ProCaisse"}, {"name": "Configuration Base", "description": "Gestion de la configuration de base et des licences ProCaisse"}, {"name": "Bons de Commande Mobile", "description": "Gestion des bons de commande depuis l'application mobile ProCaisse"}, {"name": "Caisses Mobile", "description": "Gestion des caisses et consultations depuis l'application mobile ProCaisse"}, {"name": "Clients Mobile", "description": "Gestion des clients depuis l'application mobile ProCaisse"}, {"name": "Familles Mobile", "description": "Gestion des familles d'articles depuis l'application mobile ProCaisse"}, {"name": "Types Article DuxInventory", "description": "Gestion des types d'articles dans le module DuxInventory"}, {"name": "Fournisseurs DuxInventory", "description": "Gestion des fournisseurs dans le module DuxInventory"}, {"name": "Inventaires DuxInventory", "description": "Gestion des inventaires dans le module DuxInventory"}, {"name": "Articles DuxInventory", "description": "Gestion des articles dans le module DuxInventory"}, {"name": "Cellules DuxInventory", "description": "Gestion des cellules de stockage dans le module DuxInventory"}, {"name": "Couleurs DuxInventory", "description": "Gestion des couleurs d'articles dans le module DuxInventory"}, {"name": "Familles DuxInventory", "description": "Gestion des familles d'articles dans le module DuxInventory"}, {"name": "<PERSON><PERSON>", "description": "Gestion des marques dans le module DuxInventory"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Gestion des modèles d'articles dans le module DuxInventory"}, {"name": "Stations DuxInventory", "description": "Gestion des stations/points de vente dans le module DuxInventory"}, {"name": "Unités DuxInventory", "description": "Gestion des unités de mesure dans le module DuxInventory"}, {"name": "Utilisateurs DuxInventory", "description": "Gestion des utilisateurs et authentification dans le module DuxInventory"}, {"name": "Marques Mobile", "description": "Gestion des marques depuis l'application mobile"}, {"name": "Sessions Caisse Mobile", "description": "Gestion des sessions de caisse depuis l'application mobile ProCaisse"}, {"name": "Stations Mobile", "description": "Gestion des stations/points de vente depuis l'application mobile ProCaisse"}, {"name": "Tai<PERSON>", "description": "Gestion des tailles d'articles"}, {"name": "Tickets Mobile", "description": "Gestion des tickets de vente depuis l'application mobile ProCaisse"}, {"name": "Unités Mobile", "description": "Gestion des unités de mesure depuis l'application mobile ProCaisse"}]}