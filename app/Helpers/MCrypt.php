<?php

namespace App\Helpers;

use App\Http\Controllers\Controller;


class MCrypt extends Controller
{
    /*  private $iv = 'BLtAeDqI7JCTSpWj';
      private $key = 'kqRD81UPVT9zFtb';
  */
    private $iv = '4MhdztfAsMLm9Bso';
    private $key = 'Bh4m6jaSm9ChR5cv';


    public  function encrypt($str)
    {

        //$key = $this->hex2bin($key);
        $iv = $this->iv;

        $td = mcrypt_module_open('rijndael-128', '', 'cbc', $iv);

        mcrypt_generic_init($td, $this->key, $iv);
        $encrypted = mcrypt_generic($td, $str);

        mcrypt_generic_deinit($td);
        mcrypt_module_close($td);

        return bin2hex($encrypted);
    }

    public static function decrypt($code)
    {
        //$key = $this->hex2bin($key);
        $code = self::hex2bin($code);
        $iv = '4MhdztfAsMLm9Bso';
        $key = 'Bh4m6jaSm9ChR5cv';
        $td = mcrypt_module_open('rijndael-128', '', 'cbc', $iv);

        mcrypt_generic_init($td, $key, $iv);
        $decrypted = mdecrypt_generic($td, $code);

        mcrypt_generic_deinit($td);
        mcrypt_module_close($td);

        return utf8_encode(trim($decrypted));
    }

    public static function hex2bin($hexdata)
    {
        $bindata = '';

        for ($i = 0; $i < strlen($hexdata); $i += 2) {
            $bindata .= chr(hexdec(substr($hexdata, $i, 2)));
        }

        return $bindata;
    }

}