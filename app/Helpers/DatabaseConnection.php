<?php

namespace App\Helpers;

use App\Http\Controllers\Controller;
use Illuminate\Database\ConnectionInterface;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class DatabaseConnection extends Controller
{
    /**
     * @throws \Exception
     */
    public static function setConnection($data): ConnectionInterface
    {

        //(bool)env
        $DB_FROM_ENV = false;

        $params = !!$data && is_array($data) && sizeof($data) > 0 ? $data["connexion"] ?? null : null;
        Artisan::call('cache:gc');
        Artisan::call('optimize:clear');
        $port='1433';
        $host=MCrypt::decrypt($params["dbIpAddress"]);
        if( str_contains($host, '/') || str_contains($host, '\\') ){
            $port='';
        }


        Config::set('database.connections.onthefly.host', !$DB_FROM_ENV ? MCrypt::decrypt($params["dbIpAddress"]) : env("DB_HOST"));
        Config::set('database.connections.onthefly.port', !$DB_FROM_ENV ? $port : env("DB_PORT"));
        Config::set('database.connections.onthefly.database', !$DB_FROM_ENV ? MCrypt::decrypt($params["dbName"]) : env("DB_DATABASE"));
        Config::set('database.connections.onthefly.username', !$DB_FROM_ENV ? MCrypt::decrypt($params["username"]) : env("DB_USERNAME"));
        Config::set('database.connections.onthefly.password', !$DB_FROM_ENV ? MCrypt::decrypt($params["password"]) : env("DB_PASSWORD"));
        // Config::set('database.connections.onthefly.encrypt', 'yes');
        // Config::set('database.connections.onthefly.trust_server_certificate', 'true');
        // Rearrange the connection data
        DB::reconnect('onthefly');
        // Ping the database. This will thrown an exception in case
        // the database does not exists.
        try {
            Schema::connection('onthefly')->getConnection()->reconnect();
        } catch (\Exception $ex) {
            $message = $ex->getMessage();
            throw new \Exception($message);
        }

        return DB::connection('onthefly')->setQueryGrammar(new SqlServerGrammar());

    }


    static function getDefaultConfig()
    {
        config(['database.connections.onthefly' => [
            'driver' => 'sqlsrv',
            'host' => env("DB_HOST"),
            'port' => env("DB_PORT"),
            'database' => env("DB_DATABASE"),
            'username' => env("DB_USERNAME"),
            'password' => env("DB_PASSWORD"),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
        ]]);
    }
}
