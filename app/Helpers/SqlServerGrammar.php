<?php


namespace App\Helpers;

use Illuminate\Database\Query\Builder;
use Illuminate\Database\Query\Grammars\SqlServerGrammar as GrammarsBase;

class SqlServerGrammar extends GrammarsBase
{
    public function getLanguage(): string
    {
            return "Set Language French  SET DATEFORMAT 'ymd' ";
    }

    public function compileInsert(Builder $query, array $values)
    {
        return $this->getLanguage() . parent::compileInsert($query, $values); // TODO: Change the autogenerated stub
    }

    public function compileSelect(Builder $query)
    {
        return $this->getLanguage() . parent::compileSelect($query); // TODO: Change the autogenerated stub
    }

    public function compileUpdate(Builder $query, $values)
    {
        return $this->getLanguage() . parent::compileUpdate($query, $values); // TODO: Change the autogenerated stub
    }

}