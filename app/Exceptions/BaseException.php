<?php

namespace App\Exceptions;

use App\Enums\ExceptionType;
use Exception;
use Illuminate\Support\Facades\Lang;
use Symfony\Component\CssSelector\Exception\InternalErrorException;

/**
 * Class BaseException
 *
 * Abstract Exception class that all of the application specific exceptions will extend from. This will make it
 * easy to catch all of the application specific exceptions and provides a clean separation from the other
 * potential exceptions that may be thrown during the application’s execution.
 *
 * @package App\Exceptions
 *
 */
abstract class BaseException extends Exception
{
    /** @var int */
    protected $status = 402;

    /** @var string */
    protected $title;

    /** @var string */
    protected $detail;

    /** @var string */
    protected $type;

    /** @var string */
    protected $instance;

    /** @var string */
    protected $code = 000000;

    /** @var array */
    protected $additionalDetail;


    /**
     * BaseException constructor.
     * @param string $title
     * @param string $detail
     * @param int $code
     */
    public function __construct(string $title, string $detail, int $code, int $status)
    {
        if (!is_scalar($title)) {
            $this->title = Lang::has('exceptions.' . strtolower($title)) ? Lang::get('exceptions.' . strtolower($title)) : $title;
        }
        $this->detail = $detail;
        $this->code = $code ?: $this->code;
        $this->status = $status ?: $this->status;
        parent::__construct($title, $this->code);
    }


    /**
     * Get the status
     *
     * @return int
     */
    public function getStatus()
    {
        return (int)$this->status;
    }

    /**
     * @param $status
     *
     * @return $this
     */
    public function setStatus($status)
    {
        $this->status = $status;

        return $this;
    }

    /**
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * @param $title
     *
     * @return $this
     */
    public function setTitle($title)
    {
        $this->title = $title;

        return $this;
    }

    /**
     * @return string
     */
    public function getDetail()
    {
        return $this->detail;
    }

    /**
     * @param $detail
     *
     * @return $this
     */
    public function setDetail($detail)
    {
        $this->detail = $detail;

        return $this;
    }

    /**
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * @param $type
     *
     * @return $this
     */
    public function setType($type)
    {
        $this->type = $type;

        return $this;
    }

    /**
     * @return string
     */
    public function getInstance()
    {
        return $this->instance;
    }

    /**
     * @param $instance
     *
     * @return $this
     */
    public function setInstance($instance)
    {
        $this->instance = $instance;

        return $this;
    }

    /**
     * @param array $additionalDetail
     *
     * @return $this
     * @throws InternalErrorException
     */
    public function setAdditional($additionalDetail)
    {
        if (!is_array($additionalDetail)) {
            throw new InternalErrorException('Additional detail must be array');
        }

        $this->additionalDetail = $additionalDetail;

        return $this;
    }

    /**
     * Fails with the current exception object
     *
     * @throws BaseException
     */
    public function toss()
    {
        throw $this;
    }

    /**
     * @param int $code
     */
    public function setCode(int $code): void
    {
        $this->code = $code;
    }


    /**
     * Return the Exception as an array
     *
     * @return array
     */
    public function toArray()
    {
        $error = [
            'status' => $this->status,
            'title' => $this->title,
            'detail' => $this->detail,
            'code' => $this->code,
            'type' => $this->type ?: 'https://www.w3.org/Protocols/rfc2616/rfc2616-sec10.html',
            'instance' => $this->instance,
        ];

        $error = array_merge($error, $this->additionalDetail ?: []);

        return array_filter($error);
    }
}
