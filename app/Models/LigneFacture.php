<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property integer $LG_FACT_NumOrdre
 * @property string $LG_FACT_NumFact
 * @property string $LG_FACT_Exerc
 * @property string $LG_FACT_NumPiece
 * @property string $LG_FACT_TypePiece
 * @property string $LG_FACT_ExercPiece
 * @property string $LG_FACT_CodeLigne
 * @property string $LG_FACT_DesgLigne
 * @property float $LG_FACT_PUHT
 * @property float $LG_FACT_Qte
 * @property float $LG_FACT_TauxRemise
 * @property float $LG_FACT_MntRemise
 * @property float $LG_FACT_MntNetHt
 * @property float $LG_FACT_TauxTva
 * @property float $LG_FACT_MntTva
 * @property float $LG_FACT_PUTTC
 * @property float $LG_FACT_MntTTC
 * @property float $LG_FACT_PrixPub
 * @property float $LG_FACT_MntHT
 * @property string $LG_FACT_Etat
 * @property string $LG_FACT_Typeligne
 * @property string $LG_FACT_CodeUnit
 * @property string $LG_FACT_UniDesg
 * @property string $LG_FACT_user
 * @property string $LG_FACT_station
 * @property boolean $LG_FACT_export
 * @property string $LG_FACT_DDm
 * @property float $LG_FACT_Mnt_Fodec
 * @property float $LG_FACT_Fodec
 * @property float $LG_FACT_DC
 * @property float $LG_FACT_Mnt_DC
 * @property string $LG_FACT_BE
 * @property string $LG_FACT_BE_Exercice
 * @property string $LG_Fact_NumAvoir
 * @property string $LG_Fact_ExerciceAvoir
 * @property float $LG_QteAvoir
 * @property int $LG_Fact_OrdreAvoir
 * @property float $LG_Fact_MntHTVA
 * @property boolean $LG_Fact_Retour
 * @property float $LG_Fact_Qte_Retour
 * @property float $CR_Article
 * @property float $CMP_Article
 * @property string $LG_NumSerie
 */
class LigneFacture extends Model
{
    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'ligne_facture';

    /**
     * @var array
     */
    protected $fillable = ['LG_FACT_NumPiece', 'LG_FACT_TypePiece', 'LG_FACT_ExercPiece', 'LG_FACT_CodeLigne', 'LG_FACT_DesgLigne', 'LG_FACT_PUHT', 'LG_FACT_Qte', 'LG_FACT_TauxRemise', 'LG_FACT_MntRemise', 'LG_FACT_MntNetHt', 'LG_FACT_TauxTva', 'LG_FACT_MntTva', 'LG_FACT_PUTTC', 'LG_FACT_MntTTC', 'LG_FACT_PrixPub', 'LG_FACT_MntHT', 'LG_FACT_Etat', 'LG_FACT_Typeligne', 'LG_FACT_CodeUnit', 'LG_FACT_UniDesg', 'LG_FACT_user', 'LG_FACT_station', 'LG_FACT_export', 'LG_FACT_DDm', 'LG_FACT_Mnt_Fodec', 'LG_FACT_Fodec', 'LG_FACT_DC', 'LG_FACT_Mnt_DC', 'LG_FACT_BE', 'LG_FACT_BE_Exercice', 'LG_Fact_NumAvoir', 'LG_Fact_ExerciceAvoir', 'LG_QteAvoir', 'LG_Fact_OrdreAvoir', 'LG_Fact_MntHTVA', 'LG_Fact_Retour', 'LG_Fact_Qte_Retour', 'CR_Article', 'CMP_Article', 'LG_NumSerie'];
}
