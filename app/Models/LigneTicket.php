<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property Article $article
 * @property float $LT_NumTicket
 * @property string $LT_IdCarnet
 * @property string $LT_Exerc
 * @property string $LT_CodArt
 * @property float $LT_PrixVente
 * @property float $LT_Remise
 * @property float $LT_TVA
 * @property float $LT_MtHT
 * @property float $LT_MtTTC
 * @property string $LT_Unite
 * @property float $LT_Qte
 * @property boolean $LT_Annuler
 * @property int $LT_NumOrdre
 * @property int $LT_QtePiece
 * @property float $LT_PrixEncaisse
 * @property float $LT_PACHAT
 * @property string $LT_Tarif
 * @property string $LT_user
 * @property string $LT_station
 * @property boolean $LT_export
 * @property string $LT_DDm
 * @property float $LT_QteFacturee
 * @property float $LT_Pachat_Res
 * @property float $LT_Pachat_PrixFacturee
 * @property string $LT_NumFacture
 * @property string $LT_ExercFacture
 * @property int $LT_Ordre
 * @property string $LT_BonEntree
 * @property string $LT_BonEntreeExerc
 * @property float $CMP_Globale
 * @property float $LT_Taux_Remise
 * @property string $LT_Commentaire
 * @property boolean $LT_Commande
 * @property float $LT_PACHATTC
 * @property string $NumSerie
 * @property string $DD_Garantie
 * @property string $DF_Garantie
 * @property string $Observation_Garantie
 * @property string $LT_NumeroBL
 * @property float $LT_Ordresupp
 */
class LigneTicket extends Model
{
    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'LigneTicket';

    /**
     * @var array
     */
    protected $fillable = ['LT_PrixVente', 'LT_Remise', 'LT_TVA', 'LT_MtHT', 'LT_MtTTC', 'LT_Qte', 'LT_Annuler', 'LT_NumOrdre', 'LT_QtePiece', 'LT_PrixEncaisse', 'LT_PACHAT', 'LT_Tarif', 'LT_user', 'LT_station', 'LT_export', 'LT_DDm', 'LT_QteFacturee', 'LT_Pachat_Res', 'LT_Pachat_PrixFacturee', 'LT_NumFacture', 'LT_ExercFacture', 'LT_Ordre', 'LT_BonEntree', 'LT_BonEntreeExerc', 'CMP_Globale', 'LT_Taux_Remise', 'LT_Commentaire', 'LT_Commande', 'LT_PACHATTC', 'NumSerie', 'DD_Garantie', 'DF_Garantie', 'Observation_Garantie', 'LT_NumeroBL', 'LT_Ordresupp'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function article()
    {
        return $this->belongsTo('App\Models\article', 'LT_CodArt', 'ART_Code');
    }

}
