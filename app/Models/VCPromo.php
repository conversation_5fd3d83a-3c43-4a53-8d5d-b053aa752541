<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property string $CodeVCPromo
 * @property string $CodeVCPromoM
 * @property string $CodeArtLocal
 * @property string $ArticleConcur
 * @property string $DateOp
 * @property string $CodeConcur
 * @property string $NoteOp
 * @property float $PrixConcur
 * @property float $TauxPromo
 * @property int $CodeUser
 * @property string $InfoOp1
 * @property string $CodeTypeCom
 */
class VCPromo extends Model
{
    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'VCPromo';

    /**
     * The primary key for the model.
     * 
     * @var string
     */
    protected $primaryKey = 'CodeVCPromo';

    /**
     * The "type" of the auto-incrementing ID.
     * 
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     * 
     * @var bool
     */
    public $incrementing = false;

    /**
     * @var array
     */
    protected $fillable = ['CodeVCPromoM', 'CodeArtLocal', 'ArticleConcur', 'DateOp', 'CodeConcur', 'NoteOp', 'PrixConcur', 'TauxPromo'
        , 'CodeUser', 'InfoOp1', 'CodeTypeCom'];

    public function VC_Image()
    {
        $this->hasMany(VCImage::class);
    }
}
