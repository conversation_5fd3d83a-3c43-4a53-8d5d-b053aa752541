<?php

namespace App\Models;

use App\Helpers\DB;
use Illuminate\Database\Eloquent\Model;

/**
 * @property SessionCaisse $sessionCaisse
 * @property float $TIK_NumTicket
 * @property string $TIK_Exerc
 * @property string $TIK_IdCarnet
 * @property string $TIK_DateHeureTicket
 * @property string $TIK_IdSCaisse
 * @property string $TIK_CodClt
 * @property float $TIK_NumFact
 * @property float $TIK_Regler
 * @property float $TIK_MtTTC
 * @property float $TIK_MtRemise
 * @property float $TIK_MtHT
 * @property float $TIK_MtTVA
 * @property string $TIK_TypeTiket
 * @property float $TIK_MtEspece
 * @property float $TIK_MtCheque
 * @property string $TIK_NumCheque
 * @property string $TIK_Echeance
 * @property float $TIK_MtCarteBanq
 * @property float $TIK_Mtrecue
 * @property float $TIK_Mtrendue
 * @property string $TIK_Etat
 * @property string $TIK_DateLivraison
 * @property string $TIK_NomClient
 * @property string $TIK_user
 * @property string $TIK_station
 * @property boolean $TIK_export
 * @property string $TIK_DDm
 * @property boolean $TIK_Annuler
 * @property string $TIK_CODE_COMMERCIAL
 * @property string $TIK_DESIG_COMMERCIAL
 * @property boolean $TIK_is_Contrat
 * @property string $TIK_Num_Contrat
 * @property string $TIK_Date_Mariage
 * @property string $TIK_Emplacement_Mariage
 * @property string $TIK_Contrat_Champ1
 * @property string $TIK_Contrat_Champ2
 * @property float $TIK_Nbre_Pts_Gain
 * @property float $TIK_Nbre_Total_Pts
 * @property string $TIK_Num_Carte
 * @property float $TIK_Mnt_Bonus
 * @property float $TIK_TauxRemise
 * @property string $TIK_NumeroBL
 * @property boolean $TIK_EnvWebServ
 */
class Ticket extends Model
{
    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'Ticket';

    /**
     * @var array
     */
    protected $fillable = ['TIK_DateHeureTicket', 'TIK_IdSCaisse', 'TIK_CodClt', 'TIK_NumFact', 'TIK_Regler', 'TIK_MtTTC', 'TIK_MtRemise', 'TIK_MtHT', 'TIK_MtTVA', 'TIK_TypeTiket', 'TIK_MtEspece', 'TIK_MtCheque', 'TIK_NumCheque', 'TIK_Echeance', 'TIK_MtCarteBanq', 'TIK_Mtrecue', 'TIK_Mtrendue', 'TIK_Etat', 'TIK_DateLivraison', 'TIK_NomClient', 'TIK_user', 'TIK_station', 'TIK_export', 'TIK_DDm', 'TIK_Annuler', 'TIK_CODE_COMMERCIAL', 'TIK_DESIG_COMMERCIAL', 'TIK_is_Contrat', 'TIK_Num_Contrat', 'TIK_Date_Mariage', 'TIK_Emplacement_Mariage', 'TIK_Contrat_Champ1', 'TIK_Contrat_Champ2', 'TIK_Nbre_Pts_Gain', 'TIK_Nbre_Total_Pts', 'TIK_Num_Carte', 'TIK_Mnt_Bonus', 'TIK_TauxRemise', 'TIK_NumeroBL', 'TIK_EnvWebServ'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function sessionCaisse()
    {
        return $this->belongsTo('App\Models\SessionCaisse', 'TIK_IdSCaisse', 'SC_IdSCaisse');
    }

    public static function SoldeClient($connection,$codeClient)
    {
        $soldeClient =  $connection->table('View_SoldeClient')
            ->select(DB::raw("SUM(isnull(Credit,0)) as Credit")  ,
                DB::raw("SUM(isnull(Debit,0)) as Debit"),
                DB::raw('SUM(isnull(Debit,0)) - SUM(isnull(Credit,0)) as SoldeClient'))
            ->where('CodeClient','=',$codeClient)->First();

        return [
            "SoldeClient"=>$soldeClient->SoldeClient
            ,"Debit"=>$soldeClient->Debit,
            "Credit"=>$soldeClient->Credit
        ];

    }

}
