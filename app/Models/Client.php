<?php

namespace App\Models;

use App\Enums\AuthorizationUser;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;


class Client extends Model
{


    public static function fillables(): array
    {


        $fillableColumns = [
            "CLI_Code", "CLI_NomPren", "CLI_MatFisc", "CLI_Date_Cre", "CLI_Industrielle", "CLI_Fodec",
            "CLI_DC", "CLI_Exonoration", "CLI_Timbre", "CLI_Solde", "CLI_Type", "CLI_Etat", "CLI_TauxRemGlob", "CLI_Localite",
            "CLI_Adresse", "CLI_Ville", "CLI_CodePostale", "CLI_Tel1", "CLI_Tel2", "CLI_Fax", "CLI_mail", "CLI_SiteWeb",
            "CLI_Obs", "CLI_Station", "CLI_User", "CLI_Pers_Contact", "CLI_PC_Tel1", "CLI_PC_Tel2", "CLI_PC_Email", "CLI_Exo_Num",
            "CLI_Exo_Valable", "CLI_Type_Reg", "CLI_Exeno", "CLI_ValiditeTraite", "CLI_CodeRep", "CLI_export", "CLI_DDm", "CLI_Forfetaire",
            "Cli_Debit", "Cli_Credit", "Cli_EX", "Num_Fidelite", "Clt_Fidelite", "Clt_Epoux", "Clt_Epouse", "Clt_Epouse_Tel", "Clt_Epoux_Tel",
            "Clt_CIN_ville", "Clt_CIN_Date", "Clt_CIN", "avec_qui_convention", "dateNaissance", "datePA", "PrefixPhone", "Clt_Latitude",
            "Clt_Longitude", "Clt_Couleur", "Clt_Info1", "Clt_Info2", "Clt_Info3", "Clt_Info4", "Clt_Info5", "Clt_PersMorale", "Clt_PersPysiq", "Clt_Circuit",
            "Clt_CodCompta", "CLI_exportM", "CLI_DDmM", "CLI_isCredit", "CLI_MaxCredit", "CLI_isPGros", "CLI_PGros", "CLI_isRemise",
            "CLI_Remise", "CLI_Code_M", "CLI_Tarif", "Clt_Info6", "Clt_Info7", "Clt_Info8", "Clt_Info9", "Clt_Info10",
            DB::raw("SUM(isnull(Credit,0)) as Credit"), DB::raw("SUM(isnull(Debit,0)) as Debit"),
            DB::raw("SUM(isnull(Debit,0)) - SUM(isnull(Credit,0)) as Solde"),

        ];

        if (Schema::connection("onthefly")->hasColumn('client', 'Clt_MntRevImp')) {
            $fillableColumns[] = 'Clt_MntRevImp';
        }

        return $fillableColumns;

    }

    public static function groupByColumns(): array
    {

        $groupByColumns = [
            "CLI_Code", "CLI_NomPren", "CLI_MatFisc", "CLI_Date_Cre", "CLI_Industrielle", "CLI_Fodec",
            "CLI_DC", "CLI_Exonoration", "CLI_Timbre", "CLI_Solde", "CLI_Type", "CLI_Etat", "CLI_TauxRemGlob", "CLI_Localite",
            "CLI_Adresse", "CLI_Ville", "CLI_CodePostale", "CLI_Tel1", "CLI_Tel2", "CLI_Fax", "CLI_mail", "CLI_SiteWeb",
            "CLI_Obs", "CLI_Station", "CLI_User", "CLI_Pers_Contact", "CLI_PC_Tel1", "CLI_PC_Tel2", "CLI_PC_Email", "CLI_Exo_Num",
            "CLI_Exo_Valable", "CLI_Type_Reg", "CLI_Exeno", "CLI_ValiditeTraite", "CLI_CodeRep", "CLI_export", "CLI_DDm", "CLI_Forfetaire",
            "Cli_Debit", "Cli_Credit", "Cli_EX", "Num_Fidelite", "Clt_Fidelite", "Clt_Epoux", "Clt_Epouse", "Clt_Epouse_Tel", "Clt_Epoux_Tel",
            "Clt_CIN_ville", "Clt_CIN_Date", "Clt_CIN", "avec_qui_convention", "dateNaissance", "datePA", "PrefixPhone", "Clt_Latitude",
            "Clt_Longitude", "Clt_Couleur", "Clt_Info1", "Clt_Info2", "Clt_Info3", "Clt_Info4", "Clt_Info5", "Clt_PersMorale", "Clt_PersPysiq", "Clt_Circuit",
            "Clt_CodCompta", "CLI_exportM", "CLI_DDmM", "CLI_isCredit", "CLI_MaxCredit", "CLI_isPGros", "CLI_PGros", "CLI_isRemise",
            "CLI_Remise", "CLI_Code_M", "CLI_Tarif", "Clt_Info6", "Clt_Info7", "Clt_Info8", "Clt_Info9", "Clt_Info10"
        ];

        if (Schema::connection("onthefly")->hasColumn('client', 'Clt_MntRevImp')) {
            $groupByColumns[] = 'Clt_MntRevImp';
        }

        return $groupByColumns;



    }
    public static function authorizationsAllClients($idUser, $connexion)
    {
        return $connexion->table('AutorisationUser')
            ->where('AutoCodeUt', '=', $idUser)
            ->where('AutoCodeAu', '=', AuthorizationUser::authorizationAllClients)
            ->pluck('AutEtat')->first();
    }

    public static function authorizationsAllClientsByStation($idUser, $connexion)
    {
        return $connexion->table('AutorisationUser')
            ->where('AutoCodeUt', '=', $idUser)
            ->where('AutoCodeAu', '=', AuthorizationUser::authorizationClientByStation)
            ->pluck('AutEtat')->first();
    }

    public static function authorizationsAllClientsByZone($idUser, $connexion)
    {
        return $connexion->table('AutorisationUser')
            ->where('AutoCodeUt', '=', $idUser)
            ->where('AutoCodeAu', '=', AuthorizationUser::authorizationClientByZone)
            ->pluck('AutEtat')->first();
    }

    public static function getZonesUser($idUser,$connexion)
    {
        return $connexion->table('ZoneUtilisateur')
            ->where('CodeUtilisateur', '=', $idUser)
            ->get()->pluck('CodeZone')->toArray();
    }

    public static function getClientsByZones($zones,$connexion)
    {
        return $connexion->table('client')
            ->join('View_SoldeClient', 'CodeClient', '=', 'CLI_Code')
            ->whereIn('Clt_Circuit', array_values($zones));
    }

    public static function filterClients($array,$key): array
    {
        $filtered_array = array();

        $i = 0;

        $key_array = array();


        foreach ($array as $item)
        {

            if (!in_array($item->$key, $key_array)) {

                $key_array[$i] = $item->$key;
                $filtered_array[$i] = $item;
                $i++;

            }

        }

        return $filtered_array;
    }

}
