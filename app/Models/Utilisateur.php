<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Utilisateur extends Model
{
    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'Utilisateur';

    /**
     * The primary key for the model.
     * 
     * @var string
     */
    protected $primaryKey = 'Code_Ut';

    /**
     * @var array
     */
    protected $fillable = ['Nom', 'Prenom', 'Tel', 'Mail', 'Login', 'Passe', 'Etat', 'Groupe', 'Station', 'Type_user', 'export', 'DDm', 'CodeCle', 'Autorise_R', 'CodeCle1', 'Code_Caisse', 'Code_Carnet', 'FiltreClt', 'UPgros', 'Autorise_Rt', 'Autorise_Bc', 'exportM', 'DDmM', 'UPointT', 'MajPvBL', 'AutBonA', 'MajRegTik', 'MajTik', 'AutoriseCrCt', 'AutoriseTourne', 'Autorise_Clt', 'ClotSessAuto', 'CrtourneAuto', 'HeurFintourne', 'CltEquivalent'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function etat()
    {
        return $this->belongsTo('App\Etat', 'Etat', 'Etat');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function groupe()
    {
        return $this->belongsTo('App\Groupe', 'Groupe', 'GRP_Code');
    }

    /**
     * @return HasMany
     */
    public function profilUs()
    {
        return $this->hasMany('App\ProfilU', 'Id_Utilisateur', 'Code_Ut');
    }

    /**
     * @return HasMany
     */
    public function profiles()
    {
        return $this->hasMany('App\Profile', 'Code_Ut', 'Code_Ut');
    }

    /**
     * @return HasMany
     */
    public function profileboutons()
    {
        return $this->hasMany('App\Profilebouton', 'Code_Ut', 'Code_Ut');
    }

    /**
     * @return HasMany
     */
    public function sessionCaisses()
    {
        return $this->hasMany('App\Models\SessionCaisse', 'SC_CodeUtilisateur', 'Code_Ut');
    }

    /**
     * @return HasMany
     */
    public function autorisationUser()
    {
        return $this->hasMany(AutorisationUser::class, 'AutoCodeUt', 'Code_Ut');
    }

    /**
     * @return HasMany
     */
    public function stations()
    {
        return $this->hasMany('App\Station', 'Code_Ut1', 'Code_Ut');
    }
}
