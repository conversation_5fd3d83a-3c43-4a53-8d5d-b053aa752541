<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $AutoCodeUt
 * @property string $AutoCodeAu
 * @property boolean $AutEtat
 */
class AutorisationUser extends Model
{

    protected $primaryKey = 'AutoCodeUt';
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'AutorisationUser';

    /**
     * @var array
     */
    protected $fillable = ['AutEtat'];

    protected $casts = [
        'AutEtat' => 'boolean',
        'AutoCodeAu' => 'integer'
    ];
}
