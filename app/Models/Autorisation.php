<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @property string $AutoCode
 * @property string $AutoNom
 * @property string $AutoDescription
 */
class Autorisation extends Model
{
    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'Autorisation';

    /**
     * The primary key for the model.
     * 
     * @var string
     */
    protected $primaryKey = 'AutoCode';

    /**
     * The "type" of the auto-incrementing ID.
     * 
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     * 
     * @var bool
     */
    public $incrementing = false;

    /**
     * @var array
     */
    protected $fillable = ['AutoNom', 'AutoDescription'];
}
