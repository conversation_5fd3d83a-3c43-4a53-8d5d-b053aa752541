<?php

namespace App\Models\DuxInventory;



class P_Unite extends BaseModel
{
    protected $connection = 'onthefly';
    protected $table = 'P_Unite';
    public $incrementing = false;
    public $timestamps = false;
    protected $guarded = [];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->idClasse = 'P_Unite';
        $this->useCode = true;
    }
}
