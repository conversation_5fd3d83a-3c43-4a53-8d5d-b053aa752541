<?php

namespace App\Models\DuxInventory;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class TicketRayon extends Model
{
    protected $connection = 'onthefly';
    protected $table = 'Ticket_Rayon';
    public $incrementing = false;
    public $timestamps = false;
    protected $guarded = [];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->{$model->getKeyName()} = BaseModel::getID();
            $model->j_ddm = Carbon::now()->format('Y-m-d H:i:s');
        });


    }
}
