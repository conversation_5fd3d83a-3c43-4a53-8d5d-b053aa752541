<?php

namespace App\Models\DuxInventory;


class P_Famille extends BaseModel
{
    protected $table = 'P_Famille';
    protected $connection = 'onthefly';
    public $incrementing = false;
    public $timestamps = false;
    protected $guarded = [];
    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->idClasse = 'P_Famille';
        $this->useCode = true;

    }
}
