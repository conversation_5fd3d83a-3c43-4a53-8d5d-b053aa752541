<?php

namespace App\Models\DuxInventory;

use Illuminate\Database\Eloquent\Model;

class P_Article_Unite extends Model
{
    protected $connection = 'onthefly';
    protected $table = 'P_Article_Unite';
    public $incrementing = false;
    public $timestamps = false;
    protected $guarded = [];
    protected $casts = [
        'j_export' => 'boolean',
        'isAchat' => 'boolean',
        'isVente' => 'boolean',
        'isdefault_Achat' => 'boolean',
        'isdefault_Vente' => 'boolean',
        'isdefault' => 'boolean',
        'issecondaire' => 'boolean',
        'isSync' => 'boolean',
    ];
}
