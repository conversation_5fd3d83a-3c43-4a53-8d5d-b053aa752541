<?php

namespace App\Models\DuxInventory;



class E_TypeArticle extends BaseModel
{
    protected $table = 'E_TypeArticle';
    protected $connection = 'onthefly';
    public $incrementing = false;
    public $timestamps = false;
    protected $guarded = [];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        $this->idClasse = 'E_TypeArticle';
        $this->useCode=true;
        $this->casts = array_merge($this->casts, [
            'AllowChangePrix' => 'boolean',
            'affect_CMP' => 'boolean',
            'affect_DPA' => 'boolean',
            'familleObligatoire' => 'boolean',
            'periodique' => 'boolean',
        ]);
    }

}
