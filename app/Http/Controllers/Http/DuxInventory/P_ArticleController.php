<?php

namespace App\Http\Controllers\Http\DuxInventory;


use App\Helpers\DatabaseConnection;
use App\Http\Requests\FilterRequest;
use App\Http\Requests\P_ArticleRequest;
use App\Models\DuxInventory\P_Article;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;

/**
 * @OA\Tag(
 *     name="Articles DuxInventory",
 *     description="Gestion des articles dans le module DuxInventory"
 * )
 */
class P_ArticleController extends BaseController
{
    public function __construct()
    {
        $this->modelClass = P_Article::class;
        $this->requestClass = P_ArticleRequest::class;
    }

    /**
     * @OA\Post(
     *     path="/duxinventory/article/getArticles",
     *     tags={"Articles DuxInventory"},
     *     summary="Récupérer la liste des articles avec pagination",
     *     description="Retourne la liste paginée des articles actifs et stockables avec leurs codes à barres",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Paramètres de connexion et de pagination",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="connexion",
     *                 ref="#/components/schemas/DatabaseConnection"
     *             ),
     *             @OA\Property(
     *                 property="object",
     *                 type="object",
     *                 @OA\Property(property="limit", type="integer", example=15, description="Nombre d'articles par page")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Liste des articles récupérée avec succès",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="current_page", type="integer", example=1),
     *                 @OA\Property(property="per_page", type="integer", example=15),
     *                 @OA\Property(property="total", type="integer", example=150),
     *                 @OA\Property(
     *                     property="data",
     *                     type="array",
     *                     @OA\Items(ref="#/components/schemas/Article")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function getArticles(FilterRequest $request)
    {
        try {
            $validated = $request->validated();
            $limit = $validated['object']['limit'];
            $articles = P_Article::from('P_Article as a')
                ->leftJoin('L_CodeABare as l', 'l.idarticle', '=', 'a.id')
                ->where('a.active', '1')
                ->where('a.isStockable', '1')
                ->select('a.*','l.codeabare')
                ->paginate($limit);
            return response()->success($articles);
        } catch (\Exception $e) {
            return response()->error($e);
        }
    }


    /**
     * @OA\Post(
     *     path="/duxinventory/article/getCountArticle",
     *     tags={"Articles DuxInventory"},
     *     summary="Compter le nombre total d'articles",
     *     description="Retourne le nombre total d'articles actifs et stockables",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Paramètres de connexion",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="connexion",
     *                 ref="#/components/schemas/DatabaseConnection"
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Nombre d'articles récupéré avec succès",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="integer", example=1250, description="Nombre total d'articles")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function getCountArticle()
    {
        $count = P_Article::count()
            ->where('active', '1')
            ->where('isStockable', '1');
        return response()->success($count);
    }

    public function etatStock(Request $request)
    {
        set_time_limit(300);
        $idStation = $request->input('object.idStation');
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $limit = $request->input('object.limit');
        $where = "";
        if ($idStation !== 'all') {
            $where = "and dbo.P_Station.id='$idStation'";
        }
        try {

            $detailDoc = $connection->select("set language french; 
        SELECT   dbo.L_stock.*,
         dbo.P_Article.code as code,
         dbo.P_Article.photo as photo,
         dbo.P_Article.libelle as libelle,dbo.P_Article.libelleCourte as libelleCourte, 
         dbo.P_Article.tauxTva as tauxTva,
         dbo.P_Article.prixuht as prixuht, dbo.P_Article.codeModele as codeModele, dbo.P_Article.libelleModele as libelleModele, dbo.P_Article.codeFamille as codeFamille, dbo.P_Article.libelleFamille as libelleFamille, 
         dbo.P_Article.codeCouleur as codeCouleur, dbo.P_Article.libelleCouleur as libelleCouleur, dbo.P_Article.codeTaille as codeTaille, 
         dbo.P_Article.libelleTaille as libelleTaille, dbo.P_Article.codeMarque as codeMarque, dbo.P_Article.libellleMarque as libelleMarque, dbo.P_Article.codeUnite as codeUnite,dbo.P_Article.libelleUnite, dbo.P_Article.codeTypeArticle, 
         dbo.P_Article.libelleTypeArticle, 
         CASE WHEN dbo.P_Article.dernierDateAchat IS NULL THEN '' ELSE  CONVERT(VARCHAR(30), dbo.P_Article.dernierDateAchat, 103) END as ddateAchat,CASE WHEN dbo.P_Article.dernierDateVente IS NULL THEN '' ELSE  CONVERT(VARCHAR(30), dbo.P_Article.dernierDateVente, 103) END as ddatevente,
         ISNULL(qtee.qteE ,0) as qtee,ISNULL(qtAV.qteAVT ,0) as qtAV,ISNULL(qtes.qteS ,0) as qts,
        
        dbo.P_Article.cmp AS cmpArt,
        dbo.L_stock.qte*dbo.P_Article.cmp as tot_cmp,
        dbo.P_Article.prixVentePubHt,
        dbo.P_Article.dernierPrixAchat,
        dbo.L_stock.qte*dbo.P_Article.dernierPrixAchat as tot_pa, dbo.L_stock.qte*dbo.P_Article.prixVentePubHt as tot_pv
        ,dbo.P_Article.prixuhtDevise as dprixdevise, 
        dbo.P_Article.active, dbo.P_Article.isStockable as isStockable, 
        dbo.P_Article.coutRevientPonderer as coutRevientPonderer, 
        dbo.L_stock.qte*dbo.P_Article.coutRevientPonderer as tot_crp,
        dbo.P_Station.code AS codestation, 
        dbo.P_Station.libelle AS stationArt, dbo.P_Station.active AS etatstation,(select codeabare  from L_CodeABare
        where id=L_stock .idCodeBar  and idarticle =P_Article .id ) as codeabare,P_Article .RefFrs
        
        FROM    dbo.L_stock LEFT OUTER JOIN
                                 dbo.P_Station ON dbo.L_stock.idStation = dbo.P_Station.id LEFT OUTER JOIN
                                 dbo.P_Article ON dbo.L_stock.idArticle = dbo.P_Article.id left outer join					 
                                 ( SELECT idarticle,idStation,sum(qte) as qteE  from View_Mvt_Art
         where incrementeStock=1 and codeClasseDocument<>'inv'  and isAchat='true'
         group by idArticle ,idStation ) as qtee on qtee.idArticle=L_stock.idArticle and qtee.idStation =L_stock.idStation  left outer join		
                             (  SELECT idarticle,idStation,sum(qte) as qteAVT from View_Mvt_Art
         where incrementeStock =1 and codeClasseDocument<>'inv'  and isVente='true' 
         group by idArticle ,idStation) qtAV on qtav.idArticle=L_stock.idArticle and qtAV.idStation =L_stock.idStation  left outer join		
                             (  SELECT idarticle,idStation,sum(qte) as qteS from View_Mvt_Art
         where decrementeStock =1 
        
         group by idArticle ,idStation) qtes on qtes.idArticle=L_stock.idArticle and qtes.idStation =L_stock.idStation 
         where dbo.P_Station.active='True' and dbo.P_Article.active='True' and dbo.P_Article.isStockable='True'  $where ");

            $resultsCollection = collect($detailDoc);
            $page = LengthAwarePaginator::resolveCurrentPage();
            $result = new LengthAwarePaginator(
                $resultsCollection->forPage($page, $limit)->values(),
                $resultsCollection->count(),
                $limit,
                $page,
                ['path' => LengthAwarePaginator::resolveCurrentPath()]
            );
            return response()->success($result);
        } catch (\Exception $e) {
            return response()->error($e);
        }
    }

    public function allStockArticle(Request $request)
    {
        try {
            $data = $request->json()->all();
            $connection = DatabaseConnection::setConnection($data);
            $limit = $request->input('object.limit');
            $datas = $connection->table('viewStk as l')
                ->leftJoin('P_Station as s', 's.id', '=', 'l.idStation')
                ->leftJoin('P_Article as p', 'l.idArticle', '=', 'p.id')
                ->where('p.active', '=', 'true')
                ->where('s.stockDefectueux', '=', '0')
                ->where('p.isStockable', '=', 'true')
                ->select('p.id as idArticle', 'p.code as codart', 'p.photo', 'p.libelle as libelle', 'p.libelleCourte as libelleCourte',
                    'p.libelleFamille as libelleFamille', 'p.libellleMarque as libelleMarque', 'p.active as active', 'p.isStockable as isStockable', 'l.idStation',
                    DB::raw('SUM(l.qte) as qtTotal'),
                    DB::raw('SUM(l.qteReserve) as qtCMD'),
                    DB::raw('SUM(l.qte-l.qteReservecalculer) as rest'),
                    DB::raw('SUM(l.qteReservecalculer) as qteReservecalculer'),
                    DB::raw('SUM(p.QteMin) as QteMin'))
                ->groupBy(['s.pointvente', 'p.id', 'p.code', 'p.libelleFamille', 'p.libellleMarque', 'p.libelle', 'p.libelleCourte', 'p.photo', 'p.active', 'p.isStockable', 'l.idStation'])
                ->paginate($limit);
            return response()->success($datas);
        } catch (\Exception $e) {
            return response()->error($e);
        }
    }


    public function allQteTheoriqueArticle(Request $request)
    {
        try {
            $data = $request->json()->all();
            $connection = DatabaseConnection::setConnection($data);
            $limit = $request->input('object.limit');
            $idStation = $request->input('object.station');
            $datas = $connection->table('L_stock as l')
                ->where('idStation', $idStation)
                ->paginate($limit);
            return response()->success($datas);
        } catch (\Exception $e) {
            return response()->error($e);
        }
    }

}
