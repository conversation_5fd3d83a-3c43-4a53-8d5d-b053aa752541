<?php

namespace App\Http\Controllers\Http\DuxInventory;

use App\Enums\ClasseDocument;
use App\Services\M_DocumentService;


class BonTransfertController extends M_DocumentController
{
    protected $documentService;
    protected $idClasseDocument;
    protected $dateFilter;

    public function __construct(M_DocumentService $documentService)
    {
        parent::__construct($documentService);
        $this->idClasseDocument = ClasseDocument::BonTransfert;
        $this->dateFilter = true;
    }
}
