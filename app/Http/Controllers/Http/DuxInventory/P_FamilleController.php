<?php

namespace App\Http\Controllers\Http\DuxInventory;

use App\Http\Requests\BaseRequest;
use App\Models\DuxInventory\P_Famille;

/**
 * @OA\Tag(
 *     name="Familles DuxInventory",
 *     description="Gestion des familles d'articles dans le module DuxInventory"
 * )
 */
class P_FamilleController extends BaseController
{
    public function __construct()
    {
        $this->modelClass = P_Famille::class;
        $this->requestClass = BaseRequest::class;
    }

    /**
     * @OA\Post(
     *     path="/duxinventory/famille/getAll",
     *     tags={"Familles DuxInventory"},
     *     summary="Récupérer toutes les familles d'articles",
     *     description="Retourne la liste complète des familles d'articles disponibles",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Paramètres de connexion",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="connexion",
     *                 ref="#/components/schemas/DatabaseConnection"
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Liste des familles récupérée avec succès",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(ref="#/components/schemas/Famille")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    // La méthode index() est héritée de BaseController

    /**
     * @OA\Post(
     *     path="/duxinventory/famille/add",
     *     tags={"Familles DuxInventory"},
     *     summary="Ajouter ou mettre à jour des familles",
     *     description="Permet d'ajouter de nouvelles familles ou de mettre à jour celles existantes",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Données des familles à ajouter/modifier",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="connexion",
     *                 ref="#/components/schemas/DatabaseConnection"
     *             ),
     *             @OA\Property(
     *                 property="object",
     *                 type="array",
     *                 @OA\Items(ref="#/components/schemas/FamilleInput")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Familles ajoutées/mises à jour avec succès",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(ref="#/components/schemas/Famille")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Données invalides",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    // La méthode add() est héritée de BaseController

}
