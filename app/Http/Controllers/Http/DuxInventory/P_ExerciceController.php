<?php

namespace App\Http\Controllers\Http\DuxInventory;


use App\Models\DuxInventory\P_Exercice;
use App\Http\Controllers\Controller;


class P_ExerciceController extends Controller
{
    public function getExercice()
    {
        try {
            $exercice = P_Exercice::where('active','1')->first();
            return response()->success($exercice);
        }catch (\Exception $e) {
            return response()->error($e);
        }
    }

}
