<?php

namespace App\Http\Controllers\Http\DuxInventory;


use App\Http\Requests\BaseRequest;
use App\Models\DuxInventory\P_Marque;

/**
 * @OA\Tag(
 *     name="Marques DuxInventory",
 *     description="Gestion des marques dans le module DuxInventory"
 * )
 */
class P_MarqueController extends BaseController
{

    public function __construct()
    {
        $this->modelClass = P_Marque::class;
        $this->requestClass = BaseRequest::class;
    }

    /**
     * @OA\Post(
     *     path="/duxinventory/marque/getAll",
     *     tags={"Marques DuxInventory"},
     *     summary="Récupérer toutes les marques",
     *     description="Retourne la liste complète des marques disponibles",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Paramètres de connexion",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="connexion",
     *                 ref="#/components/schemas/DatabaseConnection"
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Liste des marques récupérée avec succès",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(ref="#/components/schemas/Marque")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    // La méthode index() est héritée de BaseController

    /**
     * @OA\Post(
     *     path="/duxinventory/marque/add",
     *     tags={"Marques DuxInventory"},
     *     summary="Ajouter ou mettre à jour des marques",
     *     description="Permet d'ajouter de nouvelles marques ou de mettre à jour celles existantes",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Données des marques à ajouter/modifier",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="connexion",
     *                 ref="#/components/schemas/DatabaseConnection"
     *             ),
     *             @OA\Property(
     *                 property="object",
     *                 type="array",
     *                 @OA\Items(ref="#/components/schemas/MarqueInput")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Marques ajoutées/mises à jour avec succès",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(ref="#/components/schemas/Marque")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Données invalides",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    // La méthode add() est héritée de BaseController

}
