<?php

namespace App\Http\Controllers;

/**
 * @OA\Info(
 *     title="ProCaisse API Documentation",
 *     version="1.0.0",
 *     description="Documentation complète des APIs du système ProCaisse pour la gestion de caisse et d'inventaire",
 *     @OA\Contact(
 *         email="<EMAIL>",
 *         name="Support ProCaisse"
 *     )
 * )
 * 
 * @OA\Server(
 *     url=L5_SWAGGER_CONST_HOST,
 *     description="Serveur ProCaisse"
 * )
 * 
 * @OA\SecurityScheme(
 *     securityScheme="bearerAuth",
 *     type="http",
 *     scheme="bearer",
 *     bearerFormat="JWT",
 *     description="Authentification par token Bearer JWT"
 * )
 * 
 * @OA\Schema(
 *     schema="ErrorResponse",
 *     type="object",
 *     @OA\Property(property="success", type="boolean", example=false),
 *     @OA\Property(property="message", type="string", example="Une erreur s'est produite"),
 *     @OA\Property(property="errors", type="object")
 * )
 * 
 * @OA\Schema(
 *     schema="SuccessResponse",
 *     type="object",
 *     @OA\Property(property="success", type="boolean", example=true),
 *     @OA\Property(property="data", type="object"),
 *     @OA\Property(property="message", type="string", example="Opération réussie")
 * )
 * 
 * @OA\Schema(
 *     schema="DatabaseConnection",
 *     type="object",
 *     description="Paramètres de connexion à la base de données",
 *     @OA\Property(property="host", type="string", example="localhost", description="Adresse du serveur de base de données"),
 *     @OA\Property(property="database", type="string", example="procaisse_db", description="Nom de la base de données"),
 *     @OA\Property(property="username", type="string", example="user", description="Nom d'utilisateur"),
 *     @OA\Property(property="password", type="string", example="password", description="Mot de passe")
 * )
 */
class SwaggerController extends Controller
{
    // Ce contrôleur sert uniquement pour les annotations Swagger globales
}
