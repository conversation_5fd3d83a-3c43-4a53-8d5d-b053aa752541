<?php

namespace App\Http\Controllers;

/**
 * @OA\Info(
 *     title="ProCaisse API Documentation",
 *     version="1.0.0",
 *     description="Documentation complète des APIs du système ProCaisse pour la gestion de caisse et d'inventaire",
 *     @OA\Contact(
 *         email="<EMAIL>",
 *         name="Support ProCaisse"
 *     )
 * )
 * 
 * @OA\Server(
 *     url=L5_SWAGGER_CONST_HOST,
 *     description="Serveur ProCaisse"
 * )
 * 
 * @OA\SecurityScheme(
 *     securityScheme="bearerAuth",
 *     type="http",
 *     scheme="bearer",
 *     bearerFormat="JWT",
 *     description="Authentification par token Bearer JWT"
 * )
 * 
 * @OA\Schema(
 *     schema="ErrorResponse",
 *     type="object",
 *     @OA\Property(property="success", type="boolean", example=false),
 *     @OA\Property(property="message", type="string", example="Une erreur s'est produite"),
 *     @OA\Property(property="errors", type="object")
 * )
 * 
 * @OA\Schema(
 *     schema="SuccessResponse",
 *     type="object",
 *     @OA\Property(property="success", type="boolean", example=true),
 *     @OA\Property(property="data", type="object"),
 *     @OA\Property(property="message", type="string", example="Opération réussie")
 * )
 * 
 * @OA\Schema(
 *     schema="DatabaseConnection",
 *     type="object",
 *     description="Paramètres de connexion à la base de données ProCaisse",
 *     @OA\Property(property="id_base_config", type="string", example="1562", description="Identifiant de configuration de base"),
 *     @OA\Property(property="key_base", type="string", example="661-47J-J4P", description="Clé de base"),
 *     @OA\Property(property="dbName", type="string", example="681f857273fa321692a64d730af8412b", description="Nom de base de données crypté"),
 *     @OA\Property(property="dbIpAddress", type="string", example="17d94b77f3ffb1fedc4176d9916c9265", description="Adresse IP cryptée"),
 *     @OA\Property(property="adresse_ip", type="string", example="*************", description="Adresse IP du serveur"),
 *     @OA\Property(property="port", type="string", example="2222", description="Port de connexion"),
 *     @OA\Property(property="username", type="string", example="2148ed972ff6ca8e0bf0484a9bcdc179", description="Nom d'utilisateur crypté"),
 *     @OA\Property(property="password", type="string", example="454193ea003c0c8ea858f1501fccbd3c", description="Mot de passe crypté"),
 *     @OA\Property(property="designation_base", type="string", example="ChahiaNewPatrimoine", description="Désignation de la base"),
 *     @OA\Property(property="produit", type="string", example="ProCaisse Mobility", description="Nom du produit"),
 *     @OA\Property(property="id_entreprise", type="string", example="980", description="Identifiant de l'entreprise"),
 *     @OA\Property(property="date_creation", type="string", example="26-03-2024 12:09:26", description="Date de création"),
 *     @OA\Property(
 *         property="licences",
 *         type="array",
 *         description="Liste des licences",
 *         @OA\Items(ref="#/components/schemas/Licence")
 *     )
 * )
 *
 * @OA\Schema(
 *     schema="Licence",
 *     type="object",
 *     description="Informations de licence",
 *     @OA\Property(property="id", type="integer", example=29904, description="Identifiant de la licence"),
 *     @OA\Property(property="activat", type="string", example="true", description="Statut d'activation"),
 *     @OA\Property(property="datef", type="string", example="2025-09-23", description="Date de fin"),
 *     @OA\Property(property="dater", type="number", example=81.0, description="Durée restante"),
 *     @OA\Property(property="demo", type="string", example="false", description="Mode démo"),
 *     @OA\Property(property="device", type="string", example="promobile", description="Type d'appareil"),
 *     @OA\Property(property="email", type="string", example="<EMAIL>", description="Email"),
 *     @OA\Property(property="etablissement", type="string", example="nader", description="Établissement"),
 *     @OA\Property(property="id_device", type="string", example="79fc8999cc3a379e", description="Identifiant de l'appareil"),
 *     @OA\Property(property="produit", type="string", example="ProCaisse Mobility", description="Nom du produit"),
 *     @OA\Property(property="version", type="string", example="v13", description="Version")
 * )
 *
 * @OA\Schema(
 *     schema="PaginationRequest",
 *     type="object",
 *     description="Paramètres de pagination",
 *     @OA\Property(property="page", type="string", example="1", description="Numéro de page"),
 *     @OA\Property(property="limit", type="string", example="2000", description="Nombre d'éléments par page")
 * )
 */
class SwaggerController extends Controller
{
    // Ce contrôleur sert uniquement pour les annotations Swagger globales
}
