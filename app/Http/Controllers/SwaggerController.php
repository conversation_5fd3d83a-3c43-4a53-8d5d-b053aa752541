<?php

namespace App\Http\Controllers;

/**
 * @OA\Info(
 *     title="ProCaisse API Documentation",
 *     version="1.0.0",
 *     description="Documentation complète des APIs du système ProCaisse pour la gestion de caisse et d'inventaire",
 *     @OA\Contact(
 *         email="<EMAIL>",
 *         name="Support ProCaisse"
 *     )
 * )
 * 
 * @OA\Server(
 *     url=L5_SWAGGER_CONST_HOST,
 *     description="Serveur de développement ProCaisse"
 * )
 * 
 * @OA\SecurityScheme(
 *     securityScheme="bearerAuth",
 *     type="http",
 *     scheme="bearer",
 *     bearerFormat="JWT",
 *     description="Authentification par token Bearer JWT"
 * )
 * 
 * @OA\Tag(
 *     name="Articles",
 *     description="Gestion des articles et produits"
 * )
 * 
 * @OA\Tag(
 *     name="Marques",
 *     description="Gestion des marques"
 * )
 * 
 * @OA\Tag(
 *     name="Familles",
 *     description="Gestion des familles d'articles"
 * )
 * 
 * @OA\Tag(
 *     name="Unités",
 *     description="Gestion des unités de mesure"
 * )
 * 
 * @OA\Tag(
 *     name="Couleurs",
 *     description="Gestion des couleurs"
 * )
 * 
 * @OA\Tag(
 *     name="Inventaires",
 *     description="Gestion des inventaires"
 * )
 * 
 * @OA\Tag(
 *     name="Bons d'entrée",
 *     description="Gestion des bons d'entrée"
 * )
 * 
 * @OA\Tag(
 *     name="Bons de livraison",
 *     description="Gestion des bons de livraison"
 * )
 * 
 * @OA\Tag(
 *     name="Bons de transfert",
 *     description="Gestion des bons de transfert"
 * )
 * 
 * @OA\Tag(
 *     name="Stations",
 *     description="Gestion des stations/points de vente"
 * )
 * 
 * @OA\Tag(
 *     name="Fournisseurs",
 *     description="Gestion des fournisseurs"
 * )
 * 
 * @OA\Tag(
 *     name="Utilisateurs",
 *     description="Gestion des utilisateurs et authentification"
 * )
 * 
 * @OA\Tag(
 *     name="Clients",
 *     description="Gestion des clients"
 * )
 * 
 * @OA\Tag(
 *     name="Tickets",
 *     description="Gestion des tickets de vente"
 * )
 * 
 * @OA\Tag(
 *     name="Caisses",
 *     description="Gestion des caisses"
 * )
 * 
 * @OA\Tag(
 *     name="Sessions",
 *     description="Gestion des sessions de caisse"
 * )
 * 
 * @OA\Schema(
 *     schema="ErrorResponse",
 *     type="object",
 *     @OA\Property(property="success", type="boolean", example=false),
 *     @OA\Property(property="message", type="string", example="Une erreur s'est produite"),
 *     @OA\Property(property="errors", type="object")
 * )
 * 
 * @OA\Schema(
 *     schema="SuccessResponse",
 *     type="object",
 *     @OA\Property(property="success", type="boolean", example=true),
 *     @OA\Property(property="data", type="object"),
 *     @OA\Property(property="message", type="string", example="Opération réussie")
 * )
 * 
 * @OA\Schema(
 *     schema="PaginationMeta",
 *     type="object",
 *     @OA\Property(property="current_page", type="integer", example=1),
 *     @OA\Property(property="from", type="integer", example=1),
 *     @OA\Property(property="last_page", type="integer", example=10),
 *     @OA\Property(property="per_page", type="integer", example=15),
 *     @OA\Property(property="to", type="integer", example=15),
 *     @OA\Property(property="total", type="integer", example=150)
 * )
 * 
 * @OA\Schema(
 *     schema="FilterRequest",
 *     type="object",
 *     @OA\Property(
 *         property="object",
 *         type="object",
 *         @OA\Property(property="limit", type="integer", example=15, description="Nombre d'éléments par page")
 *     )
 * )
 */
class SwaggerController extends Controller
{
    // Ce contrôleur sert uniquement pour les annotations Swagger globales
}
