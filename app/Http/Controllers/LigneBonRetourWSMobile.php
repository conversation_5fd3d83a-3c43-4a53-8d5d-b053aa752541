<?php

namespace App\Http\Controllers;

use App\Helpers\AppHelper;
use App\Helpers\DatabaseConnection;
use DateTime;
use Illuminate\Http\Request;

class LigneBonRetourWSMobile extends Controller
{

    /*fonction get data from database*/

    public function getLigneBonRetour(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        return response()->json($connection->table('ligne_bon_retour')->get(), 200, ['Content-type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }


    public function addBatchLigneBonRetour($items)
    {
        if (!empty($items)) {
            $connection = DatabaseConnection::setConnection($items);
            $result = [];
            foreach ($items["object"] as $data) {
                $item = $data;
                $item['LIG_BonEntree_DDm'] =  AppHelper::setDateFormat($data['LIG_BonEntree_DDm']);
                $item["LIG_BonEntree_MntNetHt"] = floatval($data["LIG_BonEntree_MntNetHt"]);
                $item["LIG_BonEntree_MntTva"] = floatval($data["LIG_BonEntree_MntTva"]);
                $item["LIG_BonEntree_MntTTC"] = floatval($data["LIG_BonEntree_MntTTC"]);
                $item["LIG_BonEntree_Remise"] = floatval($data["LIG_BonEntree_Remise"]);
                $c = $connection->table('ligne_bon_retour')->insert($item);
                if ($c) {
                    array_push($result, $data);
                }
            }
        } else {
            return response()->json(null);
        }

        return response()->json($result);
    }

    public function deleteBatchLigneBonEntrees(Request $request)
    {
        $items = $request->json()->all();

        if (!empty($items)) {
            //    $data['object']['CLI_Code'] =(new PrefixWSMobile)->getPrefix($data,"Client");
            $connection = DatabaseConnection::setConnection($items);
            $result = null;
            foreach ($items["object"] as $data) {

                $c = $connection->table('ligne_bon_entree_Mobile')
                    ->where('LIG_BonEntree_NumBon', $data["LIG_BonEntree_NumBon"])->where('LIG_BonEntree_CodeArt', $data["LIG_BonEntree_CodeArt"])->delete();

                if ($c) {
                    $result[] = $data;
                } else {
                    $result[] = null;
                }
            }
        } else {
            return response()->json(null);
        }

        return response()->json($result);
    }

    public function updateBatchLigneBonEntrees(Request $request)
    {
        $items = $request->json()->all();

        if (!empty($items)) {
            //    $data['object']['CLI_Code'] =(new PrefixWSMobile)->getPrefix($data,"Client");
            $connection = DatabaseConnection::setConnection($items);
            $result = null;
            foreach ($items["object"] as $data) {

                $c = $connection->table('ligne_bon_entree_Mobile')->where('LIG_BonEntree_NumBon', $data["LIG_BonEntree_NumBon"])->where('LIG_BonEntree_CodeArt', $data["LIG_BonEntree_CodeArt"])->update($data);

                if ($c) {
                    $result[] = $data;
                } else {
                    $result[] = null;
                }
            }
        } else {
            return response()->json(null);
        }

        return response()->json($result);
    }

    public function addBatchLigneBonEntrees(Request $request)
    {
        $items = $request->json()->all();

        if (!empty($items)) {
            //    $data['object']['CLI_Code'] =(new PrefixWSMobile)->getPrefix($data,"Client");
            $connection = DatabaseConnection::setConnection($items);
            $result = null;
            foreach ($items["object"] as $data) {

                $c = $connection->table('ligne_bon_entree_Mobile')->insert($data);

                if ($c) {
                    $result[] = $data;
                } else {
                    $result[] = null;
                }
            }
        } else {
            return response()->json(null);
        }

        return response()->json($result);
    }


}
