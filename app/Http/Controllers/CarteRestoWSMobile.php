<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests;
use Illuminate\Http\Response;
use App\Helpers\DatabaseConnection;

/**
 * @OA\Tag(
 *     name="Cartes Restaurant Mobile",
 *     description="Gestion des cartes restaurant depuis l'application mobile ProCaisse"
 * )
 */
class CarteRestoWSMobile extends Controller
{

    /**
     * @OA\Post(
     *     path="/CarteResto/getCarteResto",
     *     tags={"Cartes Restaurant Mobile"},
     *     summary="Récupérer toutes les cartes restaurant",
     *     description="Retourne la liste complète des cartes restaurant disponibles",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Paramètres de connexion",
     *         @OA\JsonContent(ref="#/components/schemas/StandardRequest")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Liste des cartes restaurant récupérée avec succès",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/CarteResto")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function getCarteResto(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('Carte_Resto')->get());

    }


    /**
     * @OA\Post(
     *     path="/CarteResto/getCarteRestoByCode",
     *     tags={"Cartes Restaurant Mobile"},
     *     summary="Récupérer une carte restaurant par son code",
     *     description="Retourne les détails d'une carte restaurant spécifique en utilisant son code",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Paramètres de connexion et code de la carte",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="connexion",
     *                 ref="#/components/schemas/DatabaseConnection"
     *             ),
     *             @OA\Property(
     *                 property="object",
     *                 type="string",
     *                 example="CARTE001",
     *                 description="Code de la carte restaurant à rechercher"
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Carte restaurant trouvée avec succès",
     *         @OA\JsonContent(ref="#/components/schemas/CarteResto")
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Carte restaurant non trouvée",
     *         @OA\JsonContent(type="null")
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function getCarteRestoByCode(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('Carte_Resto')->where('Code', $data["object"])->first());

    }

}





