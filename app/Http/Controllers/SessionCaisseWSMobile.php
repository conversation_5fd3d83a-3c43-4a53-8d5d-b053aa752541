<?php

namespace App\Http\Controllers;

use App\Helpers\AppHelper;
use App\Helpers\DatabaseConnection;
use App\Http\Requests;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;

/**
 * @OA\Tag(
 *     name="Sessions Caisse Mobile",
 *     description="Gestion des sessions de caisse depuis l'application mobile ProCaisse"
 * )
 */
class SessionCaisseWSMobile extends Controller
{
    /**
     * @OA\Post(
     *     path="/SessionCaisse/addSession",
     *     tags={"Sessions Caisse Mobile"},
     *     summary="Créer une nouvelle session de caisse",
     *     description="Crée une nouvelle session de caisse en fermant d'abord toute session existante",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Données de la session à créer",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="connexion",
     *                 ref="#/components/schemas/DatabaseConnection"
     *             ),
     *             @OA\Property(
     *                 property="object",
     *                 type="object",
     *                 @OA\Property(property="utilisateur", type="string", example="USER001", description="Code utilisateur"),
     *                 @OA\Property(property="caisse", type="string", example="CAISSE001", description="Code caisse"),
     *                 @OA\Property(property="carnet", type="string", example="CARNET001", description="Code carnet"),
     *                 @OA\Property(property="station", type="string", example="ST001", description="Code station"),
     *                 @OA\Property(property="fondCaisse", type="number", format="float", example=1000.00, description="Fond de caisse initial"),
     *                 @OA\Property(property="nomMachine", type="string", example="MACHINE001", description="Nom de la machine")
     *             ),
     *             @OA\Property(property="SC_IdSCaisse", type="string", example="SC001", description="ID session à fermer (optionnel)")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Session créée avec succès",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="error", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="INSERTED"),
     *             @OA\Property(
     *                 property="data",
     *                 ref="#/components/schemas/SessionCaisse"
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Erreur lors de la création",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="error", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="NOT INSERTED")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function addSession(Request $request)
    {

        try {
            $data = $request->json()->all();
            $connection = DatabaseConnection::setConnection($data);
            $value = ($connection->select("SELECT TOP (1) Exercice_Code FROM  Exercice WHERE (Exercice_Etat = 'Actif')"));
            $SC_IdSCaisse = $request->input('SC_IdSCaisse');
            $result_close = $this->closeSession($connection, $SC_IdSCaisse);
            if ($result_close) {
                if (!!$value && sizeof($value)) {
                    $session = ((new PrefixWSMobile)->getPrefixSessionCaisse($data, "SessionCaisse",
                        ($value[0]->Exercice_Code),
                        $data["object"]['utilisateur']));
                    $sessionInserted = array(
                        $session,
                        $data["object"]['caisse'],
                        $data["object"]['utilisateur'],
                        $data["object"]['carnet'],
                        $data["object"]['station'],
                        $data["object"]['fondCaisse'],
                        $data["object"]['nomMachine']
                    );

                    $inserted = $connection->statement('exec Creation_Session_vendeur ?,?,?,?,?,?,?', $sessionInserted);
                    if ($inserted) {
                        $sessionResponse = $connection->table('SessionCaisse')
                            ->where('SC_IdSCaisse', '=', $session)
                            ->first();
                        return response()->json([
                            'error' => false,
                            "message" => "INSERTED",
                            "data" => $sessionResponse
                        ]);

                    } else {
                        return response()->json([
                            'error' => true,
                            "message" => "NOT INSERTED",
                            "data" => null
                        ]);
                    }
                } else {
                    return response()->json([
                        'error' => false,
                        "message" => "There is no exercise active",
                        "data" => $value
                    ]);
                }
            } else {
                return response()->json([
                    'error' => true,
                    "message" => "FactClotSessPDA is set false",
                    "data" => null
                ]);
            }


        } catch (Exception $e) {
            return response()->json([
                'error' => true,
                "message" => $e->getMessage(),
                "data" => null
            ]);
        }
    }
    public function closeSession($connection, $SC_IdSCaisse)
    {
        $connection->statement('exec CloturerCaisseByIdSession ?', array($SC_IdSCaisse));

        $c = $connection->table('ville')->where('FactClotSessPDA', '=', true)->first();

        if (!!$c) {
            $inserted = $connection->statement('exec FacturationClotSessPDA ?',  array($SC_IdSCaisse));
            if ($inserted) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }


    /**
     * @OA\Post(
     *     path="/SessionCaisse/getSessionCaisses",
     *     tags={"Sessions Caisse Mobile"},
     *     summary="Récupérer les sessions de caisse",
     *     description="Retourne la liste des sessions de caisse avec filtres optionnels par utilisateur et station",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Paramètres de connexion",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="connexion",
     *                 ref="#/components/schemas/DatabaseConnection"
     *             )
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="user",
     *         in="header",
     *         description="Code utilisateur pour filtrer les sessions",
     *         required=false,
     *         @OA\Schema(type="string", example="USER001")
     *     ),
     *     @OA\Parameter(
     *         name="station",
     *         in="query",
     *         description="Code station pour filtrer les sessions",
     *         required=false,
     *         @OA\Schema(type="string", example="ST001")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Liste des sessions récupérée avec succès",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/SessionCaisse")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function getSessionCaisse(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $user = $request->header('user');
        $result = $connection->table('SessionCaisse');
        if(!!$request->input("station"))
        {
            if(!!$user)
            {
                $result->where('SC_Station','=',$request->input("station"))
                    ->where('SC_CodeUtilisateur','=',$user)
                    ->orderby('SC_DateHeureOuv', 'desc');
            }else
            {
                $result
                    ->where('SC_Station','=',$request->input("station"))
                    ->orderby('SC_DateHeureOuv', 'desc');
            }

        }else{
            if(!!$user) {
                $result
                    ->where('SC_CodeUtilisateur', '=', $user)
                    ->orderby('SC_DateHeureOuv', 'desc');
            }else
            {
                $result->orderby('SC_DateHeureOuv', 'desc');
            }
        }

        return response()->json($result->get());
    }

    public function getSessionCaisseByX(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('SessionCaisse')->where($request->field, $request->value)->get());

    }

    public function getSessionCaisseByUser(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        $response = $connection->table('SessionCaisse')->where('SC_CodeUtilisateur', $data["object"])
            ->where('SC_ClotCaisse', 0)->orderByDesc('SC_DateHeureCrea')->first();
        return response()->json($response);

    }

    public function getSessionCaisseByDate(Request $request)
    {
        $data = $request->json()->all();

        $date_from = $data['object']['date_from'];
        $date_to = $data['object']['date_to'];
        $connection = DatabaseConnection::setConnection($data);
        if(!!$request->input("station"))
        {
            $result = $connection->table('SessionCaisse')
                ->join('utilisateur','Code_Ut','=','SC_CodeUtilisateur')
                ->where('SC_Station','=',$request->input("station"))
                ->whereBetween('SC_DateHeureCrea', [$date_from, $date_to])
                //->orWhereBetween('SC_DateHeureClot', [$date_from, $date_to])
                ->orderby('SC_DateHeureOuv', 'desc')->tosql();
        }else{
            $result =  $connection->table('SessionCaisse')
                ->join('utilisateur','Code_Ut','=','SC_CodeUtilisateur')
                ->whereBetween('SC_DateHeureCrea', [$date_from, $date_to])
                //->orWhereBetween('SC_DateHeureClot', [$date_from, $date_to])
                ->orderby('SC_DateHeureOuv', 'desc')->get();
        }
        return response()->json($result);

    }


    public function addSessionVendeur(Request $request)
    {

        try {
            $data = $request->json()->all();
            $connection = DatabaseConnection::setConnection($data);
            $value = ($connection->select("SELECT TOP (1) Exercice_Code FROM  Exercice WHERE (Exercice_Etat = 'Actif')"));
            if (!!$value && sizeof($value)) {
                $session = ((new PrefixWSMobile)->getPrefixSessionCaisse($data, "SessionCaisse",
                    ($value[0]->Exercice_Code),
                    $data["object"]['utilisateur']));
                $inserted = $connection->statement('exec Creation_Session_vendeur ?,?,?,?,?,?,?', array(
                    $session,
                    $data["object"]['caisse'],
                    $data["object"]['utilisateur'],
                    $data["object"]['carnet'],
                    $data["object"]['station'],
                    $data["object"]['fondCaisse'],
                    $data["object"]['nomMachine']
                ));
                $this->makeBackup($data["connexion"]['id_base_config'],
                    "Session",
                    "user".$data["object"]['utilisateur'],
                    $data["object"],
                    'insert'
                );
                if($inserted)
                {
                    $sessionInserted = $connection->table('SessionCaisse')
                        ->where('SC_IdSCaisse','=',$session)->first();
                    if($sessionInserted)
                    {
                        return response()->json($sessionInserted);
                    }else{
                        return response()->json(false);
                    }

                }else{
                    return response()->json($inserted);
                }

            } else {
                return response()->json(false);
            }
        } catch (Exception $e) {
            return response()->json(false);
        }
    }

    public function addSessionCaisse(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        $data['SC_IdSCaisse'] = (new PrefixWSMobile)->getPrefix($connection, "SessionCaisse", true, "/");

        if (!empty ($data)) {
            $s = $connection->table('SessionCaisse')->insert($data);

            return response()->json($s);

        } else {
            return (false);
        }


    }

    public function closeSessionVendeur(Request $request)
    {

        try {
            $data = $request->json()->all();

            $connection = DatabaseConnection::setConnection($data);

            $connection->statement('exec CloturerCaisseByIdSession ?', array($data["object"]));

            $c = $connection->table('ville')->where('FactClotSessPDA', '=',true)->first();

            if (!!$c) {
                $connection->statement('exec FacturationClotSessPDA ?', array($data["object"]));
            }

            return response()->json(true);

        } catch (Exception $e) {
            return response()->json(false);
        }
    }


    public function updateSessionCaisse(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);


        $connection->table('SessionCaisse')->where('SC_IdSCaisse', $data["SC_IdSCaisse"])->update($data);

        return ("Data modified");


    }


    public function deleteSessionCaisse(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        $connection->table('SessionCaisse')->where('SC_IdSCaisse', $data["object"])->delete();
        return ("Data Deleted");
    }

    public static function VerifActiveSession($idSCaisse,$connection)
    {
        $verif = $connection->table('SessionCaisse')->where('SC_IdSCaisse',$idSCaisse)->pluck('SC_ClotCaisse')->fisrt();
        return response()->json((bool)$verif);

    }


}


