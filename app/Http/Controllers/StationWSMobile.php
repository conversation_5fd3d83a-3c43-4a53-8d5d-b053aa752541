<?php

namespace App\Http\Controllers;

use App\Helpers\DatabaseConnection;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class StationWSMobile extends Controller
{

    /*fonction get data from database*/

    public function getStations(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('station')->get());
    }

    public function getStationByCode(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('station')->where('STAT_Code', $data["object"])->first());
    }

    public function getStationByX(Request $request)
    {
        return response()->json(DB::table('station')->where($request->field, $request->value)->get());

    }

    public function getClientStations(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('station')->whereNotNull('CLI_Code')->get());

    }

    public function getClientStationsByUser(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('station')->whereNotNull('CLI_Code1')->where('Code_Ut1', $data["object"])->get());

    }

    public function DisplayListStation (Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        $result = $connection->table('station')
            ->select('station.*')
            ->get();

        return response()->json($result);

    }



}
