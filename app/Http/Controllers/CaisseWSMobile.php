<?php

namespace App\Http\Controllers;
use App\Helpers\AppHelper;
use Illuminate\Http\Request;
use App\Http\Requests;
use Illuminate\Http\Response;
use App\Helpers\DatabaseConnection;
use App\Http\Controllers\PrefixWSMobile;
use Illuminate\Support\Carbon;
use DateTime;

/**
 * @OA\Tag(
 *     name="Caisses Mobile",
 *     description="Gestion des caisses et consultations depuis l'application mobile ProCaisse"
 * )
 */
class CaisseWSMobile extends Controller
{

    /**
     * @OA\Post(
     *     path="/Caisse/getCaisses",
     *     tags={"Caisses Mobile"},
     *     summary="Récupérer toutes les caisses",
     *     description="Retourne la liste complète des caisses disponibles",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Paramètres de connexion",
     *         @OA\JsonContent(ref="#/components/schemas/StandardRequest")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Liste des caisses récupérée avec succès",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/Caisse")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function getCaisse(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('Caisse')->get());
    }

    public function getCaisseByX(Request $request)
    {
        return response()->json(DB::table('Caisse')->where($request->field, $request->value)->get());

    }

    public function addCaisse(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $data['SC_IdSCaisse'] =(new PrefixWSMobile)->getPrefix($connection,"Caisse", true,"/");


        if ( !empty ( $data ) ) {
            $s = 	$connection->table('Caisse')->insert($data);

            return response()->json($s);

        }
        else{
            return(false);
        }


    }
    public function updateCaisse(Request $request )
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);

        $connection->table('Caisse')->where('SC_IdSCaisse',$data["SC_IdSCaisse"])->update($data);

        return("Data modified");

    }

    public function deleteCaisse(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        $connection->table('Caisse')->where('SC_IdSCaisse', $request->id)->delete();
        return("Data Deleted");
    }

    public function CalculSommeVente(Request $request )
    {
        $bl_total = $this->CalculSommeBonLivraison($request);
        $ticket_total = $this->CalculSommeTicket($request);

        $Marge_total = $this->CalculSommeMargeVente($request);
        $vente_total =$bl_total +$ticket_total;
        $Depense_total = $this->CalculTotalDepense($request);
        $benefice= $Marge_total -$Depense_total;
        $Remise_total= $this->CalculTotalRemiseHT($request);

        $result = [
            'bl_total' => $bl_total,
            'ticket_total' => $ticket_total,
            'vente_total' => $vente_total,
            'Marge_total' =>$Marge_total,
            'Depense_total' =>$Depense_total,
            'Benefice' =>$benefice,
            'Remise_total' => $Remise_total

        ];
        return response()->json($result, 201);
    }
    public function CalculSommeBonLivraison(Request $request)
    {
        $data = $request->json()->all();
        $date_from =  AppHelper::setDateFormat($data["object"]["date_from"]);
        $date_to =  AppHelper::setDateFormat($data["object"]["date_to"]);
        $station = $data["object"]["station"];

        $connection = DatabaseConnection::setConnection($data);
        if($station == null)
        {
            return $connection->table('View_B9_Final')
                ->select($connection->raw('SUM(Mnt_TTC) as total_livraison'))
                ->whereBetween('Date', [$date_from, $date_to])
                ->where('_Type','=','Bon Livraison')
                ->first()->total_livraison;
            /*   return $connection->table('bon_livraison')
                   ->select($connection->raw('ISNULL(SUM(bon_liv_montant),0) as total_livraison'))
                   ->whereBetween('BON_LIV_Date', [$date_from, $date_to])
                   ->first()->total_livraison;*/
        }
        else{
            return $connection->table('View_B9_Final')
                ->select($connection->raw('SUM(Mnt_TTC) as total_livraison'))
                ->whereBetween('Date', [$date_from, $date_to])
                ->where('_Type','=','Bon Livraison')
                ->where('station',"=",$station)
                ->first()->total_livraison;
            /*  return $connection->table('bon_livraison')
                ->select($connection->raw('ISNULL(SUM(bon_liv_montant),0) as total_livraison'))
               ->whereBetween('bon_liv_date', [$date_from, $date_to])
               ->where('bon_livraison.BON_LIV_StationOrigine', '=', $station)
               ->first()->total_livraison;*/
        }

    }
    public function CalculSommeTicket(Request $request)
    {
        $data = $request->json()->all();
        $date_from = AppHelper::setDateFormat($data["object"]["date_from"]);
        $date_to = AppHelper::setDateFormat($data["object"]["date_to"]);
        $station = $data["object"]["station"];
        $connection = DatabaseConnection::setConnection($data);

        if($station == null) {
            return $connection->table('View_B9_Final')
                ->select($connection->raw('SUM(Mnt_TTC) as total_ticket'))
                ->whereBetween('Date', [$date_from, $date_to])
                ->where('_Type','=','Ticket')
                ->first()->total_ticket;

            /*
                        return $connection->table('ticket')
                            ->select($connection->raw('SUM(tik_mtttc) as total_ticket'))
                            ->whereBetween('TIK_DateHeureTicket', [$date_from, $date_to])
                            ->where('ticket.tik_annuler', '=', 0)
                            ->first()->total_ticket;*/
        }
        else{
            return $connection->table('View_B9_Final')
                ->select($connection->raw('SUM(Mnt_TTC) as total_ticket'))
                ->whereBetween('Date', [$date_from, $date_to])
                ->where('_Type','=','Ticket')
                ->where('station',"=",$station)
                ->first()->total_ticket;
            /* return $connection->table('ticket')
                 ->leftJoin('SessionCaisse', 'SessionCaisse.SC_IdSCaisse', '=', 'ticket.TIK_IdSCaisse')
                 ->leftJoin('Caisse', 'Caisse.CAI_IdCaisse', '=', 'SessionCaisse.SC_Caisse')
                 ->select($connection->raw('SUM(tik_mtttc) as total_ticket'))
                 ->whereBetween('tik_dateheureticket', [$date_from, $date_to])
                 ->where('Caisse.CAI_Station',"=",$station)
                 ->where('ticket.tik_annuler','=', 0)
                 ->first()->total_ticket;*/
        }

    }

    public function CalculSommeMargeVente (Request $request)
    {
        $data = $request->json()->all();
        $date_from = AppHelper::setDateFormat($data["object"]["date_from"]);
        $date_to = AppHelper::setDateFormat($data["object"]["date_to"]);
        $station = ($data["object"]["station"]);

        $connection = DatabaseConnection::setConnection($data);
        if($station == null) {
            return $connection->table('View_B9')
                ->select($connection->raw('SUM(B9TTC) as Marge_total'))
                ->whereBetween('TIK_DateHeureTicket', [$date_from, $date_to])
                ->first()->Marge_total;
        }
        else{
            return $connection->table('View_B9')
                ->select($connection->raw('SUM(B9TTC) as Marge_total'))
                ->whereBetween('TIK_DateHeureTicket', [$date_from, $date_to])
                ->where('STAT_Code',"=",$station)
                ->first()->Marge_total;
        }
    }
    public function CalculTotalRemiseHT (Request $request)
    {

        $data = $request->json()->all();
        $date_from = AppHelper::setDateFormat($data["object"]["date_from"]);
        $date_to = AppHelper::setDateFormat($data["object"]["date_to"]);
        $station = $data["object"]["station"];
        $connection = DatabaseConnection::setConnection($data);

        if($station == null){
            return $connection->table('View_B9_Final')
                ->select($connection->raw('SUM(TotRemiseHT) as Remise_total'))
                ->whereBetween('Date', [$date_from, $date_to])
                ->first()->Remise_total;
        }
        else{
            $connection->table('View_B9_Final')
                ->select($connection->raw('SUM(TotRemiseHT) as Remise_total'))
                ->whereBetween('Date', [$date_from, $date_to])
                ->where('station','=',$station)
                ->first()->Remise_total;
        }

    }

    public function CalculTotalDepense (Request $request)
    {
        $data = $request->json()->all();
        $date_from = AppHelper::setDateFormat($data["object"]["date_from"]);
        $date_to =  AppHelper::setDateFormat($data["object"]["date_to"]);
        $station = $data["object"]["station"];
        $connection = DatabaseConnection::setConnection($data);
        if($station == null) {
            return $connection->table('View_depenseCaisse')
                ->select($connection->raw('SUM(DEP_Montant) as montant_depense'))
                ->whereBetween('DDm', [$date_from, $date_to])
                ->first()->montant_depense;
        }
        else{
            return $connection->table('View_depenseCaisse')
                ->select($connection->raw('SUM(DEP_Montant) as montant_depense'))
                ->whereBetween('ddm', [$date_from, $date_to])
                ->where('STAT_Code', '=', $station)
                ->first()->montant_depense;
        }

    }
    public function DisplayDetailVente (Request $request)
    {
        $data = $request->json()->all();
        $date_from = AppHelper::setDateFormat($data["object"]["date1"]);
        $date_to = AppHelper::setDateFormat($data["object"]["date2"]);
        $station = $data["object"]["station"];
        $connection = DatabaseConnection::setConnection($data);
        if($station == null){
            $result = $connection->table('View_B9_Final')
                ->groupBy('Art_desg')
                ->selectRaw('Art_desg,sum(B9) as marge,sum(Mnt_TTC) as sum,sum(qte) as qte')
                ->whereBetween('Date', [$date_from, $date_to])
                ->get();
            return response()->json($result, 201);
        }
        $result = $connection->table('View_B9_Final')
            ->groupBy('Art_desg')
            ->selectRaw('Art_desg,sum(B9) as marge,sum(Mnt_TTC) as sum,sum(qte) as qte')
            ->whereBetween('Date', [$date_from, $date_to])
            ->where('View_B9_Final.station', '=', $station)
            ->get();
        return response()->json($result, 201);
    }
    public function CalculSommeVentev1ByStation (Request $request)
    {
        $data = $request->json()->all();
        $date_from = AppHelper::setDateFormat($data["object"]["date1"]);
        $date_to = AppHelper::setDateFormat( $data["object"]["date2"]);
        $station = $data["object"]["station"];
        $connection = DatabaseConnection::setConnection($data);
        $result = $connection->table('View_B9_Final')
            ->select($connection->raw('SUM(Mnt_TTC) as total_vente_par_station'))
            ->whereBetween('Date', [$date_from, $date_to])
            ->where('View_B9_Final.station', '=', $station)
            ->get();
        return response()->json($result, 201);
    }
    public function CalculSommeVenteAchatByStation (Request $request)
    {
        $data = $request->json()->all();
        $date_from = AppHelper::setDateFormat($data["object"]["date1"]);
        $date_to = AppHelper::setDateFormat($data["object"]["date2"]);
        $station = $data["object"]["station"];

        $connection = DatabaseConnection::setConnection($data);
        $result = $connection->table('bon_entree')
            ->select($connection->raw('SUM(bon_ent_mntttc) as total_achat'))
            ->whereBetween('bon_ent_date', [$date_from, $date_to])
            ->where('bon_entree.bon_ent_stationentree', '=', $station)
            ->first();

        return $result;
    }

    public function CalculValeurStockByStation (Request $request )
    {
        $data = $request->json()->all();
        $station = $data["object"]["station"];
        $connection = DatabaseConnection::setConnection($data);

        $result = $connection->table('View_StockDec')
            ->select($connection->raw('SUM(View_StockDec.SART_QteDeclaree) as SumSTock'))
            ->where('STAT_Code', '=', $station)
            ->first();

        return $result ;
    }

    public function CalculTotalDepenseByCaissier (Request $request)
    {
        $data = $request->json()->all();
        $date_from =  AppHelper::setDateFormat($data["object"]["date_from"]);
        $date_to =  AppHelper::setDateFormat($data["object"]["date_to"]);
        $caissier = $data["object"]["id_caissier"];
        $connection = DatabaseConnection::setConnection($data);
        return $connection->table('View_depenseCaisse')
            ->select($connection->raw('SUM(DEP_Montant) as total_depense'))
            ->whereBetween('DDm', [$date_from, $date_to])
            ->where('View_depenseCaisse.Code_Ut',$caissier)
            ->first()->total_depense;

    }
    public function CalculSommeBonLivraisonByCaissier(Request $request)
    {
        $data = $request->json()->all();
        $date_from =  AppHelper::setDateFormat($data["object"]["date_from"]);
        $date_to = AppHelper::setDateFormat($data["object"]["date_to"]);
        $caissier = $data["object"]["id_caissier"];

        $connection = DatabaseConnection::setConnection($data);
        return $connection->table('bon_livraison')

            ->select($connection->raw('SUM(bon_liv_montant) as total_livraison'))
            ->whereBetween('bon_liv_date', [$date_from, $date_to])
            ->where('bon_livraison.BON_LIV_User', '=', $caissier)
            ->first()->total_livraison;

    }
    public function CalculSommeTicketByCaissier(Request $request)
    {
        $data = $request->json()->all();
        $date_from = AppHelper::setDateFormat($data["object"]["date_from"]);
        $date_to =  AppHelper::setDateFormat($data["object"]["date_to"]);
        $caissier = $data["object"]["id_caissier"];

        $connection = DatabaseConnection::setConnection($data);
        return $connection->table('ticket')
            ->select($connection->raw('SUM(tik_mtttc) as total_ticket'))
            ->whereBetween('tik_dateheureticket', [$date_from, $date_to])
            ->where('ticket.TIK_user',"=",$caissier)
            ->where('ticket.tik_annuler','=', 0)
            ->first()->total_ticket;

    }
    public function CalculMargeByCaissier (Request $request)
    {
        $data = $request->json()->all();
        $date_from =  AppHelper::setDateFormat($data["object"]["date_from"]);
        $date_to =  AppHelper::setDateFormat($data["object"]["date_to"]);
        $caissier = $data["object"]["id_caissier"];

        $connection = DatabaseConnection::setConnection($data);
        return $connection->table('View_B9')
            ->select($connection->raw('SUM(B9) as Marge_total'))
            ->whereBetween('TIK_DateHeureTicket', [$date_from, $date_to])
            //->where('STAT_Code',"=",$id_station)
            ->first()->Marge_total;
    }

    public function DisplayTotalByCaissier(Request $request)
    {
        $bl_total = $this->CalculSommeBonLivraisonByCaissier($request);
        $ticket_total = $this->CalculSommeTicketByCaissier($request);
        $Marge_total = $this->CalculMargeByCaissier($request);
        $Depense_total = $this->CalculTotalDepenseByCaissier($request);
        $vente_total =$bl_total +$ticket_total;
        $benefice= $Marge_total -$Depense_total;
        $result = [
            'bl_total' => $bl_total,
            'ticket_total' => $ticket_total,
            'vente_total' => $vente_total,
            'Marge_total' =>$Marge_total,
            'Depense_total' =>$Depense_total,
            'Benefice' =>$benefice

        ];
        return response()->json($result, 201);
    }
    public function DisplayBenificeStation (Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $date_from =  AppHelper::setDateFormat($data["object"]["date_from"]);
        $date_to =  AppHelper::setDateFormat($data["object"]["date_to"]);
        $results = array();

        $stations = $connection->table('station')
            ->select('STAT_Code','STAT_Desg')
            ->get();

        for ($index = 0; $index < sizeof($stations); $index++) {
            $item = array();
            // $item["station"] = [$stations[$index],$depense,$marge] ;
            $depense =$connection->table('View_depenseCaisse')
                ->groupby('STAT_Code','STAT_Desg')
                ->selectRaw('SUM(DEP_Montant) as montant_depense ')
                ->whereBetween('ddm', [$date_from, $date_to])
                ->where('View_depenseCaisse.STAT_Code', '=', $stations[$index]->STAT_Code)
                ->first();

            $marge=$connection->table('View_B9')
                ->select($connection->raw('SUM(B9) as Marge_total'))
                ->where('View_B9.STAT_Code', '=', $stations[$index]->STAT_Code)
                ->whereBetween('TIK_DateHeureTicket', [$date_from, $date_to])
                ->first();
            $item["station"] = $stations[$index];
            $item["station"]->depense= $depense;
            $item["station"]->marge= $marge;

            $results[] = $item;

        }

        return response()->json($results, 201);

    }

    public function getRecapVente (Request $request)
    {
        $data = $request->json()->all();
        $station = $request->input('station');
        $id_session = $data["object"]["id_session"];
        $date_from = $data["object"]["date_from"];
        $date_to = $data["object"]["date_to"];
        $connection = DatabaseConnection::setConnection($data);

        $result = $connection->table('View_Journal_Reglement_001_3')
            ->groupBy('SC_FondCaisse','SC_TotDepense')
            ->select('SC_FondCaisse as SC_FondCaisse', 'SC_TotDepense as SC_TotDepense',
                $connection->raw('SUM(Mnt_Espece) as Mnt_Espece ,SUM(Mnt_Cheque) as Mnt_Cheque, SUM(Mnt_Carte_Bancaire) as Mnt_Carte_Bancaire , 
                    SUM(MntBonAchat) as MntBonAchat'))
            ->whereBetween('REGC_DateReg', [$date_from, $date_to]);


        if($id_session === null)
        {
            if(!!$station)
            {
                $result->where('REGC_IdStation','=',$station);

            }

            if($result->get() === null)
            {
                $recap_vente  = [
                    'total_recette' =>0,
                    'total_caisse' =>0,
                    'fond_caisse' => 0,
                    'depense' => 0,
                    'details_recette'=> [
                        'espece'=>0,
                        'cate_bancaire'=>0,
                        'bon_achat'=>0,
                        'Mnt_cheque'=>0]
                ];
                return response()->json($recap_vente, 201);
            }else {
                $espece = 0;
                $carte_bancaire = 0;
                $Mnt_Cheque = 0;
                $bon_achat = 0;
                $depense = 0;
                $fond_caisse = 0;

                foreach ($result->get() as $item) {
                    $espece += $item->Mnt_Espece;
                    $carte_bancaire += $item->Mnt_Carte_Bancaire;
                    $Mnt_Cheque += $item->Mnt_Cheque;
                    $bon_achat += $item->MntBonAchat;
                    $depense += $item->SC_TotDepense;
                    $fond_caisse += $item->SC_FondCaisse;

                }
                $total_recette = $espece + $carte_bancaire + $bon_achat + $Mnt_Cheque;
                $total_caisse = ($total_recette + $fond_caisse) - $depense;

                $recap_vente  = [
                    'total_recette' => $total_recette,
                    'total_caisse' => $total_caisse,
                    'fond_caisse' =>(double) $fond_caisse,
                    'depense' => (double) $depense,
                    'details_recette'=> [
                        'espece'=>$espece,
                        'cate_bancaire'=>$carte_bancaire,
                        'bon_achat'=>$bon_achat,
                        'Mnt_cheque'=>$Mnt_Cheque
                    ]

                ];
            }
        }
        else {

            $result->where('View_Journal_Reglement_001_3.REGC_IdSCaisse', '=', $id_session);
            if(!!$station)
            {
                $result->where('REGC_IdStation','=',$station);

            }

            if($result->first() === null)
            {
                $recap_vente = [
                    'total_recette' =>0,
                    'total_caisse' =>0,
                    'fond_caisse' => 0,
                    'depense' => 0,
                    'details_recette'=> [ 'espece'=>0,
                        'cate_bancaire'=>0,
                        'bon_achat'=>0,
                        'Mnt_cheque'=>0]

                ];
                return response()->json($recap_vente, 201);
            }else {
                $espece = $result->first()->Mnt_Espece;
                $carte_bancaire = $result->first()->Mnt_Carte_Bancaire;
                $Mnt_Cheque = $result->first()->Mnt_Cheque;
                $bon_achat = $result->first()->MntBonAchat;
                $depense = $result->first()->SC_TotDepense;
                $fond_caisse = $result->first()->SC_FondCaisse;
                $total_recette = $espece + $carte_bancaire + $bon_achat + $Mnt_Cheque;
                $total_caisse = ($total_recette + $fond_caisse) - $depense;

                $recap_vente = [
                    'total_recette' => $total_recette,
                    'total_caisse' => $total_caisse,
                    'fond_caisse' => (double) $fond_caisse,
                    'depense' => (double) $depense,
                    'details_recette'=> [
                        'espece'=>$espece,
                        'cate_bancaire'=>$carte_bancaire,
                        'bon_achat'=>$bon_achat,
                        'Mnt_cheque'=>$Mnt_Cheque
                    ]
                ];
            }
        }

        return response()->json($recap_vente, 200);

    }

}


