<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests;
use Illuminate\Http\Response;
use App\Helpers\DatabaseConnection;

/**
 * @OA\Tag(
 *     name="Banques Mobile",
 *     description="Gestion des banques depuis l'application mobile ProCaisse"
 * )
 */
class BanqueWSMobile extends Controller
{
    /**
     * @OA\Post(
     *     path="/Banque/getBanques",
     *     tags={"Banques Mobile"},
     *     summary="Récupérer toutes les banques",
     *     description="Retourne la liste complète des banques disponibles",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Paramètres de connexion",
     *         @OA\JsonContent(ref="#/components/schemas/StandardRequest")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Liste des banques récupérée avec succès",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/Banque")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
	public function getBanque(Request $request)
	 {  $data = $request->json()->all();

	 	$connection = DatabaseConnection::setConnection($data);

	return response()->json($connection->table('banque')->get());

	 }




}





