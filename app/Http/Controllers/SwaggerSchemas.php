<?php

namespace App\Http\Controllers;

/**
 * @OA\Schema(
 *     schema="StandardRequest",
 *     type="object",
 *     description="Structure standard de requête ProCaisse",
 *     @OA\Property(
 *         property="connexion",
 *         ref="#/components/schemas/DatabaseConnection",
 *         description="Paramètres de connexion à la base de données"
 *     ),
 *     @OA\Property(
 *         property="object",
 *         ref="#/components/schemas/PaginationRequest",
 *         description="Paramètres de pagination"
 *     )
 * )
 * 
 * @OA\Schema(
 *     schema="StandardRequestWithData",
 *     type="object",
 *     description="Structure standard de requête ProCaisse avec données",
 *     @OA\Property(
 *         property="connexion",
 *         ref="#/components/schemas/DatabaseConnection",
 *         description="Paramètres de connexion à la base de données"
 *     ),
 *     @OA\Property(
 *         property="object",
 *         type="object",
 *         description="Données à traiter"
 *     )
 * )
 * 
 * @OA\Schema(
 *     schema="ArticleClasseRemise",
 *     type="object",
 *     description="Classe de remise d'article",
 *     @OA\Property(property="ART_CLASS_REM_Code", type="string", example="REM001", description="Code de la classe de remise"),
 *     @OA\Property(property="ART_CLASS_REM_ArtCode", type="string", example="ART001", description="Code de l'article"),
 *     @OA\Property(property="ART_CLASS_REM_DDm", type="string", nullable=true, example="2024-01-01", description="Date de début"),
 *     @OA\Property(property="ART_CLASS_REM_export", type="string", nullable=true, example="Y", description="Statut d'export"),
 *     @OA\Property(property="ART_CLASS_REM_station", type="string", nullable=true, example="ST001", description="Code station"),
 *     @OA\Property(property="ART_CLASS_REM_user", type="string", nullable=true, example="USER001", description="Code utilisateur")
 * )
 *
 * @OA\Schema(
 *     schema="Article",
 *     type="object",
 *     description="Article du système ProCaisse",
 *     @OA\Property(property="id", type="string", example="123e4567-e89b-12d3-a456-426614174000", description="Identifiant unique"),
 *     @OA\Property(property="code", type="string", example="ART001", description="Code de l'article"),
 *     @OA\Property(property="libelle", type="string", example="Article de test", description="Libellé de l'article"),
 *     @OA\Property(property="libelleCourte", type="string", example="Art test", description="Libellé court"),
 *     @OA\Property(property="photo", type="string", nullable=true, example="article_001.jpg", description="Photo de l'article"),
 *     @OA\Property(property="active", type="string", example="1", description="Statut actif (1=actif, 0=inactif)"),
 *     @OA\Property(property="isStockable", type="string", example="1", description="Article stockable (1=oui, 0=non)"),
 *     @OA\Property(property="prixuht", type="number", format="float", example=25.50, description="Prix unitaire HT"),
 *     @OA\Property(property="tauxTva", type="number", format="float", example=20.00, description="Taux de TVA"),
 *     @OA\Property(property="codeFamille", type="string", nullable=true, example="FAM001", description="Code famille"),
 *     @OA\Property(property="libelleFamille", type="string", nullable=true, example="Famille test", description="Libellé famille"),
 *     @OA\Property(property="codeMarque", type="string", nullable=true, example="MAR001", description="Code marque"),
 *     @OA\Property(property="libellleMarque", type="string", nullable=true, example="Marque test", description="Libellé marque"),
 *     @OA\Property(property="codeabare", type="string", nullable=true, example="1234567890123", description="Code à barres")
 * )
 *
 * @OA\Schema(
 *     schema="Marque",
 *     type="object",
 *     description="Marque d'article",
 *     @OA\Property(property="id", type="string", example="123e4567-e89b-12d3-a456-426614174000", description="Identifiant unique"),
 *     @OA\Property(property="code", type="string", example="MAR001", description="Code de la marque"),
 *     @OA\Property(property="libelle", type="string", example="Nike", description="Libellé de la marque"),
 *     @OA\Property(property="active", type="boolean", example=true, description="Statut actif"),
 *     @OA\Property(property="isSync", type="boolean", example=false, description="Statut de synchronisation"),
 *     @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-01T10:00:00Z"),
 *     @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-01T10:00:00Z")
 * )
 *
 * @OA\Schema(
 *     schema="MarqueInput",
 *     type="object",
 *     description="Données d'entrée pour une marque",
 *     @OA\Property(property="id", type="string", nullable=true, example="123e4567-e89b-12d3-a456-426614174000", description="ID pour mise à jour (optionnel pour création)"),
 *     @OA\Property(property="idMobile", type="string", nullable=true, example="mobile_123", description="ID mobile pour synchronisation"),
 *     @OA\Property(property="code", type="string", example="MAR001", description="Code de la marque"),
 *     @OA\Property(property="libelle", type="string", example="Nike", description="Libellé de la marque"),
 *     @OA\Property(property="active", type="boolean", example=true, description="Statut actif")
 * )
 *
 * @OA\Schema(
 *     schema="EtatStock",
 *     type="object",
 *     description="État détaillé du stock d'un article",
 *     @OA\Property(property="code", type="string", example="ART001", description="Code de l'article"),
 *     @OA\Property(property="libelle", type="string", example="Article test", description="Libellé de l'article"),
 *     @OA\Property(property="libelleCourte", type="string", example="Art test", description="Libellé court"),
 *     @OA\Property(property="photo", type="string", nullable=true, example="article_001.jpg", description="Photo de l'article"),
 *     @OA\Property(property="qte", type="number", format="float", example=100.5, description="Quantité en stock"),
 *     @OA\Property(property="qtee", type="number", format="float", example=50.0, description="Quantité entrée"),
 *     @OA\Property(property="qtAV", type="number", format="float", example=25.0, description="Quantité avant vente"),
 *     @OA\Property(property="qts", type="number", format="float", example=75.0, description="Quantité sortie"),
 *     @OA\Property(property="prixuht", type="number", format="float", example=25.50, description="Prix unitaire HT"),
 *     @OA\Property(property="tauxTva", type="number", format="float", example=20.00, description="Taux de TVA"),
 *     @OA\Property(property="cmpArt", type="number", format="float", example=20.00, description="Coût moyen pondéré"),
 *     @OA\Property(property="tot_cmp", type="number", format="float", example=2010.00, description="Total coût moyen pondéré"),
 *     @OA\Property(property="tot_pa", type="number", format="float", example=1800.00, description="Total prix d'achat"),
 *     @OA\Property(property="tot_pv", type="number", format="float", example=2562.75, description="Total prix de vente"),
 *     @OA\Property(property="codestation", type="string", example="ST001", description="Code de la station"),
 *     @OA\Property(property="stationArt", type="string", example="Station principale", description="Libellé de la station"),
 *     @OA\Property(property="codeabare", type="string", nullable=true, example="1234567890123", description="Code à barres"),
 *     @OA\Property(property="libelleFamille", type="string", nullable=true, example="Famille test", description="Libellé famille"),
 *     @OA\Property(property="libelleMarque", type="string", nullable=true, example="Marque test", description="Libellé marque")
 * )
 *
 * @OA\Schema(
 *     schema="Taille",
 *     type="object",
 *     description="Taille d'article",
 *     @OA\Property(property="TAI_Code", type="string", example="TAI001", description="Code de la taille"),
 *     @OA\Property(property="TAI_Taille", type="string", example="M", description="Libellé de la taille"),
 *     @OA\Property(property="TAI_Station", type="string", nullable=true, example="ST001", description="Code station"),
 *     @OA\Property(property="TAI_User", type="string", nullable=true, example="USER001", description="Code utilisateur"),
 *     @OA\Property(property="DDm", type="string", nullable=true, example="2024-01-01", description="Date de début"),
 *     @OA\Property(property="export", type="string", nullable=true, example="Y", description="Statut d'export")
 * )
 *
 * @OA\Schema(
 *     schema="TailleInput",
 *     type="object",
 *     description="Données d'entrée pour une taille",
 *     @OA\Property(property="TAI_Taille", type="string", example="M", description="Libellé de la taille"),
 *     @OA\Property(property="TAI_Station", type="string", nullable=true, example="ST001", description="Code station"),
 *     @OA\Property(property="TAI_User", type="string", nullable=true, example="USER001", description="Code utilisateur"),
 *     @OA\Property(property="T_user", type="string", nullable=true, example="USER001", description="Utilisateur de traitement"),
 *     @OA\Property(property="T_station", type="string", nullable=true, example="ST001", description="Station de traitement"),
 *     @OA\Property(property="DDm", type="string", nullable=true, example="2024-01-01", description="Date de début (peut être 'NULL')"),
 *     @OA\Property(property="export", type="string", nullable=true, example="Y", description="Statut d'export (peut être 'NULL')"),
 *     @OA\Property(property="T_export", type="string", nullable=true, example="Y", description="Statut d'export de traitement (peut être 'NULL')"),
 *     @OA\Property(property="T_DDm", type="string", nullable=true, example="2024-01-01", description="Date de début de traitement (peut être 'NULL')")
 * )
 *
 * @OA\Schema(
 *     schema="Famille",
 *     type="object",
 *     description="Famille d'articles",
 *     @OA\Property(property="id", type="string", example="123e4567-e89b-12d3-a456-426614174000", description="Identifiant unique"),
 *     @OA\Property(property="code", type="string", example="FAM001", description="Code de la famille"),
 *     @OA\Property(property="libelle", type="string", example="Électronique", description="Libellé de la famille"),
 *     @OA\Property(property="active", type="boolean", example=true, description="Statut actif"),
 *     @OA\Property(property="isSync", type="boolean", example=false, description="Statut de synchronisation"),
 *     @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-01T10:00:00Z"),
 *     @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-01T10:00:00Z")
 * )
 *
 * @OA\Schema(
 *     schema="FamilleInput",
 *     type="object",
 *     description="Données d'entrée pour une famille",
 *     @OA\Property(property="id", type="string", nullable=true, example="123e4567-e89b-12d3-a456-426614174000", description="ID pour mise à jour (optionnel pour création)"),
 *     @OA\Property(property="idMobile", type="string", nullable=true, example="mobile_123", description="ID mobile pour synchronisation"),
 *     @OA\Property(property="code", type="string", example="FAM001", description="Code de la famille"),
 *     @OA\Property(property="libelle", type="string", example="Électronique", description="Libellé de la famille"),
 *     @OA\Property(property="active", type="boolean", example=true, description="Statut actif")
 * )
 *
 * @OA\Schema(
 *     schema="MarqueMobile",
 *     type="object",
 *     description="Marque mobile",
 *     @OA\Property(property="MAR_Code", type="string", example="MAR001", description="Code de la marque"),
 *     @OA\Property(property="MAR_Designation", type="string", example="Nike", description="Désignation de la marque"),
 *     @OA\Property(property="Mar_Station", type="string", nullable=true, example="ST001", description="Code station"),
 *     @OA\Property(property="Mar_User", type="string", nullable=true, example="USER001", description="Code utilisateur")
 * )
 *
 * @OA\Schema(
 *     schema="ArticleDetail",
 *     type="object",
 *     description="Détails complets d'un article",
 *     @OA\Property(property="ART_Code", type="string", example="ART001", description="Code de l'article"),
 *     @OA\Property(property="ART_CodeBar", type="string", nullable=true, example="1234567890123", description="Code à barres"),
 *     @OA\Property(property="ART_Designation", type="string", example="Article de test", description="Désignation de l'article"),
 *     @OA\Property(property="ART_PrixUnitaireHT", type="number", format="float", example=25.50, description="Prix unitaire HT"),
 *     @OA\Property(property="ART_TVA", type="number", format="float", example=20.00, description="Taux de TVA"),
 *     @OA\Property(property="ART_QteStock", type="number", format="float", example=100.5, description="Quantité en stock"),
 *     @OA\Property(property="pvttc", type="number", format="float", example=30.60, description="Prix de vente TTC"),
 *     @OA\Property(property="photo_Path", type="string", nullable=true, example="/images/article_001.jpg", description="Chemin de la photo"),
 *     @OA\Property(property="MAR_Designation", type="string", nullable=true, example="Nike", description="Désignation de la marque"),
 *     @OA\Property(property="PrixSolde", type="number", format="float", nullable=true, example=20.00, description="Prix soldé"),
 *     @OA\Property(property="TauxSolde", type="number", format="float", example=0.0, description="Taux de solde"),
 *     @OA\Property(property="FAM_Lib", type="string", nullable=true, example="Électronique", description="Libellé de la famille")
 * )
 *
 * @OA\Schema(
 *     schema="Client",
 *     type="object",
 *     description="Client ProCaisse",
 *     @OA\Property(property="CLI_Code", type="string", example="CLI001", description="Code du client"),
 *     @OA\Property(property="CLI_Nom", type="string", example="Dupont", description="Nom du client"),
 *     @OA\Property(property="CLI_Prenom", type="string", nullable=true, example="Jean", description="Prénom du client"),
 *     @OA\Property(property="CLI_Adresse", type="string", nullable=true, example="123 Rue de la Paix", description="Adresse du client"),
 *     @OA\Property(property="CLI_Telephone", type="string", nullable=true, example="+33123456789", description="Téléphone du client"),
 *     @OA\Property(property="CLI_Email", type="string", nullable=true, example="<EMAIL>", description="Email du client"),
 *     @OA\Property(property="CLI_Type", type="string", example="Particulier", description="Type de client"),
 *     @OA\Property(property="CLI_Station", type="string", nullable=true, example="ST001", description="Code station"),
 *     @OA\Property(property="CLI_DDm", type="string", format="date-time", example="2024-01-01T10:00:00Z", description="Date de dernière modification"),
 *     @OA\Property(property="Clt_Info1", type="string", nullable=true, example="info", description="Information complémentaire 1")
 * )
 *
 * @OA\Schema(
 *     schema="ClientWithSolde",
 *     type="object",
 *     description="Client avec informations de solde",
 *     @OA\Property(property="CLI_Code", type="string", example="CLI001", description="Code du client"),
 *     @OA\Property(property="CLI_Nom", type="string", example="Dupont", description="Nom du client"),
 *     @OA\Property(property="CLI_Prenom", type="string", nullable=true, example="Jean", description="Prénom du client"),
 *     @OA\Property(property="CodeClient", type="string", example="CLI001", description="Code client (référence)"),
 *     @OA\Property(property="SoldeClient", type="number", format="float", example=1250.75, description="Solde du client"),
 *     @OA\Property(property="CreditAutorise", type="number", format="float", example=5000.00, description="Crédit autorisé"),
 *     @OA\Property(property="SoldeDisponible", type="number", format="float", example=3749.25, description="Solde disponible")
 * )
 *
 * @OA\Schema(
 *     schema="ClientInput",
 *     type="object",
 *     description="Données d'entrée pour un client",
 *     @OA\Property(property="CLI_Code", type="string", example="CLI001", description="Code du client"),
 *     @OA\Property(property="CLI_Nom", type="string", example="Dupont", description="Nom du client"),
 *     @OA\Property(property="CLI_Prenom", type="string", nullable=true, example="Jean", description="Prénom du client"),
 *     @OA\Property(property="CLI_Adresse", type="string", nullable=true, example="123 Rue de la Paix", description="Adresse du client"),
 *     @OA\Property(property="CLI_Telephone", type="string", nullable=true, example="+33123456789", description="Téléphone du client"),
 *     @OA\Property(property="CLI_Email", type="string", nullable=true, example="<EMAIL>", description="Email du client"),
 *     @OA\Property(property="CLI_Type", type="string", example="Particulier", description="Type de client"),
 *     @OA\Property(property="CLI_Station", type="string", nullable=true, example="ST001", description="Code station"),
 *     @OA\Property(property="CLI_Code_M", type="string", nullable=true, example="MOBILE001", description="Code mobile pour synchronisation"),
 *     @OA\Property(property="Clt_Info1", type="string", nullable=true, example="Information complémentaire", description="Information complémentaire 1"),
 *     @OA\Property(property="CLI_DDm", type="string", format="date-time", example="2024-01-01T10:00:00Z", description="Date de dernière modification")
 * )
 *
 * @OA\Schema(
 *     schema="Ticket",
 *     type="object",
 *     description="Ticket de vente ProCaisse",
 *     @OA\Property(property="TIK_IdCarnet", type="string", example="CARNET001", description="Identifiant du carnet"),
 *     @OA\Property(property="TIK_NumTicket", type="integer", example=1001, description="Numéro du ticket"),
 *     @OA\Property(property="TIK_NumTicket_M", type="string", example="TIK001", description="Numéro ticket mobile"),
 *     @OA\Property(property="TIK_CodClt", type="string", example="CLI001", description="Code client"),
 *     @OA\Property(property="TIK_DateHeureTicket", type="string", format="date-time", example="2024-01-01T14:30:00Z", description="Date et heure du ticket"),
 *     @OA\Property(property="TIK_MtHT", type="number", format="float", example=100.00, description="Montant HT"),
 *     @OA\Property(property="TIK_MtTTC", type="number", format="float", example=120.00, description="Montant TTC"),
 *     @OA\Property(property="TIK_MtTVA", type="number", format="float", example=20.00, description="Montant TVA"),
 *     @OA\Property(property="TIK_station", type="string", example="ST001", description="Code station"),
 *     @OA\Property(property="TIK_Exerc", type="string", example="2024", description="Exercice"),
 *     @OA\Property(property="TIK_Source", type="string", example="MOBILE", description="Source du ticket"),
 *     @OA\Property(property="TIK_Etat", type="string", example="VALIDE", description="État du ticket")
 * )
 *
 * @OA\Schema(
 *     schema="TicketInput",
 *     type="object",
 *     description="Données d'entrée pour un ticket",
 *     @OA\Property(property="TIK_IdCarnet", type="string", example="CARNET001", description="Identifiant du carnet"),
 *     @OA\Property(property="TIK_NumTicket_M", type="string", example="TIK001", description="Numéro ticket mobile"),
 *     @OA\Property(property="TIK_CodClt", type="string", example="CLI001", description="Code client"),
 *     @OA\Property(property="TIK_DateHeureTicket", type="string", format="date-time", example="2024-01-01T14:30:00Z", description="Date et heure du ticket"),
 *     @OA\Property(property="TIK_MtHT", type="number", format="float", example=100.00, description="Montant HT"),
 *     @OA\Property(property="TIK_MtTTC", type="number", format="float", example=120.00, description="Montant TTC"),
 *     @OA\Property(property="TIK_MtTVA", type="number", format="float", example=20.00, description="Montant TVA"),
 *     @OA\Property(property="TIK_station", type="string", example="ST001", description="Code station"),
 *     @OA\Property(property="TIK_Exerc", type="string", example="2024", description="Exercice"),
 *     @OA\Property(property="TIK_Source", type="string", example="MOBILE", description="Source du ticket"),
 *     @OA\Property(property="TIK_Etat", type="string", example="VALIDE", description="État du ticket")
 * )
 *
 * @OA\Schema(
 *     schema="LigneTicketInput",
 *     type="object",
 *     description="Ligne de ticket de vente",
 *     @OA\Property(property="LT_CodArt", type="string", example="ART001", description="Code article"),
 *     @OA\Property(property="LT_Qte", type="number", format="float", example=2.0, description="Quantité"),
 *     @OA\Property(property="LT_PrixVente", type="number", format="float", example=25.50, description="Prix de vente unitaire"),
 *     @OA\Property(property="LT_PrixEncaisse", type="number", format="float", example=25.50, description="Prix encaissé"),
 *     @OA\Property(property="LT_MtHT", type="number", format="float", example=51.00, description="Montant HT"),
 *     @OA\Property(property="LT_MtTTC", type="number", format="float", example=61.20, description="Montant TTC"),
 *     @OA\Property(property="LT_PACHAT", type="number", format="float", example=20.00, description="Prix d'achat"),
 *     @OA\Property(property="LT_Remise", type="number", format="float", example=0.0, description="Remise appliquée"),
 *     @OA\Property(property="LT_NumLigne", type="integer", example=1, description="Numéro de ligne")
 * )
 *
 * @OA\Schema(
 *     schema="Unite",
 *     type="object",
 *     description="Unité de mesure",
 *     @OA\Property(property="id", type="string", example="123e4567-e89b-12d3-a456-426614174000", description="Identifiant unique"),
 *     @OA\Property(property="code", type="string", example="KG", description="Code de l'unité"),
 *     @OA\Property(property="libelle", type="string", example="Kilogramme", description="Libellé de l'unité"),
 *     @OA\Property(property="active", type="boolean", example=true, description="Statut actif"),
 *     @OA\Property(property="isSync", type="boolean", example=false, description="Statut de synchronisation"),
 *     @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-01T10:00:00Z"),
 *     @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-01T10:00:00Z")
 * )
 *
 * @OA\Schema(
 *     schema="Couleur",
 *     type="object",
 *     description="Couleur d'article",
 *     @OA\Property(property="id", type="string", example="123e4567-e89b-12d3-a456-426614174000", description="Identifiant unique"),
 *     @OA\Property(property="code", type="string", example="ROUGE", description="Code de la couleur"),
 *     @OA\Property(property="libelle", type="string", example="Rouge", description="Libellé de la couleur"),
 *     @OA\Property(property="active", type="boolean", example=true, description="Statut actif"),
 *     @OA\Property(property="isSync", type="boolean", example=false, description="Statut de synchronisation"),
 *     @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-01T10:00:00Z"),
 *     @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-01T10:00:00Z")
 * )
 *
 * @OA\Schema(
 *     schema="Modele",
 *     type="object",
 *     description="Modèle d'article",
 *     @OA\Property(property="id", type="string", example="123e4567-e89b-12d3-a456-426614174000", description="Identifiant unique"),
 *     @OA\Property(property="code", type="string", example="MOD001", description="Code du modèle"),
 *     @OA\Property(property="libelle", type="string", example="Modèle Standard", description="Libellé du modèle"),
 *     @OA\Property(property="active", type="boolean", example=true, description="Statut actif"),
 *     @OA\Property(property="isSync", type="boolean", example=false, description="Statut de synchronisation"),
 *     @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-01T10:00:00Z"),
 *     @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-01T10:00:00Z")
 * )
 *
 * @OA\Schema(
 *     schema="Cellule",
 *     type="object",
 *     description="Cellule de stockage",
 *     @OA\Property(property="id", type="string", example="123e4567-e89b-12d3-a456-426614174000", description="Identifiant unique"),
 *     @OA\Property(property="code", type="string", example="CELL001", description="Code de la cellule"),
 *     @OA\Property(property="libelle", type="string", example="Cellule A1", description="Libellé de la cellule"),
 *     @OA\Property(property="active", type="boolean", example=true, description="Statut actif"),
 *     @OA\Property(property="isSync", type="boolean", example=false, description="Statut de synchronisation"),
 *     @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-01T10:00:00Z"),
 *     @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-01T10:00:00Z")
 * )
 *
 * @OA\Schema(
 *     schema="TypeArticle",
 *     type="object",
 *     description="Type d'article",
 *     @OA\Property(property="id", type="string", example="123e4567-e89b-12d3-a456-426614174000", description="Identifiant unique"),
 *     @OA\Property(property="code", type="string", example="PROD", description="Code du type"),
 *     @OA\Property(property="libelle", type="string", example="Produit", description="Libellé du type"),
 *     @OA\Property(property="active", type="boolean", example=true, description="Statut actif"),
 *     @OA\Property(property="isSync", type="boolean", example=false, description="Statut de synchronisation"),
 *     @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-01T10:00:00Z"),
 *     @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-01T10:00:00Z")
 * )
 *
 * @OA\Schema(
 *     schema="Station",
 *     type="object",
 *     description="Station/Point de vente",
 *     @OA\Property(property="id", type="string", example="123e4567-e89b-12d3-a456-426614174000", description="Identifiant unique"),
 *     @OA\Property(property="code", type="string", example="ST001", description="Code de la station"),
 *     @OA\Property(property="libelle", type="string", example="Station Principale", description="Libellé de la station"),
 *     @OA\Property(property="active", type="boolean", example=true, description="Statut actif"),
 *     @OA\Property(property="alertQteTransfere", type="number", format="float", example=10.0, description="Seuil d'alerte pour transfert"),
 *     @OA\Property(property="adresse", type="string", nullable=true, example="123 Rue de la Station", description="Adresse de la station"),
 *     @OA\Property(property="telephone", type="string", nullable=true, example="+33123456789", description="Téléphone"),
 *     @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-01T10:00:00Z"),
 *     @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-01T10:00:00Z")
 * )
 *
 * @OA\Schema(
 *     schema="StationActive",
 *     type="object",
 *     description="Station active (version simplifiée)",
 *     @OA\Property(property="id", type="string", example="123e4567-e89b-12d3-a456-426614174000", description="Identifiant unique"),
 *     @OA\Property(property="libelle", type="string", example="Station Principale", description="Libellé de la station"),
 *     @OA\Property(property="alertQteTransfere", type="number", format="float", example=10.0, description="Seuil d'alerte pour transfert")
 * )
 *
 * @OA\Schema(
 *     schema="Fournisseur",
 *     type="object",
 *     description="Fournisseur (tiers)",
 *     @OA\Property(property="id", type="string", example="123e4567-e89b-12d3-a456-426614174000", description="Identifiant unique"),
 *     @OA\Property(property="code", type="string", example="FOUR001", description="Code du fournisseur"),
 *     @OA\Property(property="libelle", type="string", example="Fournisseur ABC", description="Libellé du fournisseur"),
 *     @OA\Property(property="adresse", type="string", nullable=true, example="123 Rue du Commerce", description="Adresse"),
 *     @OA\Property(property="telephone", type="string", nullable=true, example="+33123456789", description="Téléphone"),
 *     @OA\Property(property="email", type="string", nullable=true, example="<EMAIL>", description="Email"),
 *     @OA\Property(property="idTypeTier", type="string", example="TYPE001", description="Type de tiers"),
 *     @OA\Property(property="active", type="boolean", example=true, description="Statut actif"),
 *     @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-01T10:00:00Z"),
 *     @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-01T10:00:00Z")
 * )
 *
 * @OA\Schema(
 *     schema="Inventaire",
 *     type="object",
 *     description="Inventaire",
 *     @OA\Property(property="id", type="string", example="123e4567-e89b-12d3-a456-426614174000", description="Identifiant unique"),
 *     @OA\Property(property="code", type="string", example="INV001", description="Code de l'inventaire"),
 *     @OA\Property(property="libelle", type="string", example="Inventaire Janvier 2024", description="Libellé de l'inventaire"),
 *     @OA\Property(property="dateInventaire", type="string", format="date", example="2024-01-15", description="Date de l'inventaire"),
 *     @OA\Property(property="P_codeExercice", type="string", example="2024", description="Code exercice"),
 *     @OA\Property(property="etat", type="string", example="EN_COURS", description="État de l'inventaire"),
 *     @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-01T10:00:00Z"),
 *     @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-01T10:00:00Z")
 * )
 *
 * @OA\Schema(
 *     schema="EtatInventaire",
 *     type="object",
 *     description="État d'inventaire",
 *     @OA\Property(property="id", type="string", example="123e4567-e89b-12d3-a456-426614174000", description="Identifiant unique"),
 *     @OA\Property(property="code", type="string", example="EN_COURS", description="Code de l'état"),
 *     @OA\Property(property="libelle", type="string", example="En cours", description="Libellé de l'état"),
 *     @OA\Property(property="active", type="boolean", example=true, description="Statut actif")
 * )
 *
 * @OA\Schema(
 *     schema="Utilisateur",
 *     type="object",
 *     description="Utilisateur du système",
 *     @OA\Property(property="id", type="string", example="123e4567-e89b-12d3-a456-426614174000", description="Identifiant unique"),
 *     @OA\Property(property="login", type="string", example="admin", description="Login utilisateur"),
 *     @OA\Property(property="nom", type="string", example="Administrateur", description="Nom de l'utilisateur"),
 *     @OA\Property(property="prenom", type="string", nullable=true, example="Système", description="Prénom de l'utilisateur"),
 *     @OA\Property(property="email", type="string", nullable=true, example="<EMAIL>", description="Email"),
 *     @OA\Property(property="active", type="boolean", example=true, description="Statut actif"),
 *     @OA\Property(property="j_ddm", type="string", format="date-time", example="2024-01-01T10:00:00Z", description="Date de dernière modification")
 * )
 *
 * @OA\Schema(
 *     schema="SessionCaisse",
 *     type="object",
 *     description="Session de caisse",
 *     @OA\Property(property="SC_IdSCaisse", type="string", example="SC001", description="Identifiant de la session"),
 *     @OA\Property(property="SC_CodeCaisse", type="string", example="CAISSE001", description="Code de la caisse"),
 *     @OA\Property(property="SC_CodeUtilisateur", type="string", example="USER001", description="Code utilisateur"),
 *     @OA\Property(property="SC_CodeCarnet", type="string", example="CARNET001", description="Code carnet"),
 *     @OA\Property(property="SC_Station", type="string", example="ST001", description="Code station"),
 *     @OA\Property(property="SC_FondCaisse", type="number", format="float", example=1000.00, description="Fond de caisse"),
 *     @OA\Property(property="SC_DateHeureCrea", type="string", format="date-time", example="2024-01-01T08:00:00Z", description="Date/heure de création"),
 *     @OA\Property(property="SC_DateHeureOuv", type="string", format="date-time", example="2024-01-01T08:00:00Z", description="Date/heure d'ouverture"),
 *     @OA\Property(property="SC_DateHeureClot", type="string", format="date-time", nullable=true, example="2024-01-01T18:00:00Z", description="Date/heure de clôture"),
 *     @OA\Property(property="SC_ClotCaisse", type="integer", example=0, description="Statut de clôture (0=ouverte, 1=fermée)"),
 *     @OA\Property(property="SC_NomMachine", type="string", example="MACHINE001", description="Nom de la machine"),
 *     @OA\Property(property="SC_TotalVente", type="number", format="float", example=2500.00, description="Total des ventes"),
 *     @OA\Property(property="SC_TotalEncaisse", type="number", format="float", example=2500.00, description="Total encaissé")
 * )
 *
 * @OA\Schema(
 *     schema="Caisse",
 *     type="object",
 *     description="Caisse ProCaisse",
 *     @OA\Property(property="CAI_Code", type="string", example="CAISSE001", description="Code de la caisse"),
 *     @OA\Property(property="CAI_Designation", type="string", example="Caisse Principale", description="Désignation de la caisse"),
 *     @OA\Property(property="CAI_Station", type="string", example="ST001", description="Code station"),
 *     @OA\Property(property="CAI_User", type="string", example="USER001", description="Code utilisateur"),
 *     @OA\Property(property="CAI_DDm", type="string", format="date-time", example="2024-01-01T10:00:00Z", description="Date de dernière modification"),
 *     @OA\Property(property="CAI_export", type="string", nullable=true, example="Y", description="Statut d'export"),
 *     @OA\Property(property="CAI_Etat", type="string", example="Actif", description="État de la caisse")
 * )
 *
 * @OA\Schema(
 *     schema="FamilleMobile",
 *     type="object",
 *     description="Famille d'articles mobile",
 *     @OA\Property(property="FAM_Code", type="string", example="FAM001", description="Code de la famille"),
 *     @OA\Property(property="FAM_Lib", type="string", example="Électronique", description="Libellé de la famille"),
 *     @OA\Property(property="FAM_User", type="string", nullable=true, example="USER001", description="Code utilisateur"),
 *     @OA\Property(property="FAM_Station", type="string", nullable=true, example="ST001", description="Code station"),
 *     @OA\Property(property="FAM_IsTaktile", type="string", nullable=true, example="Y", description="Famille tactile"),
 *     @OA\Property(property="FAM_Couleur", type="string", nullable=true, example="#FF0000", description="Couleur de la famille"),
 *     @OA\Property(property="FAM_DesgCourte", type="string", nullable=true, example="Elec", description="Désignation courte"),
 *     @OA\Property(property="FAM_NumOrdre", type="integer", nullable=true, example=1, description="Numéro d'ordre"),
 *     @OA\Property(property="FAM_export", type="string", nullable=true, example="Y", description="Statut d'export"),
 *     @OA\Property(property="FAM_DDm", type="string", format="date-time", example="2024-01-01T10:00:00Z", description="Date de dernière modification"),
 *     @OA\Property(property="photo_Path", type="string", nullable=true, example="/images/famille_001.jpg", description="Chemin de la photo"),
 *     @OA\Property(property="FAM_CodeM", type="string", nullable=true, example="MOBILE001", description="Code mobile")
 * )
 *
 * @OA\Schema(
 *     schema="ArticleCodeBar",
 *     type="object",
 *     description="Code à barres d'article",
 *     @OA\Property(property="Parent_CodeBar", type="string", example="PARENT001", description="Code à barres parent"),
 *     @OA\Property(property="Fils_CodeBar", type="string", example="FILS001", description="Code à barres fils"),
 *     @OA\Property(property="cod_b_user", type="string", nullable=true, example="USER001", description="Code utilisateur"),
 *     @OA\Property(property="cod_b_station", type="string", nullable=true, example="ST001", description="Code station"),
 *     @OA\Property(property="cod_b_export", type="string", nullable=true, example="Y", description="Statut d'export"),
 *     @OA\Property(property="cod_b_DDm", type="string", format="date-time", nullable=true, example="2024-01-01T10:00:00Z", description="Date de dernière modification")
 * )
 *
 * @OA\Schema(
 *     schema="ArticleCodeBarSimple",
 *     type="object",
 *     description="Code à barres d'article (version simplifiée)",
 *     @OA\Property(property="Parent_CodeBar", type="string", example="PARENT001", description="Code à barres parent"),
 *     @OA\Property(property="Fils_CodeBar", type="string", example="FILS001", description="Code à barres fils"),
 *     @OA\Property(property="cod_b_user", type="string", nullable=true, example="USER001", description="Code utilisateur"),
 *     @OA\Property(property="cod_b_station", type="string", nullable=true, example="ST001", description="Code station")
 * )
 *
 * @OA\Schema(
 *     schema="ArticleCodeBarInput",
 *     type="object",
 *     description="Données d'entrée pour un code à barres d'article",
 *     @OA\Property(property="Parent_CodeBar", type="string", example="PARENT001", description="Code à barres parent"),
 *     @OA\Property(property="Fils_CodeBar", type="string", example="FILS001", description="Code à barres fils"),
 *     @OA\Property(property="cod_b_user", type="string", nullable=true, example="USER001", description="Code utilisateur"),
 *     @OA\Property(property="cod_b_station", type="string", nullable=true, example="ST001", description="Code station"),
 *     @OA\Property(property="cod_b_export", type="string", nullable=true, example="Y", description="Statut d'export (peut être 'NULL')"),
 *     @OA\Property(property="cod_b_DDm", type="string", format="date-time", nullable=true, example="2024-01-01T10:00:00Z", description="Date de dernière modification")
 * )
 *
 * @OA\Schema(
 *     schema="ArticleInput",
 *     type="object",
 *     description="Données d'entrée pour un article",
 *     @OA\Property(property="ART_Code", type="string", example="ART001", description="Code de l'article"),
 *     @OA\Property(property="ART_Designation", type="string", example="Article de test", description="Désignation de l'article"),
 *     @OA\Property(property="ART_PrixUnitaireHT", type="number", format="float", example=25.50, description="Prix unitaire HT"),
 *     @OA\Property(property="ART_TVA", type="number", format="float", example=20.00, description="Taux de TVA"),
 *     @OA\Property(property="ART_Famille", type="string", nullable=true, example="FAM001", description="Code famille"),
 *     @OA\Property(property="ART_Marque", type="string", nullable=true, example="MAR001", description="Code marque"),
 *     @OA\Property(property="ART_CodeBar", type="string", nullable=true, example="1234567890123", description="Code à barres"),
 *     @OA\Property(property="ART_QteStock", type="number", format="float", example=100.0, description="Quantité en stock"),
 *     @OA\Property(property="photo_Path", type="string", nullable=true, example="/images/article_001.jpg", description="Chemin de la photo")
 * )
 *
 * @OA\Schema(
 *     schema="Banque",
 *     type="object",
 *     description="Banque ProCaisse",
 *     @OA\Property(property="BAN_Code", type="string", example="BAN001", description="Code de la banque"),
 *     @OA\Property(property="BAN_Designation", type="string", example="Banque Populaire", description="Désignation de la banque"),
 *     @OA\Property(property="BAN_Adresse", type="string", nullable=true, example="123 Rue de la Banque", description="Adresse de la banque"),
 *     @OA\Property(property="BAN_Telephone", type="string", nullable=true, example="+33123456789", description="Téléphone"),
 *     @OA\Property(property="BAN_Email", type="string", nullable=true, example="<EMAIL>", description="Email"),
 *     @OA\Property(property="BAN_RIB", type="string", nullable=true, example="12345678901234567890", description="RIB"),
 *     @OA\Property(property="BAN_Station", type="string", nullable=true, example="ST001", description="Code station"),
 *     @OA\Property(property="BAN_User", type="string", nullable=true, example="USER001", description="Code utilisateur")
 * )
 *
 * @OA\Schema(
 *     schema="LicenseUrl",
 *     type="object",
 *     description="URL de service de licence",
 *     @OA\Property(property="id", type="integer", example=1, description="Identifiant"),
 *     @OA\Property(property="name", type="string", example="Service Principal", description="Nom du service"),
 *     @OA\Property(property="url", type="string", example="https://license.procaisse.com", description="URL du service"),
 *     @OA\Property(property="active", type="boolean", example=true, description="Statut actif"),
 *     @OA\Property(property="description", type="string", nullable=true, example="Service principal de licences", description="Description")
 * )
 *
 * @OA\Schema(
 *     schema="BonCommande",
 *     type="object",
 *     description="Bon de commande (Devis)",
 *     @OA\Property(property="DEV_Code", type="string", example="DEV001", description="Code du devis"),
 *     @OA\Property(property="DEV_CodeClient", type="string", example="CLI001", description="Code client"),
 *     @OA\Property(property="DEV_Date", type="string", format="date-time", example="2024-01-01T10:00:00Z", description="Date du devis"),
 *     @OA\Property(property="DEV_MontantHT", type="number", format="float", example=1000.00, description="Montant HT"),
 *     @OA\Property(property="DEV_MontantTTC", type="number", format="float", example=1200.00, description="Montant TTC"),
 *     @OA\Property(property="DEV_MontantTVA", type="number", format="float", example=200.00, description="Montant TVA"),
 *     @OA\Property(property="DEV_Etat", type="string", example="EN_COURS", description="État du devis"),
 *     @OA\Property(property="DEV_Station", type="string", example="ST001", description="Code station"),
 *     @OA\Property(property="DEV_User", type="string", example="USER001", description="Code utilisateur")
 * )
 */
class SwaggerSchemas extends Controller
{
    // Ce contrôleur sert uniquement pour les schémas Swagger réutilisables
}
