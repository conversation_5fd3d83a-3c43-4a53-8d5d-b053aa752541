<?php

namespace App\Http\Controllers;

use App\Helpers\DatabaseConnection;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class PhotoController extends Controller
{

    public function store(request $request)
    {
        // $test=false;
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $filename = $image->getClientOriginalName();
            // $rest = substr($filename, 0, 11);
            $path = public_path() . '/data/mershandiser/file/';
            $image->move($path, $filename);
            //$test=true;
        }
        //return response()->json(true);
    }

    public function addStationPhoto(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        if (!empty($data)) {
            foreach ($data["object"] as $item) {
                $f = $connection->table('visite_photo')->insert($item);
            }

            return response()->json($f);

        } else {
            return (false);
        }

    }

    public function getVisitePhoto(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('visite_photo')->get());

    }
}
