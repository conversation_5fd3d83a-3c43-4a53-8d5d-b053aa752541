<?php

namespace App\Http\Controllers;

use App\Helpers\AppHelper;
use App\Helpers\DatabaseConnection;
use Carbon\Carbon;
use DateTime;
use Illuminate\Http\Request;

class InventaireWSMobile extends Controller
{

    public function getInventaires(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);

        $exercice = (new ExerciceWSMobile())->getExercice($request)->original;


        $inventaires = $connection->table('Inventaire')
            ->whereYear('INV_Date', '=', $exercice->Exercice_Code)->get();
      /*  foreach ($inventaires as $inventaire)
        {
            $inventaire->countLigne = $connection->table('ligne_inventaire')->where('LG_INV_Code_Inv','=',$inventaire->INV_Code)->count();
        }*/
        return response()->json($inventaires);
    }

    public function addBatchInventaires(Request $request)
    {
        $items = $request->json()->all();

        if (!empty($items)) {
            $connection = DatabaseConnection::setConnection($items);
            $result = null;
            foreach ($items["object"] as $data) {
                $data['INV_Date'] = AppHelper::setDateFormat($data['INV_Date']);
                $c = $connection->table('Inventaire')->insert($data);
                $result[] = $c ? $data : null;
            }

        } else {
            return response()->json(null);
        }

        return response()->json($result);

    }

    public function addBatchInventairesWithLines(Request $request)
    {
        $items = $request->json()->all();
        $result = [];
        if (!empty($items)) {
            $connection = DatabaseConnection::setConnection($items);
            $invertorLineController = new LigneInventaireWSMobile();
            foreach ($items["object"] as $item) {
                //   $exist = $connection->table('Inventaire')->where("INV_Code", $item['parent']["INV_Code"])->get();
                $exist = $connection->table('Inventaire')
                    ->where("INV_Code_Station", $item['parent']["INV_Code_Station"])
                    ->where("INV_Date", $item['parent']["INV_Date"])
                    ->where("INV_User", $item['parent']["INV_User"])
                    ->get();
                if ($exist == null || empty($exist) || count($exist) <= 0) {

                    $item['parent']["INV_Code"] = (new PrefixWSMobile())->getInventairePrefix($connection,Carbon::now()->year,
                        $item['parent']["INV_Code_Station"],$item['parent']["INV_User"]);
                } else {
                     $item['parent']["INV_Code"] =  $exist[0]->INV_Code;
                }
                $item['parent']["INV_Date"] = AppHelper::setDateFormat($item['parent']["INV_Date"]);
                if (($exist == null || empty($exist) || count($exist) <= 0)) {
                    $item['parent']["INV_Etat"] = "En cours";
                }
                //>   dd($exist[0]->INV_Code);
                $inventorInsertResult = ($exist == null || empty($exist) || count($exist) <= 0) ?
                    $connection->table('Inventaire')->insert($item['parent']) :
                    $connection->table('Inventaire')
                        ->where("INV_Code_Station", $item['parent']["INV_Code_Station"])
                        ->where("INV_Date", $item['parent']["INV_Date"])
                        ->where("INV_User", $item['parent']["INV_User"])
                        ->update($item['parent']);

                for ($i = 0; $i < count($item["children"]); $i++) {
                    $item['children'][$i]["LG_INV_Code_Inv"] = ($exist == null || empty($exist) || count($exist) <= 0) ? $item['parent']["INV_Code"] : $exist[0]->INV_Code;
                }

                $invertorLineController->addBatchLigneInventairesData($connection, $item["children"]);
                //  $item['parent']["INV_Date"] = json_decode(json_encode(date_create_from_format(DateHelper::$format,$item['parent']["INV_Date"])))->date;

                if ($inventorInsertResult) {
                    array_push($result, $item);
                }
            }

        } else {
            return response()->json(null);
        }

        return response()->json($result);

    }


}
