<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests;
use Illuminate\Http\Response;
use App\Helpers\DatabaseConnection;

use App\Http\Controllers\PrefixWSMobile;

/**
 * @OA\Tag(
 *     name="Chèques Caisse Mobile",
 *     description="Gestion des chèques de caisse et devises depuis l'application mobile ProCaisse"
 * )
 */
class ChequeCaisseWSMobile extends Controller
{

    /**
     * @OA\Post(
     *     path="/ChequeCaisse/getChequeCaisse",
     *     tags={"Chèques Caisse Mobile"},
     *     summary="Récupérer tous les chèques de caisse",
     *     description="Retourne la liste complète des chèques de caisse",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Paramètres de connexion",
     *         @OA\JsonContent(ref="#/components/schemas/StandardRequest")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Liste des chèques de caisse récupérée avec succès",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/ChequeCaisse")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function getChequeCaisse(Request $request)
    {


        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('ChequeCaisse')->get());
    }


    public function getChequeCaisseByReglement(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('ChequeCaisse')
            ->where('Reglement', $data["REGC_Code"])
            ->where('Reglement_idsession', $data["REGC_IdSCaisse"])
            ->where('Reglement_exercice', $data["REGC_Exercice"])
            ->get());

    }


    public function getChequeCaisseByReglements(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $items = null;

        foreach ($data["object"] as &$item) {
            $items[] = $connection->table('ChequeCaisse')
                ->where('Reglement', $item["REGC_Code"])
                ->where('Reglement_idsession', $item["REGC_IdSCaisse"])
                ->where('Reglement_exercice', $item["REGC_Exercice"])
                ->get();


        }


        return response()->json($items);


    }

    public function getDeviseByTicket(Request $request)
    {
        return response()->json(DB::table('Devise')->where($request->field, $request->value)->get());

    }

    public function getDeviseByActive(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('Devise')->where('Principale', 1)->where('Activite', 1)->get()->first());

    }


}





