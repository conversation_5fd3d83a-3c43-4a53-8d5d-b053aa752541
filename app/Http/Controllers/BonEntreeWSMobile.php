<?php

namespace App\Http\Controllers;

use App\Helpers\AppHelper;
use App\Helpers\DatabaseConnection;
use App\Helpers\Enum;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class BonEntreeWSMobile extends Controller
{
    public function getBonEntrees(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $filterMois = $request->input("mois");
        $dateDebutMoisPrecedent = date('Y-m-01', strtotime('-' . $filterMois . 'months'));
        $bonsEntrees = $connection->table('bon_entree');

        if ($filterMois != "") {
            $bonsEntrees = $bonsEntrees->where('BON_ENT_Date', '<=', date('Y-m-d'))
                ->where('BON_ENT_Date', '>=', $dateDebutMoisPrecedent);
        }else{
            $moisCourant = date('Y-m');
            $bonsEntrees = $bonsEntrees->whereRaw("FORMAT(BON_ENT_Date, 'yyyy-MM') = '$moisCourant'");

        }
        $bonsEntrees = $bonsEntrees->get();


        return response()->json($bonsEntrees);
    }

    /**
     * @throws Exception
     */
    public function addBatchBonEntrees(Request $request)
    {
        $items = $request->json()->all();
        if (!empty ($items)) {
            $connection = DatabaseConnection::setConnection($items);
            $result = null;
            foreach ($items["object"] as $data) {
                $data ["BON_ENT_Date"] = AppHelper::setDateFormat($data['BON_ENT_Date']);
                $exist = $connection->table('bon_entree_mobile')->where("BON_ENT_Num", $data["BON_ENT_Num"])->first();
                $c = $exist == null ? $connection->table('bon_entree_mobile')->insert($data) : $connection->
                table('bon_entree_mobile')->where("BON_ENT_Num", $data["BON_ENT_Num"])->update($data);
                $result = $c ? $data : null;
            }
        } else {
            return response()->json(null);
        }

        return response()->json($result);
    }

    public function addBatchBonEntreesWithLines(Request $request)
    {
        $items = $request->json()->all();
        if (!empty ($items)) {
            $connection = DatabaseConnection::setConnection($items);
            $result = array();
            $connection->beginTransaction();

            try {
                foreach ($items["object"] as $data) {
                    $parent = $data['parent'];
                    unset($parent['BON_ENT_SYNC']);

                    $lines = $data["children"];
                    $exist = $connection->table('bon_entree')
                        ->where("BON_ENT_Num", $parent["BON_ENT_Num"])
                        ->where("BON_ENT_Num_M", $parent["BON_ENT_Num_M"])
                        ->where("BON_ENT_Exer", $parent["BON_ENT_Exer"])->first();
                    $parent["BON_ENT_Date"] = AppHelper::setDateFormat($parent['BON_ENT_Date']);

                    //Not Exist
                    if (!$exist) {
                        $errors = $this->calculateBE($lines, $parent, $connection);
                        if (sizeof($errors['data']) > 0) {
                            $result[] = $this->setBonEntree($parent["BON_ENT_Num"],
                                $parent["BON_ENT_Num_M"],
                                $parent["BON_ENT_Exer"],
                                [$errors['data']],
                                "Error Insert BE", Enum::Error_Insert_BE);
                            Log::error("Error Insert BE", [$errors['data']]);
                            throw new Exception("Error Insert BE", Enum::Error_Insert_BE);
                        } else {
                            $parent["BON_ENT_Num"] = (new PrefixWSMobile())->getBonEntreePrefix($items,
                                $parent["BON_ENT_Exer"],
                                $parent["BON_ENT_StationEntree"],
                                $parent["BON_ENT_User"]
                            );

                            $insert = $connection->table('bon_entree')->insert($parent);
                            //Error Insert Parent
                            if (!$insert) {
                                $result[] = $this->setBonEntree($parent["BON_ENT_Num"],
                                    $parent["BON_ENT_Num_M"],
                                    $parent["BON_ENT_Exer"], [],
                                    "Error Insert BE", Enum::Error_Insert_BE);
                                Log::error("Error Insert BE", $result);
                                throw new Exception("Error Insert BE", Enum::Error_Insert_BE);
                            } else {
                                for ($i = 0; $i < count($lines); $i++) {
                                    unset($lines[$i]['ordre']);
                                    $lines[$i]['LIG_BonEntree_PUTTC'] = floatval($lines[$i]['LIG_BonEntree_PUTTC']);
                                    $lines[$i]['LIG_BonEntree_MntTTC'] = floatval($lines[$i]['LIG_BonEntree_MntTTC']);
                                    $lines[$i]["LIG_BonEntree_NumBon"] = $parent["BON_ENT_Num"];
                                }
                                $insertLines = $connection->table('ligne_bon_entree')->insert($lines);
                                //Error Insert Lines
                                if (!$insertLines) {
                                    $result[] = $this->setBonEntree($parent["BON_ENT_Num"],
                                        $parent["BON_ENT_Num_M"],
                                        $parent["BON_ENT_Exer"], [],
                                        "Error Insert Lines BE", Enum::Error_Insert_LigneBE);
                                    Log::error("Error Insert Lines BE", $result);
                                    throw new Exception("Error Insert Lines BE", Enum::Error_Insert_LigneBE);
                                } else {

                                    $result[] = $this->setBonEntree($parent["BON_ENT_Num"],
                                        $parent["BON_ENT_Num_M"],
                                        $parent["BON_ENT_Exer"], [],
                                        "INSERTED");
                                }
                            }

                        }
                    } else {
                        $result[] = $this->setBonEntree($parent["BON_ENT_Num"],
                            $parent["BON_ENT_Num_M"],
                            Carbon::now()->year, [],
                            "Bon entree Already Exist", Enum::Error_BE_Existe);
                    }

                }
                $connection->commit();
            } catch (Exception $exception) {
                $connection->rollBack();
            }


        } else {
            return response()->json($this->setBonEntree(null,
                null,
                Carbon::now()->year, [],
                "empty Object", 404));
        }
        return response()->json($result);
    }

    public function calculateBE($lines, $parent, $connexion)
    {
        $MnHTToTal = 0;
        $MnNetHtTotal = 0;
        $MnTVATotal = 0;
        $TotatlTTC = 0;
        $errors = [
            "data" => []
        ];
        $timbre = 0;

        $supplier = $connexion->table('fournisseur')
            ->where('FRS_codef', '=', $parent['BON_ENT_CodeFrs'])
            ->first(['FRS_fodec', 'FRS_DC', 'FRS_timber']);

        if ($supplier->FRS_timber) {
            $timbre = $connexion->table('Timbre')
                ->where('TIMB_Etat', '=', 'Actif')->pluck('TIMB_Value')->first();
        }
        for ($i = 0; $i < count($lines); $i++) {

            //lines
            $tva = ((float)$lines[$i]['LIG_BonEntree_Tva'] / 100);
            $MnHT = $lines[$i]['LIG_BonEntree_Qte'] * $lines[$i]['LIG_BonEntree_PUHT'];
            $MnNetHt = $MnHT - ($MnHT * $lines[$i]['LIG_BonEntree_Remise'] / 100);
            $MntTTC = $MnNetHt * (1 + $tva);

            $MnTVA = ($MnNetHt * $tva);
            $PUHT=$lines[$i]['LIG_BonEntree_PUHT'];
            $PUNetHT= $PUHT - ($PUHT * $lines[$i]['LIG_BonEntree_Remise'] / 100);
            $PUTTC=$PUNetHT * (1 + $tva);


            //Test Lines
            if (round($MnNetHt, 3) != round($lines[$i]['LIG_BonEntree_MntNetHt'], 3)) {
                $errors['data'][] = $this->controlCalculateBC($i + 1,
                    "Error In the Calcul of LIG_BonEntree_MntNetHt", round($PUTTC, 3));
            }

            if (round($PUTTC, 3) != round($lines[$i]['LIG_BonEntree_PUTTC'], 3)) {
                $errors['data'][] = $this->controlCalculateBC($i + 1,
                    "Error In the Calcul of LIG_BonEntree_PUTTC", round($PUTTC, 3));
            }
            if (round($MnTVA, 3) != round($lines[$i]['LIG_BonEntree_MntTva'], 3)) {
                $errors['data'][] = $this->controlCalculateBC($i + 1,
                    "Error In the Calcul of LIG_BonEntree_MntTva", round($MnTVA, 3));
            }
            if (round($MntTTC, 3) != round($lines[$i]['LIG_BonEntree_MntTTC'], 3)) {
                $errors['data'][] = $this->controlCalculateBC($i + 1,
                    "Error In the Calcul of LIG_BonEntree_MntTTC", round($MntTTC, 3));
            }


            //parent
            $MnHTToTal = $MnHTToTal + $MnHT;
            $MnNetHtTotal = $MnNetHtTotal + $MnNetHt;
            $MnTVATotal = $MnTVATotal + $MnTVA;
            $TotatlTTC = $TotatlTTC + ($MnNetHt + $MnTVA);


            if ($supplier->FRS_fodec == 1) {
                $TotatlTTC = $TotatlTTC + $lines[$i]['LIG_BonEntree_TauxFodec'];
            }
            if ($supplier->FRS_DC == 1) {
                $TotatlTTC = $TotatlTTC + $lines[$i]['LIG_BonEntree_TauxDc'];
            }


        }

        if ($parent['BON_ENT_TypePiece'] == "FACTURE" && $supplier->FRS_timber == 1) {
            $TotatlTTC = $TotatlTTC + (float)$timbre;
        }

        $MnRemise = $MnHTToTal - $MnNetHtTotal;

        //Test parent
        if (round($MnHTToTal, 3) != round($parent['BON_ENT_MntHT'], 3)) {
            $errors['data'] = $this->controlCalculateBC(0,
                "Error In the Calcul of BON_ENT_MnHT", round($MnHTToTal, 3));
        }

        if (round($MnNetHtTotal, 3) != round($parent['BON_ENT_MntNetHt'], 3)) {
            $errors['data'] = $this->controlCalculateBC(0,
                "Error In the Calcul of BON_ENT_MntNetHt", round($MnNetHtTotal, 3));
        }
        if (round($MnRemise, 3) != round($parent['BON_ENT_Remise'], 3)) {
            $errors['data'] = $this->controlCalculateBC(0,
                "Error In the Calcul of BON_ENT_MntNetHt", round($MnRemise, 3));
        }

        if (round($MnTVATotal, 3) != round($parent['BON_ENT_MntTva'], 3)) {
            $errors['data'] = $this->controlCalculateBC(0,
                "Error In the Calcul of BON_ENT_MntTva", round($MnTVATotal, 3));
        }

        if (round($TotatlTTC, 3) != round($parent['BON_ENT_MntTTC'], 3)) {
            $errors['data'] = $this->controlCalculateBC(0,
                "Error In the Calcul of BON_ENT_MntTTC", round($TotatlTTC, 3));
        }

        return $errors;
    }

}

