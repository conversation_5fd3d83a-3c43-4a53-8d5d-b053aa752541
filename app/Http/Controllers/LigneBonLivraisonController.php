<?php

namespace App\Http\Controllers;

use App\Helpers\AppHelper;
use App\Helpers\DatabaseConnection;
use App\Helpers\Enum;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Exception;
use phpDocumentor\Reflection\Types\Boolean;

class LigneBonLivraisonController extends Controller
{

    /*fonction get data from database*/

    public function getLigneBonLivraison(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        return response()->json($connection->table('ligne_bon_transfert')
            ->where('DDm','>=',Carbon::now()->subMonth()->toDateTimeString())->get());
    }

    public function deleteBatchLigneBonLivraison(Request $request)
    {

        $items = $request->json()->all();

        if (!empty($items)) {
            //    $data['object']['CLI_Code'] =(new PrefixWSMobile)->getPrefix($data,"Client");
            $connection = DatabaseConnection::setConnection($items);
            $result = null;
            foreach ($items["object"] as $data) {

//$code=
                $c = $connection->table('ligne_bon_transfert')
                    ->where('LIG_BonEntree_NumBon', $data["LIG_BonEntree_NumBon"])->where('LIG_BonEntree_CodeArt', $data["LIG_BonEntree_CodeArt"])->delete();

                if ($c) {
                    $result[] = $data;
                } else {
                    $result[] = null;
                }

            }

        } else {
            return response()->json(null);
        }

        return response()->json($result);
    }

    public function updateBatchLigneBonLivraison(Request $request)
    {

        $items = $request->json()->all();

        if (!empty($items)) {
            //    $data['object']['CLI_Code'] =(new PrefixWSMobile)->getPrefix($data,"Client");
            $connection = DatabaseConnection::setConnection($items);
            $result = null;
            foreach ($items["object"] as $data) {

//$code=
                $c = $connection->table('ligne_bon_transfert')->where('LIG_BonEntree_NumBon', $data["LIG_BonEntree_NumBon"])->where('LIG_BonEntree_CodeArt', $data["LIG_BonEntree_CodeArt"])->update($data);

                if ($c) {
                    $result[] = $data;
                } else {
                    $result[] = null;
                }

            }

        } else {
            return response()->json(null);
        }

        return response()->json($result);
    }

    public function addBatchLigneBonLivraisonData($connection, $items, $bonTransfert)
    {
        $testLigneBL=true;
        $message="";
        $codeEnum=null;
        if (!empty($items)) {
            $Mnt_TTC = 0;
            $Mnt_HT = 0;
            $nbLigneBL = count($items);
            $count = 0;

            foreach ($items as $data) {

                $article = $connection->table('article')->where("ART_Code", $data["LG_BonTrans_CodeArt"])->first();
                $data["LG_ETAT"] = "Validée";
                $data["DDm"] = Carbon::now();
                $data["LG_BonTrans_PACHATNHT"] = $article->ART_PrixUnitaireHT;
                $data["LG_BonTrans_TVA"] = $article->ART_TVA;

                $Unite_article = $connection->table('Unite_article')
                    //->where("UNITE_ARTICLE_CodeUnite", $data["LG_BonTrans_Unite"])
                    ->where("UNITE_ARTICLE_CodeArt", $data["LG_BonTrans_CodeArt"])
                    ->get();
                if (!!$Unite_article) {
                    //if this article has a lot of unity so we have to select the one who has UNITE_ARTICLE_IsUnitaire == 1
                    if (sizeof($Unite_article) > 1) {
                        foreach ($Unite_article as $unite) {
                            if ($unite->UNITE_ARTICLE_IsUnitaire == 1) {
                                $data["LG_BonTrans_Unite"] = $unite->UNITE_ARTICLE_CodeUnite;
                                $data["LIG_BonEntree_QtePiece"] = $unite->UNITE_ARTICLE_QtePiece;
                                $data["LG_BonTrans_PVTTC"] = $unite->UNITE_ARTICLE_PrixVenteTTC;
                                $data["LG_BonTrans_MNTTC"] = $data["QteDecTransferer"] * $data["LG_BonTrans_PVTTC"];
                            }
                        }

                    } else {
                        //select the defaut unity
                        foreach ($Unite_article as $unite) {
                            $data["LG_BonTrans_Unite"] = $unite->UNITE_ARTICLE_CodeUnite;
                            $data["LIG_BonEntree_QtePiece"] = $unite->UNITE_ARTICLE_QtePiece;
                            $data["LG_BonTrans_PVTTC"] = $unite->UNITE_ARTICLE_PrixVenteTTC;
                            $data["LG_BonTrans_MNTTC"] = $data["QteDecTransferer"] * $data["LG_BonTrans_PVTTC"];
                        }
                    }


                }

                $data["LG_BonTrans_MNTHT"] = $data["QteDecTransferer"] * $data["LG_BonTrans_PACHATNHT"];

                if (array_key_exists("LG_BonTrans_PUHT", $data)) {
                    unset($data["LG_BonTrans_PUHT"]);
                }

                if (array_key_exists("LG_BonTrans_SYNC", $data)) {
                    unset($data["LG_BonTrans_SYNC"]);
                }
                if (array_key_exists("LG_BonTrans_PUHT", $data)) {
                    unset($data["LG_BonTrans_PUHT"]);
                }
                if (array_key_exists("LG_BonTrans_PUHT", $data)) {
                    unset($data["LG_BonTrans_PUHT"]);
                }
                $data["LG_BonTrans_DDm"] = AppHelper::setDateFormat();

                $exist = $connection->table("ligne_bon_transfert")
                    ->where("LG_BonTrans_NumBon", $data["LG_BonTrans_NumBon"])
                    ->where("LG_BonTrans_CodeArt", $data["LG_BonTrans_CodeArt"])
                    ->where("LG_BonTrans_Exerc", $data["LG_BonTrans_Exerc"])->first();

                $success = (empty($exist)) ? $this->insertLine($connection, $data) :
                    $this->updateLine($connection, $data);

                if ($success) {
                    $count++;
                }


                $Mnt_HT += $data['LG_BonTrans_MNTHT'];

                $Mnt_TTC += $data['LG_BonTrans_MNTTC'];


            }

            if ($count < $nbLigneBL) {
                $testLigneBL=false;
                $message="Erreur dans l'insertion des lignes du Bon Transfert N° ";
                $codeEnum=Enum::Error_Insert_LG_BL;
                //   throw new Exception("Erreur dans l'insertion des lignes");
            }

            if ($Mnt_HT == 0) {
                $testLigneBL=false;
                $message="Mnt_HT est null";
                $codeEnum=Enum::Mnt_HT_BL_NULL;
                //throw new Exception('Mnt_HT est null');
            } else
                if ($Mnt_TTC == 0) {
                    $testLigneBL=false;
                    $message="Mnt_TTC est null";
                    $codeEnum=Enum::Mnt_TTC_BL_NULL;
                    //   throw new Exception('Mnt_TTC est null');
                }

            return [
                'testLigneBL'=>$testLigneBL,
                'message'=>$message,
                'codeEnum'=>$codeEnum,
                'Mnt_HT' => $Mnt_HT,
                'Mnt_TTC' => $Mnt_TTC,
            ];


        }
        return null;
    }

    public function addBatchLigneBonLivraisonMobile(Request $request): \Illuminate\Http\JsonResponse
    {
        $items = $request->json()->all();

        if (!empty($items)) {
            //    $data['object']['CLI_Code'] =(new PrefixWSMobile)->getPrefix($data,"Client");
            $connection = DatabaseConnection::setConnection($items);
            $result = null;
            foreach ($items["object"] as $data) {
                $c = $this->updateOrCreate($connection, $data, 'ligne_bon_transfert', [['LG_BonTrans_NumBon', '=', $data['LG_BonTrans_NumBon']], ['LG_BonTrans_Exerc', '=', $data['LG_BonTrans_Exerc']], ['LG_BonTrans_CodeArt', '=', $data['LG_BonTrans_CodeArt']]]);
                if ($c) {
                    $result[] = $data;
                } else {
                    $result[] = null;
                }
            }

        } else {
            return response()->json(null);
        }

        return response()->json($result);
    }


    public function updateOrCreate($connection, $data, $table, $whereClauses)
    {
        $exists = $connection->table($table)->where($whereClauses)->first();

        if ($exists) {
            return $connection->table($table)->where($whereClauses)->udpdte($data);
        }
    }

    /**
     * @param $connection
     * @param $data
     * @return mixed
     */
    public function updateLine($connection, $data)
    {
        if ($data["DDm"] == null || $data["DDm"] == "NULL") {
            unset($data["DDm"]);
        }
        if ($data["LG_BonTrans_DDm"] != null && $data["LG_BonTrans_DDm"] != "NULL") {
            $date_array = str_split("/");
            $data["LG_BonTrans_DDm"] = $date_array[1] . $date_array[0] . $date_array[2];
        } else {
            unset($data["LG_BonTrans_DDm"]);
        }
        if ($data["LG_BonTrans_TVA"] == "NULL" || $data["LG_BonTrans_TVA"] == null)
            unset($data["LG_BonTrans_TVA"]);
        if ($data["Qte_transfert"] == "NULL" || $data["Qte_transfert"] == null)
            unset($data["Qte_transfert"]);
        if ($data["LG_BonTrans_NumOrdre"] == "NULL" || $data["LG_BonTrans_NumOrdre"] == null)
            unset($data["LG_BonTrans_NumOrdre"]);
        if ($data["LIG_BonEntree_QtePiece"] == "LIG_BonEntree_QtePiece" || $data["LIG_BonEntree_QtePiece"] == null)
            unset($data["LIG_BonEntree_QtePiece"]);
        unset($data["LG_BonTrans_Stat"]);
        unset($data["LG_BonTrans_StatDest"]);
        unset($data["LG_ETAT"]);
        unset($data["LIG_BonEntree_QtePiece"]);

        $data["LG_BonTrans_MNTHT"] = floatval($data["LG_BonTrans_MNTHT"]);
        $data["LG_BonTrans_MNTTC"] = floatval($data["LG_BonTrans_MNTTC"]);
        $data["LG_BonTrans_PACHATNHT"] = floatval($data["LG_BonTrans_PACHATNHT"]);
        //z  $connection->statement('ALTER LOGIN aymen WITH DEFAULT_LANGUAGE =french');
        if ($data["LG_BonTrans_export"] == "NULL")
            unset($data["LG_BonTrans_export"]);
        if ($data["export"] == "NULL")
            unset($data["export"]);
        return $connection->table("ligne_bon_transfert")
            ->where("LG_BonTrans_NumBon", $data["LG_BonTrans_NumBon"])
            ->where("LG_BonTrans_CodeArt", $data["LG_BonTrans_CodeArt"])
            ->where("LG_BonTrans_Exerc", $data["LG_BonTrans_Exerc"])
            ->update($data);

    }

    public function insertLine($connection, $data)
    {
        if ($data["DDm"] != null && $data["DDm"] != "NULL") {
            $data["DDm"] = ($data["DDm"]);
        } else {
            unset($data["DDm"]);
        }
        if ($data["LG_BonTrans_DDm"] != null && $data["LG_BonTrans_DDm"] != "NULL") {
            $data["LG_BonTrans_DDm"] = ($data["LG_BonTrans_DDm"]);
        } else {
            unset($data["LG_BonTrans_DDm"]);
        }
        if ($data["LG_BonTrans_TVA"] == "NULL" || $data["LG_BonTrans_TVA"] == null) {
            $data["LG_BonTrans_TVA"] = 0;
        }
        if ($data["Qte_transfert"] == "NULL" || $data["Qte_transfert"] == null) {
            $data["Qte_transfert"] = 0;
        }
        if ($data["LG_BonTrans_NumOrdre"] == "NULL" || $data["LG_BonTrans_NumOrdre"] == null) {
            unset($data["LG_BonTrans_NumOrdre"]);
        }
        if ($data["LIG_BonEntree_QtePiece"] == "NULL" || $data["LIG_BonEntree_QtePiece"] == null) {
            $data["LIG_BonEntree_QtePiece"] = 0;
        }
        $data["LG_BonTrans_MNTHT"] = floatval($data["LG_BonTrans_MNTHT"]);
        $data["LG_BonTrans_MNTTC"] = floatval($data["LG_BonTrans_MNTTC"]);
        $data["LG_BonTrans_PACHATNHT"] = floatval($data["LG_BonTrans_PACHATNHT"]);
        if ($data["LG_BonTrans_export"] == "NULL") {
            unset($data["LG_BonTrans_export"]);
        }
        if ($data["export"] == "NULL") {
            unset($data["export"]);
        }
        return $connection->table("ligne_bon_transfert")->insert($data);
    }


}
