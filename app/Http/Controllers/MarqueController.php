<?php

namespace App\Http\Controllers;

use App\Helpers\DatabaseConnection;
use App\Helpers\Enum;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class MarqueController extends Controller
{

    public function getMarques(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $marques = $connection->table('marque')->get(["MAR_Code", "MAR_Designation", "Mar_Station", "Mar_User"]);
        $marque = collect([
            'MAR_Code' => "Autre",
            'MAR_Designation' => "Autre",
            'Mar_Station' => "",
            'Mar_User' => ""
        ]);
        return $marques->add($marque);

    }

    public function addMarqueMobile(Request $request)
    {
        $items = $request->json()->all();

        $connection = DatabaseConnection::setConnection($items);

        if (!empty($items)) {
            $result = array();
            foreach ($items["object"] as $data) {
                $connection->beginTransaction();
                try {

                    if ($data["MAR_export"] == "NULL") {
                        $data["MAR_export"] = null;
                    }
                    if ($data["MAR_DDm"] == "NULL") {
                        $data["MAR_DDm"] = null;
                    }

                    $exist = $connection->table('marque')
                        ->where('MAR_Code', '=', $data['MAR_Code'])
                        ->first();

                    if (!$exist) {
                        $updated = $connection->table('marque')->insert($data);
                        if (!$updated) {
                            $result[] = $this->setGlobalResult("MAR_Code", $data['MAR_Code'],
                                "ERROR IN THE MODIFICATION LEVEL", Enum::Error_Code);
                            throw new Exception("ERROR IN THE MODIFICATION LEVEL", Enum::Error_Code);
                        } else {
                            $result[] = $this->setGlobalResult("MAR_Code", $data['MAR_Code'], "UPDATED");
                        }
                    } else {
                        $inserted = $connection->table('marque')
                            ->where('MAR_Code', $data['MAR_Code'])
                            ->update([
                                'MAR_DDm' => $data['MAR_DDm'],
                                'MAR_Designation' => $data['MAR_Designation'],
                                'MAR_export' => $data['MAR_export'],
                                'MAR_Station' => $data['MAR_Station'],
                                'MAR_User' => $data['MAR_User']
                            ]);
                        if (!$inserted) {
                            $result[] = $this->setGlobalResult("MAR_Code", $data['MAR_Code'],
                                "ERROR IN THE INSERTION LEVEL", Enum::Error_Code
                            );
                            throw new Exception("", Enum::Error_Code);
                        } else {
                            $result[] = $this->setGlobalResult("MAR_Code", $data['MAR_Code'],
                                "INSERTED", Enum::Success_Code
                            );
                        }
                    }

                    $connection->commit();
                } catch (\Exception|\Throwable $e) {
                    $connection->rollBack();
                }

            }
            return response()->json($result);

        } else {
            return response()->json("Empty Object");
        }

    }

    public function updateFamille(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        $connection->table('famille')->where('FAM_Code', $data["FAM_Code"])->update($data);

        return ("Data modified");

    }

    public function deleteFamille(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        $connection->table('famille')->where('FAM_Code', $data["object"])->delete();
        return ("Data Deleted");
    }

    public function updateOrCreate($connection, $data, $table, $whereClauses)
    {
        $exists = $connection->table($table)->where($whereClauses)->first();

        if ($exists) {
            $connection->table($table)->where($whereClauses)->delete();
        }

        return $connection->table($table)->insert($data);

    }

}
