<?php

namespace App\Http\Controllers;

use App\Helpers\DatabaseConnection;
use App\Helpers\Enum;
use Exception;
use Illuminate\Http\Request;
use DB;
use Illuminate\Support\Facades\Log;

class TicketRayonController extends Controller
{
    public function addTicketRayon(Request $request)
    {
        $items = $request->json()->all();
        $connection = DatabaseConnection::setConnection($items);

        if (!empty($items)) {
            foreach ($items["object"] as $data) {
                $connection->beginTransaction();
                try {
                    $exist = $connection->table('ticket_rayon_mobile')
                        //->where('ART_Code', '=', $data['ART_Code'])
                        ->where('ART_Designation', '=', $data['ART_Designation'])
                        ->where('ddm', '=', $data['ddm'])
                        ->first();
                    if (!$exist) {
                        $inserted = $connection->table('ticket_rayon_mobile')->insert($data);
                        if (!$inserted) {
                            $result[] = $this->setGlobalResult("ART_Code", $data['ART_Code'],
                                "ERROR IN THE INSERTION LEVEL", Enum::Error_Code
                            );

                            throw new Exception("", Enum::Error_Insert_Ticket_Rayon);
                        } else {
                            $result[] = $this->setGlobalResult("ART_Code", $data['ART_Code'],
                                "INSERTED"
                            );
                        }
                    } else {
                        $updated = $connection->table('ticket_rayon_mobile')
                            ->where('ART_Code', $data['ART_Code'])
                            ->update([
                                'ART_Code' => $data['ART_Code'],
                                'ART_Designation' => $data['ART_Designation'],
                                'ddm' => $data['ddm'],
                                'ART_SYNC' => $data['ART_SYNC']
                            ]);
                        if (!$updated) {
                            $result[] = $this->setGlobalResult("ART_Code", $data['ART_Code'],
                                "ERROR IN THE MODIFICATION LEVEL", Enum::Error_Code
                            );
                            throw new Exception("", Enum::Error_Insert_Ticket_Rayon);
                        } else {
                            $result[] = $this->setGlobalResult("ART_Code", $data['ART_Code'],
                                "UPDATED"
                            );
                        }
                    }

                    $connection->commit();
                } catch (\Exception|\Throwable $e) {
                    $connection->rollBack();
                }
            }

            return response()->json($result);

        } else {
            return response()->json("Empty Object");
        }

    }


}