<?php

namespace App\Http\Controllers;

use App\Helpers\DatabaseConnection;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class TailleController extends Controller
{

    public function getTailles(Request $request): JsonResponse
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('Taille')->get());

    }

    public function addTailleMobile(Request $request)
    {
        $items = $request->json()->all();

        $connection = DatabaseConnection::setConnection($items);

        if (!empty($items)) {
            // $f = $connection->table('famille')->insert($data);
            foreach ($items["object"] as $data) {
                if ($data["DDm"] == "NULL")
                    $data["DDm"] = NULL;
                if ($data["export"] == "NULL")
                    $data["export"] = NULL;
                if ($data["T_export"] == "NULL")
                    $data["T_export"] = NULL;
                if ($data["T_DDm"] == "NULL")
                    $data["T_DDm"] = NULL;

                $data["T_DDm"] = NULL;

                $exist = $connection->table('Taille_Mobile')
                    ->where('TAI_Taille', '=', $data['TAI_Taille'])
                    ->get();
                if ($exist == null) {
                    $f = $connection->table('Taille_Mobile')->insert($data);
                } else {
                    $f = $connection->table('Taille_Mobile')
                        ->where('TAI_Taille', $data['TAI_Taille'])
                        ->update([
                            'DDm' => $data['DDm'],
                            'export' => $data['export'],
                            'TAI_Station' => $data['TAI_Station'],
                            'TAI_User' => $data['TAI_User'],
                            'T_user' => $data['T_user'],
                            'T_station' => $data['T_station']
                        ]);
                }
            }

            return response()->json($f);

        } else {
            return (false);
        }

    }

    public function updateFamille(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        $connection->table('famille')->where('FAM_Code', $data["FAM_Code"])->update($data);

        return ("Data modified");

    }

    public function deleteFamille(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        $connection->table('famille')->where('FAM_Code', $data["object"])->delete();
        return ("Data Deleted");
    }

    public function updateOrCreate($connection, $data, $table, $whereClauses)
    {
        $exists = $connection->table($table)->where($whereClauses)->first();

        if ($exists) {
            $connection->table($table)->where($whereClauses)->delete();
        }

        return $connection->table($table)->insert($data);

    }

}
