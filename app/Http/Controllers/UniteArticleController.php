<?php

namespace App\Http\Controllers;

use App\Helpers\DatabaseConnection;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class UniteArticleController extends Controller
{
    public function getUniteArticlesPagination(Request $request)
    {
        $data = $request->json()->all();
        $limit = $request->input('limit');
        $connection = DatabaseConnection::setConnection($data);
        $unite_article=$connection->table('Unite_article')->paginate($limit);

        return response()->json($unite_article);

    }
    public function getUniteArticles(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('Unite_article')->get());

    }

    public function addUniteArticleMobile(Request $request)
    {
        $items = $request->json()->all();

        $connection = DatabaseConnection::setConnection($items);

        if (!empty($items)) {
            //  $f = $connection->table('Unite_article_Mobile')->insert($data);
            foreach ($items["object"]["uniteArticles"] as $data) {
                if($data["UNITE_ARTICLE_DDm"]=="NULL")
                $data["UNITE_ARTICLE_DDm"]=NULL;
                if($data["UNITE_ARTICLE_export"]=="NULL")
                $data["UNITE_ARTICLE_export"]=NULL;
                if($data["UNITE_ARTICLE_TypePrixVente"]=="NULL")
                $data["UNITE_ARTICLE_TypePrixVente"]=NULL;
                if($data["UNITE_ARTICLE_station"]=="NULL")
                $data["UNITE_ARTICLE_station"]=NULL;

                $exist=$connection->table('Unite_article')
				->where('UNITE_ARTICLE_CodeUnite', '=', $data['UNITE_ARTICLE_CodeUnite'])
				->where('UNITE_ARTICLE_CodeArt', '=', $data['UNITE_ARTICLE_CodeArt'])
				->get();
				if(is_null($exist) || $exist->isEmpty()){
					$f=$connection->table('Unite_article')->insert($data);
				}else{
				$f=	$connection->table('Unite_article')
				->where('UNITE_ARTICLE_CodeUnite', $data['UNITE_ARTICLE_CodeUnite'])
				->where('UNITE_ARTICLE_CodeArt', $data['UNITE_ARTICLE_CodeArt'])
					->update([
						'UNITE_ARTICLE_DDm' => $data['UNITE_ARTICLE_DDm'],
						'UNITE_ARTICLE_export' => $data['UNITE_ARTICLE_export'],
						'UNITE_ARTICLE_IsUnitaire' => $data['UNITE_ARTICLE_IsUnitaire'],
						'UNITE_ARTICLE_PrixVenteTTC' => $data['UNITE_ARTICLE_PrixVenteTTC'],
						'UNITE_ARTICLE_QtePiece' => $data['UNITE_ARTICLE_QtePiece'],
						'UNITE_ARTICLE_TypePrixVente' => $data['UNITE_ARTICLE_TypePrixVente'],
						'UNITE_ARTICLE_user' => $data['UNITE_ARTICLE_user'],
						'UNITE_ARTICLE_station' => $data['UNITE_ARTICLE_station']
					 ]);
                }
            }
            return response()->json($f);

        } else {
            return (false);
        }

    }

    public function updateFamille(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        $connection->table('famille')->where('FAM_Code', $data["FAM_Code"])->update($data);

        return ("Data modified");

    }

    public function deleteFamille(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        $connection->table('famille')->where('FAM_Code', $data["object"])->delete();
        return ("Data Deleted");
    }

    public function updateOrCreate($connection, $data, $table, $whereClauses)
    {
        $exists = $connection->table($table)->where($whereClauses)->first();

        if ($exists) {
            $connection->table($table)->where($whereClauses)->delete();
        }

        return $connection->table($table)->insert($data);

    }
}
