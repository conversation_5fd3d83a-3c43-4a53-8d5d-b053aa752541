<?php

namespace App\Http\Controllers;

use App\Helpers\DatabaseConnection;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

/**
 * @OA\Tag(
 *     name="Article Classe Remise",
 *     description="Gestion des classes de remise des articles"
 * )
 */
class ArticleClasseRemiseController extends Controller
{

    /**
     * @OA\Post(
     *     path="/ArticleClasseRemise/getRemises",
     *     tags={"Article Classe Remise"},
     *     summary="Récupérer toutes les classes de remise des articles",
     *     description="Retourne la liste complète des classes de remise associées aux articles",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Données de connexion et paramètres de requête",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="connexion",
     *                 type="object",
     *                 description="Paramètres de connexion à la base de données",
     *                 @OA\Property(property="id_base_config", type="string", example="1562"),
     *                 @OA\Property(property="key_base", type="string", example="661-47J-J4P"),
     *                 @OA\Property(property="dbName", type="string", example="681f857273fa321692a64d730af8412b"),
     *                 @OA\Property(property="dbIpAddress", type="string", example="17d94b77f3ffb1fedc4176d9916c9265"),
     *                 @OA\Property(property="adresse_ip", type="string", example="*************"),
     *                 @OA\Property(property="port", type="string", example="2222"),
     *                 @OA\Property(property="username", type="string", example="2148ed972ff6ca8e0bf0484a9bcdc179"),
     *                 @OA\Property(property="password", type="string", example="454193ea003c0c8ea858f1501fccbd3c"),
     *                 @OA\Property(property="designation_base", type="string", example="ChahiaNewPatrimoine"),
     *                 @OA\Property(property="produit", type="string", example="ProCaisse Mobility"),
     *                 @OA\Property(property="id_entreprise", type="string", example="980"),
     *                 @OA\Property(property="date_creation", type="string", example="26-03-2024 12:09:26"),
     *                 @OA\Property(
     *                     property="licences",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer", example=29904),
     *                         @OA\Property(property="activat", type="string", example="true"),
     *                         @OA\Property(property="datef", type="string", example="2025-09-23"),
     *                         @OA\Property(property="demo", type="string", example="false"),
     *                         @OA\Property(property="device", type="string", example="promobile"),
     *                         @OA\Property(property="etablissement", type="string", example="nader"),
     *                         @OA\Property(property="id_device", type="string", example="79fc8999cc3a379e"),
     *                         @OA\Property(property="produit", type="string", example="ProCaisse Mobility"),
     *                         @OA\Property(property="version", type="string", example="v13")
     *                     )
     *                 )
     *             ),
     *             @OA\Property(
     *                 property="object",
     *                 type="object",
     *                 description="Paramètres de pagination",
     *                 @OA\Property(property="page", type="string", example="1"),
     *                 @OA\Property(property="limit", type="string", example="2000")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Liste des classes de remise récupérée avec succès",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 type="object",
     *                 @OA\Property(property="ART_CLASS_REM_Code", type="string", example="REM001", description="Code de la classe de remise"),
     *                 @OA\Property(property="ART_CLASS_REM_ArtCode", type="string", example="ART001", description="Code de l'article"),
     *                 @OA\Property(property="ART_CLASS_REM_DDm", type="string", nullable=true, example="2024-01-01", description="Date de début"),
     *                 @OA\Property(property="ART_CLASS_REM_export", type="string", nullable=true, example="Y", description="Statut d'export"),
     *                 @OA\Property(property="ART_CLASS_REM_station", type="string", nullable=true, example="ST001", description="Code station"),
     *                 @OA\Property(property="ART_CLASS_REM_user", type="string", nullable=true, example="USER001", description="Code utilisateur")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(
     *             @OA\Property(property="error", type="string", example="Erreur de connexion à la base de données")
     *         )
     *     )
     * )
     */
    public function getArticleClasseRemise(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('Article_classe_remise')->get());

    }

    /**
     * @OA\Post(
     *     path="/ArticleClasseRemise/addArticleClasseRemiseMobile",
     *     tags={"Article Classe Remise"},
     *     summary="Ajouter ou mettre à jour des classes de remise d'articles",
     *     description="Permet d'ajouter de nouvelles classes de remise ou de mettre à jour celles existantes pour les articles",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Données des classes de remise à ajouter/modifier",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="connexion",
     *                 type="object",
     *                 description="Paramètres de connexion à la base de données",
     *                 @OA\Property(property="id_base_config", type="string", example="1562"),
     *                 @OA\Property(property="key_base", type="string", example="661-47J-J4P"),
     *                 @OA\Property(property="dbName", type="string", example="681f857273fa321692a64d730af8412b"),
     *                 @OA\Property(property="dbIpAddress", type="string", example="17d94b77f3ffb1fedc4176d9916c9265"),
     *                 @OA\Property(property="adresse_ip", type="string", example="*************"),
     *                 @OA\Property(property="port", type="string", example="2222"),
     *                 @OA\Property(property="username", type="string", example="2148ed972ff6ca8e0bf0484a9bcdc179"),
     *                 @OA\Property(property="password", type="string", example="454193ea003c0c8ea858f1501fccbd3c"),
     *                 @OA\Property(property="designation_base", type="string", example="ChahiaNewPatrimoine"),
     *                 @OA\Property(property="produit", type="string", example="ProCaisse Mobility"),
     *                 @OA\Property(property="id_entreprise", type="string", example="980"),
     *                 @OA\Property(property="date_creation", type="string", example="26-03-2024 12:09:26"),
     *                 @OA\Property(
     *                     property="licences",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer", example=29904),
     *                         @OA\Property(property="activat", type="string", example="true"),
     *                         @OA\Property(property="datef", type="string", example="2025-09-23"),
     *                         @OA\Property(property="demo", type="string", example="false"),
     *                         @OA\Property(property="device", type="string", example="promobile"),
     *                         @OA\Property(property="etablissement", type="string", example="nader"),
     *                         @OA\Property(property="id_device", type="string", example="79fc8999cc3a379e"),
     *                         @OA\Property(property="produit", type="string", example="ProCaisse Mobility"),
     *                         @OA\Property(property="version", type="string", example="v13")
     *                     )
     *                 )
     *             ),
     *             @OA\Property(
     *                 property="object",
     *                 type="object",
     *                 @OA\Property(
     *                     property="articleClasseRemises",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="ART_CLASS_REM_Code", type="string", example="REM001", description="Code de la classe de remise"),
     *                         @OA\Property(property="ART_CLASS_REM_ArtCode", type="string", example="ART001", description="Code de l'article"),
     *                         @OA\Property(property="ART_CLASS_REM_DDm", type="string", nullable=true, example="2024-01-01", description="Date de début (peut être 'NULL')"),
     *                         @OA\Property(property="ART_CLASS_REM_export", type="string", nullable=true, example="Y", description="Statut d'export (peut être 'NULL')"),
     *                         @OA\Property(property="ART_CLASS_REM_station", type="string", nullable=true, example="ST001", description="Code station (peut être 'NULL')"),
     *                         @OA\Property(property="ART_CLASS_REM_user", type="string", nullable=true, example="USER001", description="Code utilisateur (peut être 'NULL')")
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Classes de remise ajoutées/mises à jour avec succès",
     *         @OA\JsonContent(
     *             type="boolean",
     *             example=true,
     *             description="Résultat de l'opération d'insertion/mise à jour"
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Données invalides ou manquantes",
     *         @OA\JsonContent(
     *             type="boolean",
     *             example=false
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(
     *             @OA\Property(property="error", type="string", example="Erreur lors de l'insertion/mise à jour")
     *         )
     *     )
     * )
     */
    public function addArticleClasseRemiseMobile(Request $request)
    {
        $items = $request->json()->all();

        $connection = DatabaseConnection::setConnection($items);

        if (!empty($items)) {
            // $f = $connection->table('famille')->insert($data);
            foreach ($items["object"]["articleClasseRemises"] as $data) {
                if ($data["ART_CLASS_REM_DDm"] == "NULL") {
                    $data["ART_CLASS_REM_DDm"] = null;
                }

                if ($data["ART_CLASS_REM_export"] == "NULL") {
                    $data["ART_CLASS_REM_export"] = null;
                }

                if ($data["ART_CLASS_REM_station"] == "NULL") {
                    $data["ART_CLASS_REM_station"] = null;
                }

                if ($data["ART_CLASS_REM_user"] == "NULL") {
                    $data["ART_CLASS_REM_user"] = null;
                }
                $exist = $connection->table('Article_classe_remise')
                    ->where('ART_CLASS_REM_Code', '=', $data['ART_CLASS_REM_Code'])
                    ->where('ART_CLASS_REM_ArtCode', '=', $data['ART_CLASS_REM_ArtCode'])
                    ->get();
                if (is_null($exist) || $exist->isEmpty()) {
                    $f = $connection->table('Article_classe_remise')->insert($data);
                } else {
                    $f = $connection->table('Article_classe_remise')
                        ->where('ART_CLASS_REM_Code', $data['ART_CLASS_REM_Code'])
                        ->where('ART_CLASS_REM_ArtCode', $data['ART_CLASS_REM_ArtCode'])
                        ->update([
                            'ART_CLASS_REM_DDm' => $data['ART_CLASS_REM_DDm'],

                            'ART_CLASS_REM_export' => $data['ART_CLASS_REM_export'],
                            'ART_CLASS_REM_station' => $data['ART_CLASS_REM_station'],
                            'ART_CLASS_REM_user' => $data['ART_CLASS_REM_user'],
                        ]);
                }
            }
            return response()->json($f);

        } else {
            return (false);
        }

    }

    /**
     * @OA\Put(
     *     path="/Famille/updateFamille",
     *     tags={"Famille"},
     *     summary="Mettre à jour une famille",
     *     description="Met à jour les informations d'une famille existante.",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Données de connexion et informations de la famille à modifier",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="connexion",
     *                 type="object",
     *                 description="Paramètres de connexion à la base de données",
     *                 @OA\Property(property="id_base_config", type="string", example="1562"),
     *                 @OA\Property(property="key_base", type="string", example="661-47J-J4P"),
     *                 @OA\Property(property="dbName", type="string", example="681f857273fa321692a64d730af8412b"),
     *                 @OA\Property(property="dbIpAddress", type="string", example="17d94b77f3ffb1fedc4176d9916c9265"),
     *                 @OA\Property(property="adresse_ip", type="string", example="*************"),
     *                 @OA\Property(property="port", type="string", example="2222"),
     *                 @OA\Property(property="username", type="string", example="2148ed972ff6ca8e0bf0484a9bcdc179"),
     *                 @OA\Property(property="password", type="string", example="454193ea003c0c8ea858f1501fccbd3c"),
     *                 @OA\Property(property="designation_base", type="string", example="ChahiaNewPatrimoine"),
     *                 @OA\Property(property="produit", type="string", example="ProCaisse Mobility"),
     *                 @OA\Property(property="id_entreprise", type="string", example="980"),
     *                 @OA\Property(property="date_creation", type="string", example="26-03-2024 12:09:26"),
     *                 @OA\Property(
     *                     property="licences",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer", example=29904),
     *                         @OA\Property(property="activat", type="string", example="true"),
     *                         @OA\Property(property="datef", type="string", example="2025-09-23"),
     *                         @OA\Property(property="demo", type="string", example="false"),
     *                         @OA\Property(property="device", type="string", example="promobile"),
     *                         @OA\Property(property="etablissement", type="string", example="nader"),
     *                         @OA\Property(property="id_device", type="string", example="79fc8999cc3a379e"),
     *                         @OA\Property(property="produit", type="string", example="ProCaisse Mobility"),
     *                         @OA\Property(property="version", type="string", example="v13")
     *                     )
     *                 )
     *             ),
     *             @OA\Property(
     *                 property="FAM_Code",
     *                 type="string",
     *                 description="Code de la famille à modifier",
     *                 example="FAM001"
     *             ),
     *             @OA\Property(
     *                 property="object",
     *                 type="object",
     *                 description="Données de la famille à mettre à jour"
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Famille modifiée avec succès",
     *         @OA\JsonContent(type="string", example="Data modified")
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(@OA\Property(property="error", type="string", example="Erreur de modification"))
     *     )
     * )
     */
    public function updateFamille(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        $connection->table('famille')->where('FAM_Code', $data["FAM_Code"])->update($data);

        return ("Data modified");

    }
    
    /**
     * @OA\Delete(
     *     path="/Famille/deleteFamille",
     *     tags={"Famille"},
     *     summary="Supprimer une famille",
     *     description="Supprime une famille selon son code.",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Données de connexion et code de la famille à supprimer",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="connexion",
     *                 type="object",
     *                 description="Paramètres de connexion à la base de données"
     *             ),
     *             @OA\Property(
     *                 property="object",
     *                 type="string",
     *                 description="Code de la famille à supprimer",
     *                 example="FAM001"
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Famille supprimée avec succès",
     *         @OA\JsonContent(type="string", example="Data Deleted")
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(@OA\Property(property="error", type="string", example="Erreur de suppression"))
     *     )
     * )
     */
    public function deleteFamille(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        $connection->table('famille')->where('FAM_Code', $data["object"])->delete();
        return ("Data Deleted");
    }

    public function updateOrCreate($connection, $data, $table, $whereClauses)
    {
        $exists = $connection->table($table)->where($whereClauses)->first();

        if ($exists) {
            $connection->table($table)->where($whereClauses)->delete();
        }

        return $connection->table($table)->insert($data);

    }

}
