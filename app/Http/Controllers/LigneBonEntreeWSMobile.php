<?php

namespace App\Http\Controllers;

use App\Helpers\DatabaseConnection;
use Carbon\Carbon;
use Illuminate\Http\Request;

class LigneBonEntreeWSMobile extends Controller
{

    /*fonction get data from database*/

    public function getLigneBonEntrees(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        $filterMois = $request->input("mois");
        $dateDebutMoisPrecedent = date('Y-m-01', strtotime('-' . $filterMois . 'months'));
        $LignesBonsEntrees = $connection->table('ligne_bon_entree');
        if ($filterMois != "") {
            $LignesBonsEntrees = $LignesBonsEntrees->where('LIG_BonEntree_DDm', '<=', date('Y-m-d'))
                ->where('LIG_BonEntree_DDm', '>=', $dateDebutMoisPrecedent);
        }else{
            $moisCourant = date('Y-m');
            $LignesBonsEntrees = $LignesBonsEntrees->whereRaw("FORMAT(LIG_BonEntree_DDm, 'yyyy-MM') = '$moisCourant'");

        }
        $LignesBonsEntrees = $LignesBonsEntrees->get();


        return response()->json($LignesBonsEntrees);


    }

    public function deleteBatchLigneBonEntrees(Request $request)
    {

        $items = $request->json()->all();

        if (!empty($items)) {
            //    $data['object']['CLI_Code'] =(new PrefixWSMobile)->getPrefix($data,"Client");
            $connection = DatabaseConnection::setConnection($items);
            $result = null;
            foreach ($items["object"] as $data) {
                $c = $connection->table('ligne_bon_entree_Mobile')
                    ->where('LIG_BonEntree_NumBon', $data["LIG_BonEntree_NumBon"])
                    ->where('LIG_BonEntree_CodeArt', $data["LIG_BonEntree_CodeArt"])->delete();
                $result[] = $c ? $data : null;
            }

        } else {
            return response()->json(null);
        }

        return response()->json($result);
    }

    public function updateBatchLigneBonEntrees(Request $request)
    {

        $items = $request->json()->all();

        if (!empty($items)) {
            //    $data['object']['CLI_Code'] =(new PrefixWSMobile)->getPrefix($data,"Client");
            $connection = DatabaseConnection::setConnection($items);
            $result = null;
            foreach ($items["object"] as $data) {

                $c = $connection->table('ligne_bon_entree_Mobile')->where('LIG_BonEntree_NumBon', $data["LIG_BonEntree_NumBon"])->where('LIG_BonEntree_CodeArt', $data["LIG_BonEntree_CodeArt"])->update($data);

                if ($c) {
                    $result[] = $data;
                } else {
                    $result[] = null;
                }

            }

        } else {
            return response()->json(null);
        }

        return response()->json($result);
    }

    public function addBatchLigneBonEntrees(Request $request)
    {
        $items = $request->json()->all();

        if (!empty($items)) {
            $connection = DatabaseConnection::setConnection($items);
            $result = null;
            foreach ($items["object"] as $data) {
                if ($data["LIG_BonEntree_Qte"][0] == ".")
                    $data["LIG_BonEntree_Qte"] = substr($data["LIG_BonEntree_Qte"], 1);
                if ($data["LIG_BonEntree_PUHT"][0] == ".")
                    $data["LIG_BonEntree_PUHT"] = substr($data["LIG_BonEntree_PUHT"], 1);
                if ($data["LIG_BonEntree_TVA"][0] == ".")
                    $data["LIG_BonEntree_TVA"] = substr($data["LIG_BonEntree_TVA"], 1);
                $data["LIG_BonEntree_PUHT"] = floatval($data["LIG_BonEntree_PUHT"]);
                $old = $connection->table('ligne_bon_entree_Mobile')
                    ->where("LIG_BonEntree_NumBon", $data["LIG_BonEntree_NumBon"])
                    ->where('LIG_BonEntree_CodeArt', $data["LIG_BonEntree_CodeArt"])
                    ->where('LIG_BonEntree_Exerc', $data["LIG_BonEntree_Exerc"])
                    ->first();
                if (!empty($old)) {
                    $c = $connection->table('ligne_bon_entree_Mobile')
                        ->where("LIG_BonEntree_NumBon", $data["LIG_BonEntree_NumBon"])
                        ->where('LIG_BonEntree_CodeArt', $data["LIG_BonEntree_CodeArt"])
                        ->where('LIG_BonEntree_Exerc', $data["LIG_BonEntree_Exerc"])->update($data);
                } else {
                    $c = $connection->table('ligne_bon_entree_Mobile')->insert($data);
                }
                if ($c) {
                    $result[] = $data;
                } else {
                    $result[] = null;
                }

            }

        } else {
            return response()->json(null);
        }

        return response()->json($result);

    }


    public function addBatchLigneBonEntreesData($connection, $items)
    {
        if (!empty($items)) {
            $result = null;
            foreach ($items as $data) {
                if ($data["LIG_BonEntree_Qte"][0] != ".") {
                } else {
                    $data["LIG_BonEntree_Qte"] = substr($data["LIG_BonEntree_Qte"], 1);
                }
                if ($data["LIG_BonEntree_PUHT"][0] != ".") {
                } else {
                    $data["LIG_BonEntree_PUHT"] = substr($data["LIG_BonEntree_PUHT"], 1);
                }
                $data["LIG_BonEntree_PUHT"] = floatval($data["LIG_BonEntree_PUHT"]);
                $old = $connection->table('ligne_bon_entree_Mobile')
                    ->where("LIG_BonEntree_NumBon", $data["LIG_BonEntree_NumBon"])
                    ->where('LIG_BonEntree_CodeArt', $data["LIG_BonEntree_CodeArt"])
                    ->where('LIG_BonEntree_Exerc', $data["LIG_BonEntree_Exerc"])
                    ->first();
                if (!empty($old)) {
                    $c = $connection->table('ligne_bon_entree_Mobile')
                        ->where("LIG_BonEntree_NumBon", $data["LIG_BonEntree_NumBon"])
                        ->where('LIG_BonEntree_CodeArt', $data["LIG_BonEntree_CodeArt"])
                        ->where('LIG_BonEntree_Exerc', $data["LIG_BonEntree_Exerc"])->update($data);
                } else {
                    $c = $connection->table('ligne_bon_entree_Mobile')->insert($data);
                }

                $result[] = $c ? $data : null;
            }

        } else {
            return response()->json(null);
        }

        return response()->json($result);

    }

}
