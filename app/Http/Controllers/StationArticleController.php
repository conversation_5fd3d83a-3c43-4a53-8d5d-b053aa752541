<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Helpers\DatabaseConnection;

class StationArticleController extends Controller
{


    public function getArticleCountInStation($codeArt, $station, $connection)
    {
        $res =  $connection->table("StationArticle")->where("SART_CodeSatation", $station)->where("SART_CodeArt", $codeArt)->select("SART_Qte")->first();
        return $res->SART_Qte;
    }
    public function  getArticleCountInStationOUt(){
        $connection = DatabaseConnection::setConnection(null);
        return response()->json($this->getArticleCountInStation("001001003", "002", $connection)+400);
    }
    public function setArticleSockQantityInStation($codeArt, $station, $quantity, $connection)
    {
        return $connection->table("StationArticle")->where("SART_CodeSatation", $station)->where("SART_CodeArt", $codeArt)->update(["SART_Qte" => $quantity]);
    }

    public function getstockArticle(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('stationArticle')->whereNotNull('SART_CodeSatation')->get());

    }
    public function getstockArticlePagination(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);

        $limit = $request->input('limit');
        $connection = DatabaseConnection::setConnection($data);
        $stationArticle=$connection->table('stationArticle')
            ->whereNotNull('SART_CodeSatation')->paginate($limit);
        return response()->json($stationArticle);

    }

}
