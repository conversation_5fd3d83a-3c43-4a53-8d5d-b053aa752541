<?php

namespace App\Http\Controllers;

use App\Helpers\Enum;
use Carbon\Carbon;


class LigneFactureMobileController extends Controller
{

    public function addBatchLigneFacturesData($connection, $items ,$num_fact,$ticket)
    {

        $date = Carbon::now();
        $lignes = null;
        if (!empty($items)) {
            $result = null;
            $nbLigneFacture= count($items);
            $count = 0;
            foreach ($items as $data) {
                $error_code = null;
                //Get Article
                $article = $connection->table('article')->where('ART_Code','=',$data->LT_CodArt)->first();
                //Get Unite
                $unite = $connection->table('unite')->where('UNI_Code','=',$data->LT_Unite)->first();
                $c = null;
                switch ($data->LT_Qte) {
                    case 0:
                        $PUHT = 0;
                        break;
                    default:
                        $LG_FACT_PUHT = $data->LT_MtHT / $data->LT_Qte;
                        $PUHT = round($LG_FACT_PUHT, 4);
                        break;
                }
                $LG_FACT_MntTva = $data->LT_MtTTC-$data->LT_MtHT;
                $MntTva = round($LG_FACT_MntTva, 4);


                $c = $connection->table('ligne_facture')->insert([
                    'LG_FACT_NumFact' =>$num_fact,
                    'LG_FACT_Exerc' =>  $data->LT_Exerc,
                    'LG_FACT_NumPiece' => $data->LT_NumTicket_M,
                    'LG_FACT_TypePiece' =>  'Ticket',
                    'LG_FACT_ExercPiece' => $data->LT_Exerc,
                    'LG_FACT_CodeLigne' => $data->LT_CodArt,
                    'LG_FACT_DesgLigne' =>$article->ART_Designation ,
                    'LG_FACT_PUHT' => $PUHT,
                    'LG_FACT_Qte' =>  $data->LT_Qte,
                    'LG_FACT_TauxRemise' =>  $data->LT_Taux_Remise,
                    'LG_FACT_MntRemise' =>  $data->LT_Remise,
                    'LG_FACT_MntNetHt' => $data->LT_MtHT,
                    'LG_FACT_TauxTva' => $data->LT_TVA,
                    'LG_FACT_MntTva' => $MntTva,
                    'LG_FACT_PUTTC' => 0,
                    'LG_FACT_MntTTC' => $data->LT_MtTTC,
                    'LG_FACT_PrixPub' => 0,
                    'LG_FACT_MntHT' =>$data->LT_MtHT,
                    'LG_FACT_Etat' => '',
                    'LG_FACT_Typeligne' => '',
                    'LG_FACT_CodeUnit' => $unite->UNI_Code,
                    'LG_FACT_UniDesg' => $unite->UNI_Designation,
                    'LG_FACT_user' => $ticket->TIK_CodClt,
                    'LG_FACT_station' => $ticket->TIK_station,
                    'LG_FACT_export' => 0,
                    'LG_FACT_DDm' => $date,
                    'LG_FACT_Mnt_Fodec' => 0,
                    'LG_FACT_Fodec' => 0,
                    'LG_FACT_DC' => 0,
                    'LG_FACT_Mnt_DC' => 0,
                    'LG_FACT_BE' => $data->LT_IdCarnet,
                    'LG_FACT_BE_Exercice' => '',
                    'LG_Fact_NumAvoir' => '',
                    'LG_Fact_ExerciceAvoir' => '',
                    'LG_QteAvoir' => 0,
                    'LG_Fact_OrdreAvoir' => 0,
                    'LG_Fact_MntHTVA' => 0,
                    'LG_Fact_Retour' => 0,
                    'CMP_Article' => 0,
                    'LG_NumSerie' => '',

                ]);

                if ($c) {
                    $count++;
                }

               //Update lignes Tickets
                $connection->table('LigneTicket')
                    ->where('LT_NumTicket',$ticket->TIK_NumTicket)
                    ->where('LT_IdCarnet','=',$data->LT_IdCarnet)
                    ->where('LT_Exerc',$data->LT_Exerc)
                    ->update([
                        'LT_NumFacture' => $num_fact,
                        'LT_QteFacturee' => $data->LT_Qte,
                        'LT_ExercFacture' => $data->LT_Exerc
                    ]);



            }
            if ($count < $nbLigneFacture) {
                return Enum::Error_Insert_Ligne_Facture;

            }else{
                return Enum::Success_Code;
            }



        }else
        {
            return null;
        }
    }
}
