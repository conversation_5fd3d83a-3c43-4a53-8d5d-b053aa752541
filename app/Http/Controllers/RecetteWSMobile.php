<?php

namespace App\Http\Controllers;

use App\Helpers\AppHelper;
use App\Helpers\DatabaseConnection;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use DateTime;
use Illuminate\Support\Facades\DB;

class RecetteWSMobile extends Controller
{
    public function getRecetteByReglement(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
    //    $date_from =  AppHelper::setDateFormat($data["object"]["date_from"]);
     //   $date_to =  AppHelper::setDateFormat($data["object"]["date_to"]);
          $date_from =  $data["object"]["date_from"];
           $date_to = $data["object"]["date_to"];
        $caisser = $data["object"]["caissier"];

        $station=$request->input('station');
        $caisserByStation=$this->getCaissierByStation($connection,$station);

        if($caisser == null) {
            $tab=array();
            foreach ($caisserByStation as $caisserByStat ){
                array_push($tab,$caisserByStat->Code_Ut);
            }

            $result1 = $connection->table('View_RecapVente')
                ->select($connection->raw('ISNULL(sum(MEsp),0) as MntEspece,ISNULL(sum(Mcheque),0) as MntCheque,ISNULL(sum(MTraite),0) as Traite,ISNULL(sum(MCB),0) as MntCB,
	                         ISNULL(sum(MtTicket),0) as MtTicket, ISNULL(sum(MtReg),0) as MtReg, ISNULL(SUM(MntCr),0) as MntCredit'))
                ->whereBetween('Datecomp', [$date_from, $date_to])
                ->whereIn('IdUser',$tab)
                ->first();

        } else{
            $result1 = $connection->table('View_RecapVente')
                ->select($connection->raw('ISNULL(sum(MEsp),0) as MntEspece,ISNULL(sum(Mcheque),0) as MntCheque,ISNULL(sum(MTraite),0) as Traite,ISNULL(sum(MCB),0) as MntCB,
	                                   ISNULL(sum(MtTicket),0) as MtTicket, ISNULL(sum(MtReg),0) as MtReg, ISNULL(SUM(MntCr),0) as MntCredit'))
                ->whereBetween('Datecomp', [$date_from, $date_to])
                ->where('IdUser','=',$caisser)
                ->first();
        }
        if($caisser == null) {
            $tab=array();
            foreach ($caisserByStation as $caisserByStat ){

               // $tab[]['Code_Ut']=(int)$caisserByStat->Code_Ut;
                array_push($tab,(int)$caisserByStat->Code_Ut);
            }

            $resultDepance = $connection->table('depence_caisse')
                ->select($connection->raw('ISNULL(sum(DEP_Montant),0)as depense'))
                ->whereBetween('DDm', [$date_from, $date_to])
                ->whereIn('DEP_User',$tab)
                ->first()->depense;


        }
        else{
            $resultDepance = $connection->table('depence_caisse')
                ->select(DB::raw('ISNULL(sum(DEP_Montant),0)as depense'))
                ->whereBetween('DDm', [$date_from, $date_to])
                ->where('DEP_User','=',$caisser)
                ->first()->depense;

        }

        if($caisser == null) {

            $tab=array();
            foreach ($caisserByStation as $caisserByStat ){

                // $tab[]['Code_Ut']=(int)$caisserByStat->Code_Ut;
                array_push($tab,(int)$caisserByStat->Code_Ut);
            }
            $result2 = $connection->table('View_RegelementTicket')
                ->select($connection->raw('count(TIK_NumTicket) as TIK_NumTicket'))
                ->whereBetween('TIK_DateHeureTicket', [$date_from, $date_to])
                ->whereIn('REGC_User',$tab)
                ->first()->TIK_NumTicket;
        }
        else {
            $result2 = $connection->table('View_RegelementTicket')
                ->select($connection->raw('count(TIK_NumTicket) as TIK_NumTicket'))
                ->whereBetween('TIK_DateHeureTicket', [$date_from, $date_to])
                ->where('REGC_User','=',$caisser)
                ->first()->TIK_NumTicket;
        }

        $result["TIK_NumTicket"] = $result2;
        $result["Recap_vente"] = $result1;
        $result["depense"]=$resultDepance;

        return response()->json($result);

    }

    public function getNbrePassageParDate(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        $req = "Set Language French SELECT count(TIK_NumTicket) FROM [View_RegelementTicket]             WHERE TIK_DateHeureTicket between '" . ($data["object"][0]) . "'  AND '" . ($data["object"][1]) . "'";
        return response()->json(collect($connection->select($req)->first()));
        //return response()->json(  $req);
    }

    public function getDetailRecette(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $req = "Set Language French SELECT     sum(REGC_MntEspece)as Espece, sum(REGC_MntCarteBancaire)as carte_bancaire,	
         sum (REGC_MntChéque) as cheque, sum (REGC_MntTraite)as Mnt_Traite, count(TIK_NumTicket) as nbr_ticket
            FROM View_RegelementTicket
            WHERE TIK_DateHeureTicket between '" . ($data["object"][0]) . "'  AND '" . ($data["object"][1]) . "'";
        return response()->json(collect($connection->select($req)->first()));

    }

    public function getCaissierByStation($connection,$station){
        if(!!$station)
        {
            $result = $connection->table('Utilisateur')
                ->leftJoin('station', 'station.STAT_Code', '=', 'Utilisateur.Station')
                ->where('station.STAT_Code','=',$station)
                ->select('Utilisateur.Code_Ut')
                ->get();

        }else
        {
            $result = $connection->table('Utilisateur')
                ->leftJoin('station', 'station.STAT_Code', '=', 'Utilisateur.Station')
                ->select('Utilisateur.Code_Ut')
                ->get();
        }
        return $result;

    }

}
