<?php

namespace App\Http\Controllers;

use App\Enums\AuthorizationUser;
use App\Helpers\AppHelper;
use App\Helpers\DatabaseConnection;
use App\Models\article;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

/**
 * @OA\Tag(
 *     name="Articles Mobile",
 *     description="Gestion des articles depuis l'application mobile ProCaisse"
 * )
 */
class ArticleWSMobile extends Controller
{
    /**
     * @OA\Post(
     *     path="/Article/getArticleByCode",
     *     tags={"Articles Mobile"},
     *     summary="Récupérer un article par son code",
     *     description="Retourne les détails d'un article avec ses unités en fonction de son code",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Paramètres de connexion et code article",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="connexion",
     *                 ref="#/components/schemas/DatabaseConnection"
     *             ),
     *             @OA\Property(
     *                 property="object",
     *                 type="string",
     *                 example="ART001",
     *                 description="Code de l'article à rechercher"
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Article trouvé avec succès",
     *         @OA\JsonContent(ref="#/components/schemas/ArticleWithUnite")
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Article non trouvé",
     *         @OA\JsonContent(type="null")
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function getArticleByCode(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        return response()->json(
            $connection->table('article')
                ->join('Unite_article', 'Unite_article.UNITE_ARTICLE_CodeArt', '=', 'Article.ART_Code')
                ->where('Unite_article.UNITE_ARTICLE_IsUnitaire', '=', 1)
                ->where('ART_Code', $data["object"])->first()
        );
    }

    /**
     * @OA\Post(
     *     path="/Article/getArticleByBarCode",
     *     tags={"Articles Mobile"},
     *     summary="Rechercher un article par code à barres",
     *     description="Retourne les détails d'un article en recherchant par code à barres ou nom",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Paramètres de connexion et code à barres",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="connexion",
     *                 ref="#/components/schemas/DatabaseConnection"
     *             ),
     *             @OA\Property(
     *                 property="object",
     *                 type="string",
     *                 example="1234567890123",
     *                 description="Code à barres ou nom de l'article à rechercher"
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Article trouvé avec succès",
     *         @OA\JsonContent(ref="#/components/schemas/ArticleDetail")
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Article non trouvé",
     *         @OA\JsonContent(
     *             type="null"
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function getArticleByBarCode(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $barcode = $data["object"];
        $response = collect($connection->select("Set Language French  SET DATEFORMAT 'ymd' SELECT dbo.article.ART_Code, dbo.article.ART_CodeBar, dbo.article.ART_Designation, ISNULL(dbo.article.ART_PrixUnitaireHT, 0) AS ART_PrixUnitaireHT, dbo.article.ART_TVA,
	ISNULL(dbo.article.ART_QteStock, 0) AS ART_QteStock, ISNULL(dbo.Unite_article.UNITE_ARTICLE_PrixVenteTTC, 0) AS pvttc,
dbo.article.photo_Path, dbo.marque.MAR_Designation,
	dbo.article.PrixSolde, ISNULL(dbo.article.TauxSolde, 0) AS TauxSolde, dbo.famille.FAM_Lib
        FROM   dbo.article LEFT OUTER JOIN
	dbo.famille ON dbo.article.ART_Famille = dbo.famille.FAM_Code LEFT OUTER JOIN
	dbo.marque ON dbo.article.ART_Marque = dbo.marque.MAR_Code LEFT OUTER JOIN
	dbo.Unite_article ON dbo.article.ART_Code = dbo.Unite_article.UNITE_ARTICLE_CodeArt
WHERE (dbo.Unite_article.UNITE_ARTICLE_IsUnitaire = 'True') AND (ISNULL(dbo.article.is_bloquer, 'False') <> 'True') AND ((dbo.article.ART_CodeBar like '%$barcode%') or (dbo.article.ART_Designation  like '%$barcode%'))"))->first();
        return response()->json($response);
    }

    /**
     * @OA\Post(
     *     path="/Article/getArticleByName",
     *     tags={"Articles Mobile"},
     *     summary="Rechercher un article par nom",
     *     description="Retourne les articles correspondant au nom donné (recherche dans nom, code à barres et fournisseur)",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Paramètres de connexion et terme de recherche",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="connexion",
     *                 ref="#/components/schemas/DatabaseConnection"
     *             ),
     *             @OA\Property(
     *                 property="object",
     *                 type="string",
     *                 example="Pain",
     *                 description="Terme de recherche (nom, code à barres ou fournisseur)"
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Articles trouvés avec succès",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/ArticleComplet")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function getArticleByName(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $name = $data["object"];
        return response()->json(collect($connection->select("
			Set Language French  SET DATEFORMAT 'ymd' SELECT     dbo.article.ART_Code, dbo.article.ART_CodeBar,dbo.article.ART_Designation, ISNULL(dbo.article.ART_PrixUnitaireHT, 0) AS ART_PrixUnitaireHT, dbo.article.ART_TVA, dbo.article.ART_CodeFrs, dbo.article.ART_CodeFrs2,
				ISNULL(dbo.article.ART_QteStock, 0) AS ART_QteStock, ISNULL(dbo.Unite_article.UNITE_ARTICLE_PrixVenteTTC, 0) AS pvttc, dbo.marque.MAR_Designation, dbo.article.PrixSolde, ISNULL(dbo.article.TauxSolde,
				0) AS TauxSolde, dbo.famille.FAM_Lib, dbo.famille.FAM_Code, dbo.marque.MAR_Code, dbo.Unite_article.UNITE_ARTICLE_QtePiece, dbo.Unite_article.UNITE_ARTICLE_CodeUnite, dbo.couleur.COU_Designation,
				dbo.article.ART_TAILLE,dbo.article_code_bar.Fils_CodeBar,Art_MaxTRemise ,
dbo.article.photo_Path,
                dbo.fournisseur.FRS_Nomf AS fournissuer
  
		FROM         dbo.article LEFT OUTER JOIN
				dbo.couleur ON dbo.article.ART_Couleur = dbo.couleur.COU_Code LEFT OUTER JOIN
				dbo.famille ON dbo.article.ART_Famille = dbo.famille.FAM_Code LEFT OUTER JOIN
				dbo.fournisseur ON dbo.article.ART_Fournisseur = dbo.fournisseur.FRS_codef LEFT OUTER JOIN
				dbo.marque ON dbo.article.ART_Marque = dbo.marque.MAR_Code LEFT OUTER JOIN
				dbo.Unite_article ON dbo.article.ART_Code = dbo.Unite_article.UNITE_ARTICLE_CodeArt   JOIN
				dbo.article_code_bar ON dbo.article.ART_CodeBar = dbo.article_code_bar.Parent_CodeBar
	 	WHERE (dbo.Unite_article.UNITE_ARTICLE_IsUnitaire = 'True')
		AND (ISNULL(dbo.article.is_bloquer, 'False') <> 'True')
		AND (dbo.article.ART_CodeBar like '%$name%'
		or dbo.article.ART_Designation  like '%$name%'
		or dbo.fournisseur.FRS_Nomf  like '%$name%')
        ")));
    }

    /**
     * @OA\Post(
     *     path="/Article/getCountArticle",
     *     tags={"Articles Mobile"},
     *     summary="Obtenir le nombre total d'articles",
     *     description="Retourne le nombre total d'articles non mouvementés dans la base de données",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Paramètres de connexion à la base de données",
     *         @OA\JsonContent(ref="#/components/schemas/StandardRequest")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Nombre d'articles récupéré avec succès",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="countArticle", type="integer", example=1234, description="Nombre total d'articles")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function getCountArticle(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $count = $connection->table('View_GetArticleNonMouvemente')->count();
        return response()->json(["countArticle" => $count]);
    }

    /**
     * @OA\Post(
     *     path="/Article/getArticles",
     *     tags={"Articles Mobile"},
     *     summary="Récupérer la liste des articles avec autorisations",
     *     description="Retourne la liste des articles avec gestion des autorisations utilisateur, filtrage par station et type d'utilisateur",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Paramètres de connexion et filtres",
     *         @OA\JsonContent(ref="#/components/schemas/StandardRequest")
     *     ),
     *     @OA\Parameter(
     *         name="station",
     *         in="query",
     *         description="Code de la station pour filtrer",
     *         required=false,
     *         @OA\Schema(type="string", example="ST001")
     *     ),
     *     @OA\Parameter(
     *         name="limit",
     *         in="query",
     *         description="Nombre d'éléments par page",
     *         required=false,
     *         @OA\Schema(type="integer", example=15)
     *     ),
     *     @OA\Parameter(
     *         name="ddm",
     *         in="query",
     *         description="Date de dernière modification",
     *         required=false,
     *         @OA\Schema(type="string", format="date", example="2024-01-01")
     *     ),
     *     @OA\Parameter(
     *         name="user",
     *         in="header",
     *         description="Code utilisateur pour autorisations",
     *         required=true,
     *         @OA\Schema(type="string", example="USER001")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Liste des articles récupérée avec succès",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/ArticleComplet")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function getArticle(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $article = new article();
        $Article_Site = $connection->table('ville')->pluck('Article_Site')->first();
        $station = $request->input('station');
        $limit = $request->input('limit');
        $ddm = $request->input('ddm');
        $result = array();
        if (!!Request::createFromGlobals()->hasHeader('user')) {
            $idUser = $request->header('user');
            $authorizationProductMovement = $connection->table('AutorisationUser')
                ->where('AutoCodeUt', '=', $idUser)
                ->where("AutoCodeAu", "=", AuthorizationUser::authorizationProductMovement)
                ->pluck('AutEtat')
                ->first();
            if (!$station) {

                //article non Mouvementé
                $result = $connection->table('View_GetArticleNonMouvemente')
                    ->leftJoin('dbo.Unite_article', 'View_GetArticleNonMouvemente.ART_Code', '=', 'dbo.Unite_article.UNITE_ARTICLE_CodeArt')
                    ->select('View_GetArticleNonMouvemente.*', 'dbo.Unite_article.UNITE_ARTICLE_PrixVenteTTC') // Sélectionnez uniquement les colonnes nécessaires de la table dbo.Unite_article
                    ->paginate($limit);

            } else {
                $type_user = $connection->table('utilisateur')
                    ->where('Code_Ut', '=', $idUser)
                    ->pluck('Type_user')->first();

                if (strtolower($type_user) === 'operateur patrimoine') {
                    $result = array_values($article->getPatrimoine($request, $connection));
                } else {
                    //Type User Vendeur
                    //Article Site
                    if ((bool)$Article_Site) {
                        $query = $article->articleStation($request, $ddm);
                        //filter by station
                        $query .= " AND (dbo.ArticleStation.STAT_Code = " . $station . ") ";
                        $result = $connection->select($query);
                        $result = $article->PrixSiteOrPrixPublique($result, $connection, $station);

                    } else {
                        //Station Article
                        $query = $article->ArticleMVT() . "AND (LOWER(dbo.article.Type_Produit) != 'patrimoine')";

                        //filter by station
                        $query .= " AND (dbo.StationArticle.SART_CodeSatation = " . $station . ") ";
                        /* if ($ddm) {
                            $query .= "AND dbo.article.ddm >= '" . $ddm . "'";
                        }*/
                        $result = $connection->select($query);

                    }
                }
            }
        }

        return response()->json($result);
    }

    /**
     * @OA\Post(
     *     path="/Article/getArticlesMobile",
     *     tags={"Articles Mobile"},
     *     summary="Récupérer tous les articles (mobile)",
     *     description="Retourne tous les articles optimisés pour l'application mobile depuis la table article_Mobile",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Paramètres de connexion à la base de données",
     *         @OA\JsonContent(ref="#/components/schemas/StandardRequest")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Liste des articles mobile récupérée avec succès",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/ArticleMobile")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function getArticles(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('article_Mobile')->get());
    }

    /**
     * @OA\Post(
     *     path="/Article/getArticlesByUser",
     *     tags={"Articles Mobile"},
     *     summary="Récupérer les articles par utilisateur",
     *     description="Retourne les articles associés à un utilisateur spécifique avec leurs stations et clients",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Paramètres de connexion et code utilisateur",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="connexion",
     *                 ref="#/components/schemas/DatabaseConnection"
     *             ),
     *             @OA\Property(
     *                 property="object",
     *                 type="string",
     *                 example="USER001",
     *                 description="Code de l'utilisateur"
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Articles de l'utilisateur récupérés avec succès",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/ArticleUtilisateur")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function getArticlesByUser(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json(collect($connection->select("Set Language French  SET DATEFORMAT 'ymd' SELECT dbo.article.ART_Code, dbo.article.ART_CodeBar, dbo.article.photo_Path, dbo.article.ART_Designation, ISNULL(dbo.article.ART_PrixUnitaireHT, 0) AS ART_PrixUnitaireHT, dbo.article.ART_TVA, dbo.article.ART_CodeFrs,
                         dbo.article.ART_CodeFrs2,
						 ISNULL(dbo.article.ART_QteStock, 0) AS ART_QteStock,
						 ISNULL(dbo.Unite_article.UNITE_ARTICLE_PrixVenteTTC, 0) AS pvttc,
						 isnull(dbo.article.ART_PrixGros1,0) AS prixGros1,
						 isnull(dbo.article.ART_PrixGros2,0) AS prixGros2,
						 isnull(dbo.article.ART_PrixGros3,0) AS prixGros3,
						 ISNULL(dbo.Unite_article.UNITE_ARTICLE_PrixVenteTTC, 0) AS ART_PrixPublique,
						 dbo.marque.MAR_Designation, dbo.article.PrixSolde,
                         ISNULL(dbo.article.TauxSolde, 0) AS TauxSolde,
						 dbo.famille.FAM_Lib, dbo.famille.FAM_Code, dbo.marque.MAR_Code, dbo.marque.photo_PathM as photo_Path_Marque,
                        dbo.station.STAT_Desg, dbo.station.STAT_Code, dbo.station.Code_Ut1,
                         dbo.ArticleClient.Code_Meti,dbo.Unite_article.UNITE_ARTICLE_QtePiece,
        dbo.Unite_article.UNITE_ARTICLE_CodeUnite,dbo.ArticleClient.CLI_Code

        FROM            dbo.article INNER JOIN
                         dbo.ArticleStation ON dbo.article.ART_Code = dbo.ArticleStation.ART_Code INNER JOIN
                         dbo.station ON dbo.ArticleStation.STAT_Code = dbo.station.STAT_Code INNER JOIN
                         dbo.ArticleClient ON dbo.article.ART_Code = dbo.ArticleClient.ART_Code LEFT OUTER JOIN
                         dbo.famille ON dbo.article.ART_Famille = dbo.famille.FAM_Code LEFT OUTER JOIN
                         dbo.marque ON dbo.article.ART_Marque = dbo.marque.MAR_Code LEFT OUTER JOIN
                         dbo.Unite_article ON dbo.article.ART_Code = dbo.Unite_article.UNITE_ARTICLE_CodeArt
        WHERE        (dbo.Unite_article.UNITE_ARTICLE_IsUnitaire = 'True') AND (ISNULL(dbo.article.is_bloquer, 'False') <> 'True') AND (dbo.station.Code_Ut1 = '" . $data["object"] . "')")));
    }

    /**
     * @OA\Post(
     *     path="/Article/getArticlesByStation",
     *     tags={"Articles Mobile"},
     *     summary="Récupérer les articles par station",
     *     description="Retourne les articles associés à une station spécifique avec leurs quantités en stock",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Paramètres de connexion et code station",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="connexion",
     *                 ref="#/components/schemas/DatabaseConnection"
     *             ),
     *             @OA\Property(
     *                 property="object",
     *                 type="string",
     *                 example="ST001",
     *                 description="Code de la station"
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Articles de la station récupérés avec succès",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/ArticleStation")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function getArticleByStation(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        $codeSatation = $data["object"];

        return response()->json(collect($connection->select(" Set Language French  SET DATEFORMAT 'ymd' SELECT  dbo.article.ART_Code, dbo.article.ART_CodeBar ,
	(select top 1 Fils_CodeBar
	 from article_code_bar
	 where dbo.article.ART_CodeBar=article_code_bar.Parent_CodeBar
	 and article_code_bar.Parent_CodeBar
	 not like article_code_bar.Fils_CodeBar) as Fils_CodeBar,
              dbo.article.ART_Designation,
              ISNULL(dbo.article.ART_PrixUnitaireHT, 0) AS ART_PrixUnitaireHT,
              dbo.article.ART_TVA,
              ISNULL(dbo.article.ART_QteStock, 0) AS ART_QteStock,
              isnull(dbo.article.ART_PrixGros1,0) AS prixGros1,
              isnull(dbo.article.ART_PrixGros2,0) AS prixGros2,
              isnull(dbo.article.ART_PrixGros3,0) AS prixGros3, 
		dbo.article.photo_Path,
              ISNULL(dbo.Unite_article.UNITE_ARTICLE_PrixVenteTTC, 0) AS ART_PrixPublique,
              isnull(dbo.article.PrixSolde,0) AS pvttc,
              dbo.marque.MAR_Designation,
              dbo.article.PrixSolde, ISNULL(dbo.article.TauxSolde, 0) AS TauxSolde,
              dbo.famille.FAM_Lib,
              dbo.StationArticle.SART_Qte,
              dbo.StationArticle.SART_CodeSatation,
              dbo.famille.FAM_Code,
              dbo.Unite_article.UNITE_ARTICLE_QtePiece,
              dbo.Unite_article.UNITE_ARTICLE_CodeUnite,
              Art_MaxTRemise
    FROM dbo.article
     INNER JOIN dbo.StationArticle 
     ON dbo.article.ART_Code = dbo.StationArticle.SART_CodeArt 
     LEFT OUTER JOIN dbo.famille 
     ON dbo.article.ART_Famille = dbo.famille.FAM_Code
     LEFT OUTER JOIN dbo.marque 
     ON dbo.article.ART_Marque = dbo.marque.MAR_Code
     LEFT OUTER JOIN dbo.Unite_article 
     ON dbo.article.ART_Code = dbo.Unite_article.UNITE_ARTICLE_CodeArt
       WHERE      (ISNULL(dbo.article.is_bloquer, N'False') <> 'True') AND (dbo.StationArticle.SART_CodeSatation = '$codeSatation') $request->additionalQuery")));
    }


    /**
     * @OA\Post(
     *     path="/Article/getArticleByX",
     *     tags={"Articles Mobile"},
     *     summary="Récupérer les articles par champ personnalisé",
     *     description="Retourne les articles selon un champ et une valeur personnalisés (recherche flexible)",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Paramètres de connexion et critères de recherche",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="connexion",
     *                 ref="#/components/schemas/DatabaseConnection"
     *             ),
     *             @OA\Property(
     *                 property="field",
     *                 type="string",
     *                 example="ART_Famille",
     *                 description="Nom du champ à rechercher"
     *             ),
     *             @OA\Property(
     *                 property="value",
     *                 type="string",
     *                 example="FAM001",
     *                 description="Valeur à rechercher"
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Articles trouvés avec succès",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/Article")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function getArticleByX(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('article')->where($request->field, $request->value)->get());
    }

    /**
     * @OA\Post(
     *     path="/Article/addArticle",
     *     tags={"Articles Mobile"},
     *     summary="Ajouter un article simple",
     *     description="Ajoute un nouvel article dans la base de données (version simplifiée)",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Données de l'article à ajouter",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="connexion",
     *                 ref="#/components/schemas/DatabaseConnection"
     *             ),
     *             @OA\Property(
     *                 property="object",
     *                 ref="#/components/schemas/ArticleInput"
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Article ajouté avec succès",
     *         @OA\JsonContent(
     *             type="boolean",
     *             example=true
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Données invalides ou manquantes",
     *         @OA\JsonContent(
     *             type="boolean",
     *             example=false
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function addArticle(Request $request)
    {
        $data = $request->json()->all();

        if (!empty($data)) {
            return response()->json(true);
        } else {
            return response()->json(false);
        }
    }

    /**
     * @OA\Post(
     *     path="/Article/addArticleMobile",
     *     tags={"Articles Mobile"},
     *     summary="Ajouter ou mettre à jour des articles depuis mobile",
     *     description="Permet d'ajouter de nouveaux articles ou de mettre à jour ceux existants depuis l'application mobile",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Paramètres de connexion",
     *         @OA\JsonContent(ref="#/components/schemas/StandardRequest")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Articles ajoutés/mis à jour avec succès",
     *         @OA\JsonContent(
     *             type="boolean",
     *             example=true
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Données invalides ou manquantes",
     *         @OA\JsonContent(
     *             type="boolean",
     *             example=false
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function addArticleMobile(Request $request)
    {
        $items = $request->json()->all();
        $connection = DatabaseConnection::setConnection($items);
        $success = true;

        if (!empty($items)) {
            foreach ($items["object"]["articles"] as $data) {
                unset($data["UNITE_ARTICLE_QtePiece"]);
                unset($data["count"]);
                unset($data["prix"]);
                unset($data["pvttc"]);
                unset($data["remise"]);
                unset($data["SART_Qte"]);

                if ($data["ART_Image"] == "NULL") {
                    $data["ART_Image"] = null;
                }

                if ($data["ART_export"] == "NULL") {
                    $data["ART_export"] = null;
                }

                if ($data["ART_MargeB"] == "NULL") {
                    $data["ART_MargeB"] = null;
                }

                if ($data["ART_Poid_Qte"] == "NULL") {
                    $data["ART_Poid_Qte"] = null;
                }

                if ($data["ART_DateCr"] == "NULL") {
                    $data["ART_DateCr"] = null;
                }

                if ($data["ART_DC"] == "NULL") {
                    $data["ART_DC"] = null;
                }

                if ($data["ddm"] == "NULL") {
                    $data["ddm"] = null;
                }

                if ($data["ART_ddm"] == "NULL") {
                    $data["ART_ddm"] = null;
                }

                if ($data["ART_QteStock"] == "NULL") {
                    $data["ART_QteStock"] = null;
                }

                if ($data["ART_PrixUnitaireHT"] == "NULL") {
                    $data["ART_PrixUnitaireHT"] = null;
                }

                if ($data["ART_PrixUnitaireHTVA"] == "NULL") {
                    $data["ART_PrixUnitaireHTVA"] = null;
                }

                if ($data["ART_TVA"] == "NULL") {
                    $data["ART_TVA"] = null;
                }

                if ($data["ART_QTEmin"] == "NULL") {
                    $data["ART_QTEmin"] = null;
                }

                if ($data["ART_Fodec"] == "NULL") {
                    $data["ART_Fodec"] = null;
                }

                if ($data["ART_DC"] == "NULL") {
                    $data["ART_DC"] = null;
                }

                if ($data["ART_QTEmax"] == "NULL") {
                    $data["ART_QTEmax"] = null;
                }

                if ($data["ART_QteDeclaree"] == "NULL") {
                    $data["ART_QteDeclaree"] = null;
                }

                if ($data["ART_Prix_AchatFactReel"] == "NULL") {
                    $data["ART_Prix_AchatFactReel"] = null;
                }

                if ($data["ART_PrixGros1"] == "NULL") {
                    $data["ART_PrixGros1"] = null;
                }

                if ($data["ART_QtePrixGros1"] == "NULL") {
                    $data["ART_QtePrixGros1"] = null;
                }

                if ($data["ART_PrixGros2"] == "NULL") {
                    $data["ART_PrixGros2"] = null;
                }

                if ($data["ART_QtePrixGros2"] == "NULL") {
                    $data["ART_QtePrixGros2"] = null;
                }

                if ($data["ART_PrixGros3"] == "NULL") {
                    $data["ART_PrixGros3"] = null;
                }

                if ($data["ART_QteGros3"] == "NULL") {
                    $data["ART_QteGros3"] = null;
                }

                if ($data["ART_PrixUnitaireHTRes"] == "NULL") {
                    $data["ART_PrixUnitaireHTRes"] = null;
                }

                if ($data["ART_PrixUnitaireHTGlobale"] == "NULL") {
                    $data["ART_PrixUnitaireHTGlobale"] = null;
                }

                if ($data["ART_export"] == "NULL") {
                    $data["ART_export"] = null;
                }

                if ($data["PrixSolde"] == "NULL") {
                    $data["PrixSolde"] = null;
                }

                if ($data["TauxSolde"] == "NULL") {
                    $data["TauxSolde"] = null;
                }

                if ($data["FAM_Code"] == "NULL") {
                    $data["FAM_Code"] = null;
                }

                if ($data["ART_Cout_charge"] == "NULL") {
                    $data["ART_Cout_charge"] = null;
                }

                if ($data["CRPonderer"] == "NULL") {
                    $data["CRPonderer"] = null;
                }

                if ($data["QTE_Restante"] == "NULL") {
                    $data["QTE_Restante"] = null;
                }

                if ($data["Coeff_charge"] == "NULL") {
                    $data["Coeff_charge"] = null;
                }

                if ($data["Charge_tot_coeff"] == "NULL") {
                    $data["Charge_tot_coeff"] = null;
                }

                if ($data["is_bloquer"] == "NULL") {
                    $data["is_bloquer"] = null;
                }

                if ($data["Anc_cout"] == "NULL") {
                    $data["Anc_cout"] = null;
                }

                if ($data["is_Tacktil"] == "NULL") {
                    $data["is_Tacktil"] = null;
                }

                if ($data["export"] == "NULL") {
                    $data["export"] = null;
                }

                if ($data["ART_CoulBN"] == "NULL") {
                    $data["ART_CoulBN"] = null;
                }

                if ($data["Regularisation"] == "NULL") {
                    $data["Regularisation"] = null;
                }

                if ($data["ART_codeSerie"] == "NULL") {
                    $data["ART_codeSerie"] = null;
                }

                if ($data["Type_service"] == "NULL") {
                    $data["Type_service"] = null;
                }

                if ($data["Emp_Code"] == "NULL") {
                    $data["Emp_Code"] = null;
                }

                if ($data["Touche_Balance"] == "NULL") {
                    $data["Touche_Balance"] = null;
                }

                if ($data["Type_Balance"] == "NULL") {
                    $data["Type_Balance"] = null;
                }

                if ($data["ART_Fournisseur"] == "NULL") {
                    $data["ART_Fournisseur"] = null;
                }

                if ($data["ART_CodeFrs"] == "NULL") {
                    $data["ART_CodeFrs"] = null;
                }

                if ($data["ART_TVA"] == "NULL") {
                    $data["ART_TVA"] = null;
                }

                if ($data["ART_TAILLE"] == "NULL") {
                    $data["ART_TAILLE"] = null;
                }

                if ($data["ART_Famille"] == "NULL") {
                    $data["ART_Famille"] = null;
                }

                if ($data["ART_Marque"] == "NULL") {
                    $data["ART_Marque"] = null;
                }

                if ($data["ART_Couleur"] == "NULL") {
                    $data["ART_Couleur"] = null;
                }

                if ($data["ART_User"] == "NULL") {
                    $data["ART_User"] = null;
                }

                if ($data["ART_Station"] == "NULL") {
                    $data["ART_Station"] = null;
                }

                if ($data["ART_PrixUnitaireHT"] == "NULL") {
                    $data["ART_PrixUnitaireHT"] = null;
                }

                if ($data["export"] == "NULL") {
                    $data["export"] = null;
                }

                $data["ART_DateCr"] = null;
                $data["ART_DC"] = null;
                $data["ART_DateCr"] = null;
                $data["ddm"] = null;
                $data["ART_ddm"] = null;
                $data["is_Tacktil"] = null;
                $data["ART_export"] = null;
                $data["is_bloquer"] = null;
                $data["ART_Equivalence"] = null;
                if (array_key_exists("COU_Designation", $data)) {
                    unset($data["COU_Designation"]);
                }
                if (array_key_exists("Designation", $data)) {
                    unset($data["Designation"]);
                }
                if (array_key_exists("FAM_Code", $data)) {
                    unset($data["FAM_Code"]);
                }
                if (array_key_exists("FAM_Lib", $data)) {
                    unset($data["FAM_Lib"]);
                }
                if (array_key_exists("Fils_CodeBar", $data)) {
                    unset($data["Fils_CodeBar"]);
                }
                if (array_key_exists("MAR_Designation", $data)) {
                    unset($data["MAR_Designation"]);
                }
                if (array_key_exists("SART_CodeSatation", $data)) {
                    unset($data["SART_CodeSatation"]);
                }
                if (array_key_exists("UNITE_ARTICLE_CodeUnite", $data)) {
                    unset($data["UNITE_ARTICLE_CodeUnite"]);
                }
                //$f = $this->updateOrCreate($connection, $data, 'article', [['ART_Code', '=', $data['ART_Code']]]);
                $exist = $connection->table('article')
                    ->where('ART_Code', '=', $data['ART_Code'])
                    ->get();
                if (is_null($exist) || $exist->isEmpty()) {
                    $f = $connection->table('article')->insert($data);
                    if ($f == false) {
                        $success = false;
                    }
                } else {
                    $f = $connection->table('article')
                        ->where('ART_Code', $data['ART_Code'])
                        ->update([
                            'ART_CodeBar' => $data['ART_CodeBar'],
                            'ART_CodeFrs' => $data['ART_CodeFrs'],
                            'ART_codeSerie' => $data['ART_codeSerie'],
                            'ART_CoulBN' => $data['ART_CoulBN'],
                            'ART_Couleur' => $data['ART_Couleur'],
                            'ART_Cout_charge' => $data['ART_Cout_charge'],
                            'ART_DC' => $data['ART_DC'],
                            'ART_DateCr' => $data['ART_DateCr'],
                            'ART_ddm' => $data['ART_ddm'],
                            'ART_DesgCourte' => $data['ART_DesgCourte'],
                            'ART_Designation' => $data['ART_DesgCourte'],
                            'ART_Equivalence' => $data['ART_Equivalence'],
                            'ART_export' => $data['ART_export'],
                            'ART_Famille' => $data['ART_Famille'],
                            'ART_Fodec' => $data['ART_Fodec'],
                            'ART_Fournisseur' => $data['ART_Fournisseur'],
                            'ART_Image' => $data['ART_Image'],
                            'ART_MargeB' => $data['ART_MargeB'],
                            'ART_Marque' => $data['ART_Marque'],
                            'ART_NumSerie' => $data['ART_NumSerie'],
                            'ART_Poid_Qte' => $data['ART_Poid_Qte'],
                            'ART_Prix_AchatFactReel' => $data['ART_Prix_AchatFactReel'],
                            'ART_PrixGros1' => $data['ART_PrixGros1'],
                            'ART_PrixGros2' => $data['ART_PrixGros2'],
                            'ART_PrixGros3' => $data['ART_PrixGros3'],
                            'ART_PrixUnitaireHTGlobale' => $data['ART_PrixUnitaireHTGlobale'],
                            'ART_PrixUnitaireHTRes' => $data['ART_PrixUnitaireHTRes'],
                            'ART_PrixUnitaireHTVA' => $data['ART_PrixUnitaireHTVA'],
                            'ART_QTEmax' => $data['ART_QTEmax'],
                            'ART_QTEmin' => $data['ART_QTEmin'],
                            'ART_QteDeclaree' => $data['ART_QteDeclaree'],
                            'ART_QteGros3' => $data['ART_QteGros3'],
                            'ART_QtePrixGros1' => $data['ART_QtePrixGros1'],
                            'ART_QtePrixGros2' => $data['ART_QtePrixGros2'],
                            'ART_Station' => $data['ART_Station'],
                            'ART_QteStock' => $data['ART_QteStock'],
                            'ART_TAILLE' => $data['ART_TAILLE'],
                            'ART_TypePrixUnitaireHTVA' => $data['ART_TypePrixUnitaireHTVA'],
                            'ART_User' => $data['ART_User'],
                            'Anc_cout' => $data['Anc_cout'],
                            'CRPonderer' => $data['CRPonderer'],
                            'Charge_tot_coeff' => $data['Charge_tot_coeff'],
                            'Coeff_charge' => $data['Coeff_charge'],
                            'ddm' => $data['ddm'],
                            'Emp_Code' => $data['Emp_Code'],
                            'export' => $data['export'],
                            'is_bloquer' => $data['is_bloquer'],
                            'is_Tacktil' => $data['is_Tacktil'],
                            'PrixSolde' => $data['PrixSolde'],
                            'QTE_Restante' => $data['QTE_Restante'],
                            'Regularisation' => $data['Regularisation'],
                            'TauxSolde' => $data['TauxSolde'],
                            'Touche_Balance' => $data['Touche_Balance'],
                            'Type_Balance' => $data['Type_Balance'],
                            'Type_Produit' => $data['Type_Produit'],
                            'Type_service' => $data['Type_service'],
                            'ART_PrixUnitaireHT' => $data['ART_PrixUnitaireHT'],
                            'ART_TVA' => $data['ART_TVA'],

                        ]);
                    if (!$f) {
                        $success = false;
                    }
                }
                //    }
                //return response()->json($a);
            }
            return response()->json($success);
        } else {
            return response()->json(false);
        }
    }

    /**
     * @OA\Put (
     *     path="/Article/updateArticle",
     *     tags={"Articles Mobile"},
     *     summary="Mettre à jour un article",
     *     description="Met à jour un article existant dans la base de données",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Données de l'article à mettre à jour",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="connexion",
     *                 ref="#/components/schemas/DatabaseConnection"
     *             ),
     *             @OA\Property(
     *                 property="object",
     *                 ref="#/components/schemas/ArticleInput"
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Article modifié avec succès",
     *         @OA\JsonContent(
     *             type="string",
     *             example="Data modified"
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Article non trouvé",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function updateArticle(Request $request)
    {
        $data = $request->json()->all();
        $connection = \App\Helpers\DatabaseConnection::setConnection($data);
        $articleData = $data["object"];
        $connection->table('article')->where('ART_Code', $articleData["ART_Code"])->update($articleData);
        return response()->json("Data modified");
    }

    /**
     * @OA\Delete (
     *     path="/Article/deleteArticle",
     *     tags={"Articles Mobile"},
     *     summary="Supprimer un article",
     *     description="Supprime définitivement un article existant de la base de données",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Code de l'article à supprimer",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="connexion",
     *                 ref="#/components/schemas/DatabaseConnection"
     *             ),
     *             @OA\Property(
     *                 property="object",
     *                 type="object",
     *                 @OA\Property(property="ART_Code", type="string", example="ART001", description="Code de l'article à supprimer")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Article supprimé avec succès",
     *         @OA\JsonContent(
     *             type="string",
     *             example="Data Deleted"
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Article non trouvé",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function deleteArticle(Request $request)
    {
        $data = $request->json()->all();
        $connection = \App\Helpers\DatabaseConnection::setConnection($data);
        $articleData = $data["object"];
        $connection->table('article')->where('ART_Code', $articleData["ART_Code"])->delete();
        return response()->json("Data Deleted");
    }

    /**
     * @OA\Post(
     *     path="/Article/getCodeTest",
     *     tags={"Articles Mobile"},
     *     summary="Récupérer tous les codes-barres d'article",
     *     description="Retourne la liste complète des codes-barres des articles pour tests et vérifications",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Paramètres de connexion à la base de données",
     *         @OA\JsonContent(ref="#/components/schemas/StandardRequest")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Liste des codes-barres récupérée avec succès",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/ArticleCodeBar")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function getCodeTest(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        return response()->json($connection->table('article_code_bar')->get());
    }

    /**
     * @OA\Post(
     *     path="/Article/getCodeBarPagination",
     *     tags={"Articles Mobile"},
     *     summary="Récupérer les codes-barres d'article avec pagination",
     *     description="Retourne la liste paginée des codes-barres des articles avec gestion de la pagination",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Paramètres de connexion et pagination",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="connexion",
     *                 ref="#/components/schemas/DatabaseConnection"
     *             ),
     *             @OA\Property(
     *                 property="object",
     *                 ref="#/components/schemas/PaginationRequest"
     *             )
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="limit",
     *         in="query",
     *         description="Nombre d'éléments par page",
     *         required=false,
     *         @OA\Schema(type="integer", example=15)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Liste paginée des codes-barres récupérée avec succès",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="current_page", type="integer", example=1),
     *             @OA\Property(property="per_page", type="integer", example=15),
     *             @OA\Property(property="total", type="integer", example=500),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(ref="#/components/schemas/ArticleCodeBar")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function getCodeBarPagination(Request $request)
    {
        $data = $request->json()->all();
        $limit = $request->input('limit');
        $connection = DatabaseConnection::setConnection($data);
        $articleCodeBar=$connection->table('article_code_bar')->paginate($limit);

        return response()->json($articleCodeBar);
    }

    /**
     * @OA\Post(
     *     path="/Article/getArticleClientPrix",
     *     tags={"Articles Mobile"},
     *     summary="Récupérer les prix client des articles",
     *     description="Retourne la liste des prix spécifiques par client pour les articles",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Paramètres de connexion à la base de données",
     *         @OA\JsonContent(ref="#/components/schemas/StandardRequest")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Liste des prix client récupérée avec succès",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/ArticleClientPrix")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function getArticleClientPrix(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $result = $connection->select("Set Language French select * from article_client_prix");

        return response()->json($result);
    }

    /**
     * @OA\Post(
     *     path="/Article/getArticleFamille",
     *     tags={"Articles Mobile"},
     *     summary="Récupérer toutes les familles d'articles",
     *     description="Retourne la liste complète des familles d'articles disponibles dans le système",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Paramètres de connexion à la base de données",
     *         @OA\JsonContent(ref="#/components/schemas/StandardRequest")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Liste des familles d'articles récupérée avec succès",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/FamilleArticle")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function getArticleFamille(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $result = $connection->select("Set Language French select * from famille");

        return response()->json($result);
    }

    /**
     * @OA\Post(
     *     path="/Article/get_counts_of_products",
     *     tags={"Articles Mobile"},
     *     summary="Récupérer les compteurs de produits",
     *     description="Retourne les compteurs globaux des produits (total, actifs, en rupture, anomalies)",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Paramètres de connexion à la base de données",
     *         @OA\JsonContent(ref="#/components/schemas/StandardRequest")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Compteurs des produits récupérés avec succès",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="count_products", type="integer", example=1500, description="Nombre total de produits"),
     *             @OA\Property(property="count_products_actif", type="integer", example=1200, description="Nombre de produits actifs"),
     *             @OA\Property(property="count_products_reptures", type="integer", example=50, description="Nombre de produits en rupture"),
     *             @OA\Property(property="count_products_annomalies", type="integer", example=25, description="Nombre de produits avec anomalies")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function get_counts_of_products(Request $request)
    {
        $count_products = $this->getProducts($request)->getData()->count;
        $count_products_actif = $this->getArticleActif($request)->getData()->count;
        $count_products_reptures = $this->getArticleEnRuptureDeStock($request)->getData()->count;
        $count_products_annomalies = $this->getArticleAnomalie($request)->getData()->count;
        $result =
            [
                "count products" => $count_products,
                "count products actifs" => $count_products_actif,
                "count products reptures" => $count_products_reptures,
                "count products annomalies" => $count_products_annomalies

            ];
        return response()->json($result);
    }

    public function get_count_getProducts(Request $request)
    {
        $count_products = $this->getProducts($request)->getData()->count;
        return ["count products" => $count_products];
    }

    public function get_count_getArticleActif(Request $request)
    {
        $count_products = $this->getArticleActif($request)->getData()->count;
        return ["count products actifs" => $count_products];
    }

    public function get_count_getArticleEnRuptureDeStock(Request $request)
    {
        $count_products = $this->getArticleEnRuptureDeStock($request)->getData()->count;
        return ["count products reptures" => $count_products];
    }

    public function get_count_getArticleAnomalie(Request $request)
    {
        $count_products = $this->getArticleAnomalie($request)->getData()->count;
        return ["count products annomalies" => $count_products];
    }

    public function getArticleByChunk(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $limit = $request->input('limit');
        $result = array();
        $connection->table('View_GetArticleNonMouvemente')
            ->orderBy('DDm')
            ->chunk($limit, function ($articles) use (&$result) {
                $result[] = $articles;
            });

        return response()->json($result);
    }

    /**
     * @OA\Post(
     *     path="/Article/getArticleWithPagination",
     *     tags={"Articles Mobile"},
     *     summary="Récupérer les articles avec pagination avancée",
     *     description="Retourne la liste paginée des articles avec gestion des autorisations utilisateur et filtrage par station",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Paramètres de connexion et filtres",
     *         @OA\JsonContent(ref="#/components/schemas/StandardRequest")
     *     ),
     *     @OA\Parameter(
     *         name="station",
     *         in="query",
     *         description="Code de la station pour filtrer",
     *         required=false,
     *         @OA\Schema(type="string", example="ST001")
     *     ),
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Numéro de page",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="limit",
     *         in="query",
     *         description="Nombre d'éléments par page",
     *         required=false,
     *         @OA\Schema(type="integer", example=15)
     *     ),
     *     @OA\Parameter(
     *         name="ddm",
     *         in="query",
     *         description="Date de dernière modification",
     *         required=false,
     *         @OA\Schema(type="string", format="date", example="2024-01-01")
     *     ),
     *     @OA\Parameter(
     *         name="user",
     *         in="header",
     *         description="Code utilisateur pour autorisations",
     *         required=true,
     *         @OA\Schema(type="string", example="USER001")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Liste paginée des articles récupérée avec succès",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="current_page", type="integer", example=1),
     *             @OA\Property(property="per_page", type="integer", example=15),
     *             @OA\Property(property="total", type="integer", example=500),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(ref="#/components/schemas/ArticleDetail")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function getArticleWithPagination(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $article = new article();
        $Article_Site = $connection->table('ville')->pluck('Article_Site')->first();
        $station = $request->input('station');
        $offset = $request->input('page');
        $limit = $request->input('limit');
        $ddm = $request->input('ddm');
        $result = array();
        if (!!Request::createFromGlobals()->hasHeader('user')) {
            $idUser = $request->header('user');
            $autorisation_article_mouvemente = $connection->table('AutorisationUser')
                ->where('AutoCodeUt', '=', $idUser)
                ->where("AutoCodeAu", "=", "820000000")
                ->pluck('AutEtat')
                ->first();
            if (!$station) {

                //article non Mouvementé
                $result = $connection->table('View_GetArticleNonMouvemente')
                    ->leftJoin('dbo.Unite_article', 'View_GetArticleNonMouvemente.ART_Code', '=', 'dbo.Unite_article.UNITE_ARTICLE_CodeArt')
                    ->select('View_GetArticleNonMouvemente.*', 'dbo.Unite_article.UNITE_ARTICLE_PrixVenteTTC') // Sélectionnez uniquement les colonnes nécessaires de la table dbo.Unite_article
                    ->paginate($limit);

            } else {

                $type_user = $connection->table('utilisateur')->where('Code_Ut', '=', $idUser)->pluck('Type_user')->first();


                if (strtolower($type_user) === 'operateur patrimoine') {
                    $articles = array_values($article->getPatrimoine($request, $connection));

                    $resultsCollection = collect($articles);
                    $page = LengthAwarePaginator::resolveCurrentPage();
                    $result = new LengthAwarePaginator(
                        $resultsCollection->forPage($page, $limit)->values(),
                        $resultsCollection->count(),
                        $limit,
                        $page,
                        ['path' => LengthAwarePaginator::resolveCurrentPath()]
                    );

                } else {
                    //Type User Vendeur
                    //Article Site

                    if ((bool)$Article_Site) {
                        $query = $article->articleStation($request, $ddm);
                        //filter by station
                        $query .= " AND (dbo.ArticleStation.STAT_Code = " . $station . ") ";
                        $resultsFromSql = $connection->select($query);
                        $articles = $article->PrixSiteOrPrixPublique($resultsFromSql, $connection, $station);
                        $resultsCollection = collect($articles);
                        $page = LengthAwarePaginator::resolveCurrentPage();
                        $result = new LengthAwarePaginator(
                            $resultsCollection->forPage($page, $limit)->values(),
                            $resultsCollection->count(),
                            $limit,
                            $page,
                            ['path' => LengthAwarePaginator::resolveCurrentPath()]
                        );

                    } else {
                        //Station Article
                        //if ((bool)$autorisation_article_mouvemente) {
                        $query = $article->ArticleMVT() . "AND (LOWER(dbo.article.Type_Produit) != 'patrimoine')";
                        //filter by station
                        $query .= " AND (dbo.StationArticle.SART_CodeSatation = " . $station . ") ";
                        /* if ($ddm) {
                             $query .= "AND dbo.article.ddm >= '" . $ddm . "'";
                         }*/

                        $resultsFromSql = $connection->select($query);
                        $resultsCollection = collect($resultsFromSql);
                        $page = LengthAwarePaginator::resolveCurrentPage();
                        $result = new LengthAwarePaginator(
                            $resultsCollection->forPage($page, $limit)->values(),
                            $resultsCollection->count(),
                            $limit,
                            $page,
                            ['path' => LengthAwarePaginator::resolveCurrentPath()]
                        );


                    }
                }
            }


        }

        return response()->json($result);

    }

}
