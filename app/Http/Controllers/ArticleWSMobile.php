<?php

namespace App\Http\Controllers;

use App\Enums\AuthorizationUser;
use App\Helpers\AppHelper;
use App\Helpers\DatabaseConnection;
use App\Models\article;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

/**
 * @OA\Tag(
 *     name="Articles Mobile",
 *     description="Gestion des articles depuis l'application mobile ProCaisse"
 * )
 */
class ArticleWSMobile extends Controller
{
    public function getArticleByCode(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        return response()->json(
            $connection->table('article')
                ->join('Unite_article', 'Unite_article.UNITE_ARTICLE_CodeArt', '=', 'Article.ART_Code')
                ->where('Unite_article.UNITE_ARTICLE_IsUnitaire', '=', 1)
                ->where('ART_Code', $data["object"])->first()
        );
    }

    /**
     * @OA\Post(
     *     path="/Article/getArticleByBarCode",
     *     tags={"Articles Mobile"},
     *     summary="Rechercher un article par code à barres",
     *     description="Retourne les détails d'un article en recherchant par code à barres ou nom",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Paramètres de connexion et code à barres",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="connexion",
     *                 ref="#/components/schemas/DatabaseConnection"
     *             ),
     *             @OA\Property(
     *                 property="object",
     *                 type="string",
     *                 example="1234567890123",
     *                 description="Code à barres ou nom de l'article à rechercher"
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Article trouvé avec succès",
     *         @OA\JsonContent(ref="#/components/schemas/ArticleDetail")
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Article non trouvé",
     *         @OA\JsonContent(
     *             type="null"
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function getArticleByBarCode(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        $barcode = $data["object"];
        $response = collect($connection->select("Set Language French  SET DATEFORMAT 'ymd' SELECT dbo.article.ART_Code, dbo.article.ART_CodeBar, dbo.article.ART_Designation, ISNULL(dbo.article.ART_PrixUnitaireHT, 0) AS ART_PrixUnitaireHT, dbo.article.ART_TVA,
	ISNULL(dbo.article.ART_QteStock, 0) AS ART_QteStock, ISNULL(dbo.Unite_article.UNITE_ARTICLE_PrixVenteTTC, 0) AS pvttc,
dbo.article.photo_Path, dbo.marque.MAR_Designation,
	dbo.article.PrixSolde, ISNULL(dbo.article.TauxSolde, 0) AS TauxSolde, dbo.famille.FAM_Lib
        FROM   dbo.article LEFT OUTER JOIN
	dbo.famille ON dbo.article.ART_Famille = dbo.famille.FAM_Code LEFT OUTER JOIN
	dbo.marque ON dbo.article.ART_Marque = dbo.marque.MAR_Code LEFT OUTER JOIN
	dbo.Unite_article ON dbo.article.ART_Code = dbo.Unite_article.UNITE_ARTICLE_CodeArt
WHERE (dbo.Unite_article.UNITE_ARTICLE_IsUnitaire = 'True') AND (ISNULL(dbo.article.is_bloquer, 'False') <> 'True') AND ((dbo.article.ART_CodeBar like '%$barcode%') or (dbo.article.ART_Designation  like '%$barcode%'))"))->first();
        return response()->json($response);

    }

    public function getArticleByName(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        $name = $data["object"];
        return response()->json(collect($connection->select("
			Set Language French  SET DATEFORMAT 'ymd' SELECT     dbo.article.ART_Code, dbo.article.ART_CodeBar,dbo.article.ART_Designation, ISNULL(dbo.article.ART_PrixUnitaireHT, 0) AS ART_PrixUnitaireHT, dbo.article.ART_TVA, dbo.article.ART_CodeFrs, dbo.article.ART_CodeFrs2,
				ISNULL(dbo.article.ART_QteStock, 0) AS ART_QteStock, ISNULL(dbo.Unite_article.UNITE_ARTICLE_PrixVenteTTC, 0) AS pvttc, dbo.marque.MAR_Designation, dbo.article.PrixSolde, ISNULL(dbo.article.TauxSolde,
				0) AS TauxSolde, dbo.famille.FAM_Lib, dbo.famille.FAM_Code, dbo.marque.MAR_Code, dbo.Unite_article.UNITE_ARTICLE_QtePiece, dbo.Unite_article.UNITE_ARTICLE_CodeUnite, dbo.couleur.COU_Designation,
				dbo.article.ART_TAILLE,dbo.article_code_bar.Fils_CodeBar,Art_MaxTRemise ,
dbo.article.photo_Path,
                dbo.fournisseur.FRS_Nomf AS fournissuer
  
		FROM         dbo.article LEFT OUTER JOIN
				dbo.couleur ON dbo.article.ART_Couleur = dbo.couleur.COU_Code LEFT OUTER JOIN
				dbo.famille ON dbo.article.ART_Famille = dbo.famille.FAM_Code LEFT OUTER JOIN
				dbo.fournisseur ON dbo.article.ART_Fournisseur = dbo.fournisseur.FRS_codef LEFT OUTER JOIN
				dbo.marque ON dbo.article.ART_Marque = dbo.marque.MAR_Code LEFT OUTER JOIN
				dbo.Unite_article ON dbo.article.ART_Code = dbo.Unite_article.UNITE_ARTICLE_CodeArt   JOIN
				dbo.article_code_bar ON dbo.article.ART_CodeBar = dbo.article_code_bar.Parent_CodeBar
	 	WHERE (dbo.Unite_article.UNITE_ARTICLE_IsUnitaire = 'True')
		AND (ISNULL(dbo.article.is_bloquer, 'False') <> 'True')
		AND (dbo.article.ART_CodeBar like '%$name%'
		or dbo.article.ART_Designation  like '%$name%'
		or dbo.fournisseur.FRS_Nomf  like '%$name%')
        ")));
    }

    public function getCountArticle(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $count = $connection->table('View_GetArticleNonMouvemente')->count();
        return response()->json(["countArticle" => $count]);
    }

    public function getArticle(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $article = new article();
        $Article_Site = $connection->table('ville')->pluck('Article_Site')->first();
        $station = $request->input('station');
        $limit = $request->input('limit');
        $ddm = $request->input('ddm');
        $result = array();
        if (!!Request::createFromGlobals()->hasHeader('user')) {
            $idUser = $request->header('user');
            $authorizationProductMovement = $connection->table('AutorisationUser')
                ->where('AutoCodeUt', '=', $idUser)
                ->where("AutoCodeAu", "=", AuthorizationUser::authorizationProductMovement)
                ->pluck('AutEtat')
                ->first();
            if (!$station) {

                //article non Mouvementé
                $result = $connection->table('View_GetArticleNonMouvemente')
                    ->leftJoin('dbo.Unite_article', 'View_GetArticleNonMouvemente.ART_Code', '=', 'dbo.Unite_article.UNITE_ARTICLE_CodeArt')
                    ->select('View_GetArticleNonMouvemente.*', 'dbo.Unite_article.UNITE_ARTICLE_PrixVenteTTC') // Sélectionnez uniquement les colonnes nécessaires de la table dbo.Unite_article
                    ->paginate($limit);

            } else {
                $type_user = $connection->table('utilisateur')
                    ->where('Code_Ut', '=', $idUser)
                    ->pluck('Type_user')->first();

                if (strtolower($type_user) === 'operateur patrimoine') {
                    $result = array_values($article->getPatrimoine($request, $connection));
                } else {
                    //Type User Vendeur
                    //Article Site
                    if ((bool)$Article_Site) {
                        $query = $article->articleStation($request, $ddm);
                        //filter by station
                        $query .= " AND (dbo.ArticleStation.STAT_Code = " . $station . ") ";
                        $result = $connection->select($query);
                        $result = $article->PrixSiteOrPrixPublique($result, $connection, $station);

                    } else {
                        //Station Article
                        $query = $article->ArticleMVT() . "AND (LOWER(dbo.article.Type_Produit) != 'patrimoine')";

                        //filter by station
                        $query .= " AND (dbo.StationArticle.SART_CodeSatation = " . $station . ") ";
                        /* if ($ddm) {
                            $query .= "AND dbo.article.ddm >= '" . $ddm . "'";
                        }*/
                        $result = $connection->select($query);

                    }
                }
            }
        }

        return response()->json($result);
    }

    public function getArticles(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('article_Mobile')->get());
    }

    public function getArticlesByUser(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json(collect($connection->select("Set Language French  SET DATEFORMAT 'ymd' SELECT dbo.article.ART_Code, dbo.article.ART_CodeBar, dbo.article.photo_Path, dbo.article.ART_Designation, ISNULL(dbo.article.ART_PrixUnitaireHT, 0) AS ART_PrixUnitaireHT, dbo.article.ART_TVA, dbo.article.ART_CodeFrs,
                         dbo.article.ART_CodeFrs2,
						 ISNULL(dbo.article.ART_QteStock, 0) AS ART_QteStock,
						 ISNULL(dbo.Unite_article.UNITE_ARTICLE_PrixVenteTTC, 0) AS pvttc,
						 isnull(dbo.article.ART_PrixGros1,0) AS prixGros1,
						 isnull(dbo.article.ART_PrixGros2,0) AS prixGros2,
						 isnull(dbo.article.ART_PrixGros3,0) AS prixGros3,
						 ISNULL(dbo.Unite_article.UNITE_ARTICLE_PrixVenteTTC, 0) AS ART_PrixPublique,
						 dbo.marque.MAR_Designation, dbo.article.PrixSolde,
                         ISNULL(dbo.article.TauxSolde, 0) AS TauxSolde,
						 dbo.famille.FAM_Lib, dbo.famille.FAM_Code, dbo.marque.MAR_Code, dbo.marque.photo_PathM as photo_Path_Marque,
                        dbo.station.STAT_Desg, dbo.station.STAT_Code, dbo.station.Code_Ut1,
                         dbo.ArticleClient.Code_Meti,dbo.Unite_article.UNITE_ARTICLE_QtePiece,
        dbo.Unite_article.UNITE_ARTICLE_CodeUnite,dbo.ArticleClient.CLI_Code

        FROM            dbo.article INNER JOIN
                         dbo.ArticleStation ON dbo.article.ART_Code = dbo.ArticleStation.ART_Code INNER JOIN
                         dbo.station ON dbo.ArticleStation.STAT_Code = dbo.station.STAT_Code INNER JOIN
                         dbo.ArticleClient ON dbo.article.ART_Code = dbo.ArticleClient.ART_Code LEFT OUTER JOIN
                         dbo.famille ON dbo.article.ART_Famille = dbo.famille.FAM_Code LEFT OUTER JOIN
                         dbo.marque ON dbo.article.ART_Marque = dbo.marque.MAR_Code LEFT OUTER JOIN
                         dbo.Unite_article ON dbo.article.ART_Code = dbo.Unite_article.UNITE_ARTICLE_CodeArt
        WHERE        (dbo.Unite_article.UNITE_ARTICLE_IsUnitaire = 'True') AND (ISNULL(dbo.article.is_bloquer, 'False') <> 'True') AND (dbo.station.Code_Ut1 = '" . $data["object"] . "')")));
    }

    public function getArticleByStation(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        $codeSatation = $data["object"];

        return response()->json(collect($connection->select(" Set Language French  SET DATEFORMAT 'ymd' SELECT  dbo.article.ART_Code, dbo.article.ART_CodeBar ,
	(select top 1 Fils_CodeBar
	 from article_code_bar
	 where dbo.article.ART_CodeBar=article_code_bar.Parent_CodeBar
	 and article_code_bar.Parent_CodeBar
	 not like article_code_bar.Fils_CodeBar) as Fils_CodeBar,
              dbo.article.ART_Designation,
              ISNULL(dbo.article.ART_PrixUnitaireHT, 0) AS ART_PrixUnitaireHT,
              dbo.article.ART_TVA,
              ISNULL(dbo.article.ART_QteStock, 0) AS ART_QteStock,
              isnull(dbo.article.ART_PrixGros1,0) AS prixGros1,
              isnull(dbo.article.ART_PrixGros2,0) AS prixGros2,
              isnull(dbo.article.ART_PrixGros3,0) AS prixGros3, 
		dbo.article.photo_Path,
              ISNULL(dbo.Unite_article.UNITE_ARTICLE_PrixVenteTTC, 0) AS ART_PrixPublique,
              isnull(dbo.article.PrixSolde,0) AS pvttc,
              dbo.marque.MAR_Designation,
              dbo.article.PrixSolde, ISNULL(dbo.article.TauxSolde, 0) AS TauxSolde,
              dbo.famille.FAM_Lib,
              dbo.StationArticle.SART_Qte,
              dbo.StationArticle.SART_CodeSatation,
              dbo.famille.FAM_Code,
              dbo.Unite_article.UNITE_ARTICLE_QtePiece,
              dbo.Unite_article.UNITE_ARTICLE_CodeUnite,
              Art_MaxTRemise
    FROM dbo.article
     INNER JOIN dbo.StationArticle 
     ON dbo.article.ART_Code = dbo.StationArticle.SART_CodeArt 
     LEFT OUTER JOIN dbo.famille 
     ON dbo.article.ART_Famille = dbo.famille.FAM_Code
     LEFT OUTER JOIN dbo.marque 
     ON dbo.article.ART_Marque = dbo.marque.MAR_Code
     LEFT OUTER JOIN dbo.Unite_article 
     ON dbo.article.ART_Code = dbo.Unite_article.UNITE_ARTICLE_CodeArt
       WHERE      (ISNULL(dbo.article.is_bloquer, N'False') <> 'True') AND (dbo.StationArticle.SART_CodeSatation = '$codeSatation') $request->additionalQuery")));
    }


    public function getArticleByX(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('article')->where($request->field, $request->value)->get());
    }

    public function addArticle(Request $request)
    {
        $data = $request->json()->all();

        if (!empty($data)) {
            return response()->json(true);
        } else {
            return response()->json(false);
        }
    }

    public function addArticleMobile(Request $request)
    {
        $items = $request->json()->all();
        $connection = DatabaseConnection::setConnection($items);
        $success = true;

        if (!empty($items)) {
            foreach ($items["object"]["articles"] as $data) {
                unset($data["UNITE_ARTICLE_QtePiece"]);
                unset($data["count"]);
                unset($data["prix"]);
                unset($data["pvttc"]);
                unset($data["remise"]);
                unset($data["SART_Qte"]);

                if ($data["ART_Image"] == "NULL") {
                    $data["ART_Image"] = null;
                }

                if ($data["ART_export"] == "NULL") {
                    $data["ART_export"] = null;
                }

                if ($data["ART_MargeB"] == "NULL") {
                    $data["ART_MargeB"] = null;
                }

                if ($data["ART_Poid_Qte"] == "NULL") {
                    $data["ART_Poid_Qte"] = null;
                }

                if ($data["ART_DateCr"] == "NULL") {
                    $data["ART_DateCr"] = null;
                }

                if ($data["ART_DC"] == "NULL") {
                    $data["ART_DC"] = null;
                }

                if ($data["ddm"] == "NULL") {
                    $data["ddm"] = null;
                }

                if ($data["ART_ddm"] == "NULL") {
                    $data["ART_ddm"] = null;
                }

                if ($data["ART_QteStock"] == "NULL") {
                    $data["ART_QteStock"] = null;
                }

                if ($data["ART_PrixUnitaireHT"] == "NULL") {
                    $data["ART_PrixUnitaireHT"] = null;
                }

                if ($data["ART_PrixUnitaireHTVA"] == "NULL") {
                    $data["ART_PrixUnitaireHTVA"] = null;
                }

                if ($data["ART_TVA"] == "NULL") {
                    $data["ART_TVA"] = null;
                }

                if ($data["ART_QTEmin"] == "NULL") {
                    $data["ART_QTEmin"] = null;
                }

                if ($data["ART_Fodec"] == "NULL") {
                    $data["ART_Fodec"] = null;
                }

                if ($data["ART_DC"] == "NULL") {
                    $data["ART_DC"] = null;
                }

                if ($data["ART_QTEmax"] == "NULL") {
                    $data["ART_QTEmax"] = null;
                }

                if ($data["ART_QteDeclaree"] == "NULL") {
                    $data["ART_QteDeclaree"] = null;
                }

                if ($data["ART_Prix_AchatFactReel"] == "NULL") {
                    $data["ART_Prix_AchatFactReel"] = null;
                }

                if ($data["ART_PrixGros1"] == "NULL") {
                    $data["ART_PrixGros1"] = null;
                }

                if ($data["ART_QtePrixGros1"] == "NULL") {
                    $data["ART_QtePrixGros1"] = null;
                }

                if ($data["ART_PrixGros2"] == "NULL") {
                    $data["ART_PrixGros2"] = null;
                }

                if ($data["ART_QtePrixGros2"] == "NULL") {
                    $data["ART_QtePrixGros2"] = null;
                }

                if ($data["ART_PrixGros3"] == "NULL") {
                    $data["ART_PrixGros3"] = null;
                }

                if ($data["ART_QteGros3"] == "NULL") {
                    $data["ART_QteGros3"] = null;
                }

                if ($data["ART_PrixUnitaireHTRes"] == "NULL") {
                    $data["ART_PrixUnitaireHTRes"] = null;
                }

                if ($data["ART_PrixUnitaireHTGlobale"] == "NULL") {
                    $data["ART_PrixUnitaireHTGlobale"] = null;
                }

                if ($data["ART_export"] == "NULL") {
                    $data["ART_export"] = null;
                }

                if ($data["PrixSolde"] == "NULL") {
                    $data["PrixSolde"] = null;
                }

                if ($data["TauxSolde"] == "NULL") {
                    $data["TauxSolde"] = null;
                }

                if ($data["FAM_Code"] == "NULL") {
                    $data["FAM_Code"] = null;
                }

                if ($data["ART_Cout_charge"] == "NULL") {
                    $data["ART_Cout_charge"] = null;
                }

                if ($data["CRPonderer"] == "NULL") {
                    $data["CRPonderer"] = null;
                }

                if ($data["QTE_Restante"] == "NULL") {
                    $data["QTE_Restante"] = null;
                }

                if ($data["Coeff_charge"] == "NULL") {
                    $data["Coeff_charge"] = null;
                }

                if ($data["Charge_tot_coeff"] == "NULL") {
                    $data["Charge_tot_coeff"] = null;
                }

                if ($data["is_bloquer"] == "NULL") {
                    $data["is_bloquer"] = null;
                }

                if ($data["Anc_cout"] == "NULL") {
                    $data["Anc_cout"] = null;
                }

                if ($data["is_Tacktil"] == "NULL") {
                    $data["is_Tacktil"] = null;
                }

                if ($data["export"] == "NULL") {
                    $data["export"] = null;
                }

                if ($data["ART_CoulBN"] == "NULL") {
                    $data["ART_CoulBN"] = null;
                }

                if ($data["Regularisation"] == "NULL") {
                    $data["Regularisation"] = null;
                }

                if ($data["ART_codeSerie"] == "NULL") {
                    $data["ART_codeSerie"] = null;
                }

                if ($data["Type_service"] == "NULL") {
                    $data["Type_service"] = null;
                }

                if ($data["Emp_Code"] == "NULL") {
                    $data["Emp_Code"] = null;
                }

                if ($data["Touche_Balance"] == "NULL") {
                    $data["Touche_Balance"] = null;
                }

                if ($data["Type_Balance"] == "NULL") {
                    $data["Type_Balance"] = null;
                }

                if ($data["ART_Fournisseur"] == "NULL") {
                    $data["ART_Fournisseur"] = null;
                }

                if ($data["ART_CodeFrs"] == "NULL") {
                    $data["ART_CodeFrs"] = null;
                }

                if ($data["ART_TVA"] == "NULL") {
                    $data["ART_TVA"] = null;
                }

                if ($data["ART_TAILLE"] == "NULL") {
                    $data["ART_TAILLE"] = null;
                }

                if ($data["ART_Famille"] == "NULL") {
                    $data["ART_Famille"] = null;
                }

                if ($data["ART_Marque"] == "NULL") {
                    $data["ART_Marque"] = null;
                }

                if ($data["ART_Couleur"] == "NULL") {
                    $data["ART_Couleur"] = null;
                }

                if ($data["ART_User"] == "NULL") {
                    $data["ART_User"] = null;
                }

                if ($data["ART_Station"] == "NULL") {
                    $data["ART_Station"] = null;
                }

                if ($data["ART_PrixUnitaireHT"] == "NULL") {
                    $data["ART_PrixUnitaireHT"] = null;
                }

                if ($data["export"] == "NULL") {
                    $data["export"] = null;
                }

                $data["ART_DateCr"] = null;
                $data["ART_DC"] = null;
                $data["ART_DateCr"] = null;
                $data["ddm"] = null;
                $data["ART_ddm"] = null;
                $data["is_Tacktil"] = null;
                $data["ART_export"] = null;
                $data["is_bloquer"] = null;
                $data["ART_Equivalence"] = null;
                if (array_key_exists("COU_Designation", $data)) {
                    unset($data["COU_Designation"]);
                }
                if (array_key_exists("Designation", $data)) {
                    unset($data["Designation"]);
                }
                if (array_key_exists("FAM_Code", $data)) {
                    unset($data["FAM_Code"]);
                }
                if (array_key_exists("FAM_Lib", $data)) {
                    unset($data["FAM_Lib"]);
                }
                if (array_key_exists("Fils_CodeBar", $data)) {
                    unset($data["Fils_CodeBar"]);
                }
                if (array_key_exists("MAR_Designation", $data)) {
                    unset($data["MAR_Designation"]);
                }
                if (array_key_exists("SART_CodeSatation", $data)) {
                    unset($data["SART_CodeSatation"]);
                }
                if (array_key_exists("UNITE_ARTICLE_CodeUnite", $data)) {
                    unset($data["UNITE_ARTICLE_CodeUnite"]);
                }
                //$f = $this->updateOrCreate($connection, $data, 'article', [['ART_Code', '=', $data['ART_Code']]]);
                $exist = $connection->table('article')
                    ->where('ART_Code', '=', $data['ART_Code'])
                    ->get();
                if (is_null($exist) || $exist->isEmpty()) {
                    $f = $connection->table('article')->insert($data);
                    if ($f == false) {
                        $success = false;
                    }
                } else {
                    $f = $connection->table('article')
                        ->where('ART_Code', $data['ART_Code'])
                        ->update([
                            'ART_CodeBar' => $data['ART_CodeBar'],
                            'ART_CodeFrs' => $data['ART_CodeFrs'],
                            'ART_codeSerie' => $data['ART_codeSerie'],
                            'ART_CoulBN' => $data['ART_CoulBN'],
                            'ART_Couleur' => $data['ART_Couleur'],
                            'ART_Cout_charge' => $data['ART_Cout_charge'],
                            'ART_DC' => $data['ART_DC'],
                            'ART_DateCr' => $data['ART_DateCr'],
                            'ART_ddm' => $data['ART_ddm'],
                            'ART_DesgCourte' => $data['ART_DesgCourte'],
                            'ART_Designation' => $data['ART_DesgCourte'],
                            'ART_Equivalence' => $data['ART_Equivalence'],
                            'ART_export' => $data['ART_export'],
                            'ART_Famille' => $data['ART_Famille'],
                            'ART_Fodec' => $data['ART_Fodec'],
                            'ART_Fournisseur' => $data['ART_Fournisseur'],
                            'ART_Image' => $data['ART_Image'],
                            'ART_MargeB' => $data['ART_MargeB'],
                            'ART_Marque' => $data['ART_Marque'],
                            'ART_NumSerie' => $data['ART_NumSerie'],
                            'ART_Poid_Qte' => $data['ART_Poid_Qte'],
                            'ART_Prix_AchatFactReel' => $data['ART_Prix_AchatFactReel'],
                            'ART_PrixGros1' => $data['ART_PrixGros1'],
                            'ART_PrixGros2' => $data['ART_PrixGros2'],
                            'ART_PrixGros3' => $data['ART_PrixGros3'],
                            'ART_PrixUnitaireHTGlobale' => $data['ART_PrixUnitaireHTGlobale'],
                            'ART_PrixUnitaireHTRes' => $data['ART_PrixUnitaireHTRes'],
                            'ART_PrixUnitaireHTVA' => $data['ART_PrixUnitaireHTVA'],
                            'ART_QTEmax' => $data['ART_QTEmax'],
                            'ART_QTEmin' => $data['ART_QTEmin'],
                            'ART_QteDeclaree' => $data['ART_QteDeclaree'],
                            'ART_QteGros3' => $data['ART_QteGros3'],
                            'ART_QtePrixGros1' => $data['ART_QtePrixGros1'],
                            'ART_QtePrixGros2' => $data['ART_QtePrixGros2'],
                            'ART_Station' => $data['ART_Station'],
                            'ART_QteStock' => $data['ART_QteStock'],
                            'ART_TAILLE' => $data['ART_TAILLE'],
                            'ART_TypePrixUnitaireHTVA' => $data['ART_TypePrixUnitaireHTVA'],
                            'ART_User' => $data['ART_User'],
                            'Anc_cout' => $data['Anc_cout'],
                            'CRPonderer' => $data['CRPonderer'],
                            'Charge_tot_coeff' => $data['Charge_tot_coeff'],
                            'Coeff_charge' => $data['Coeff_charge'],
                            'ddm' => $data['ddm'],
                            'Emp_Code' => $data['Emp_Code'],
                            'export' => $data['export'],
                            'is_bloquer' => $data['is_bloquer'],
                            'is_Tacktil' => $data['is_Tacktil'],
                            'PrixSolde' => $data['PrixSolde'],
                            'QTE_Restante' => $data['QTE_Restante'],
                            'Regularisation' => $data['Regularisation'],
                            'TauxSolde' => $data['TauxSolde'],
                            'Touche_Balance' => $data['Touche_Balance'],
                            'Type_Balance' => $data['Type_Balance'],
                            'Type_Produit' => $data['Type_Produit'],
                            'Type_service' => $data['Type_service'],
                            'ART_PrixUnitaireHT' => $data['ART_PrixUnitaireHT'],
                            'ART_TVA' => $data['ART_TVA'],

                        ]);
                    if (!$f) {
                        $success = false;
                    }
                }
                //    }
                //return response()->json($a);
            }
            return response()->json($success);
        } else {
            return response()->json(false);
        }
    }

    public function updateArticle(Request $request)
    {
        $data = $request->json()->all();

        DB::table('article')->where('ART_Code', $data["ART_Code"])->update($data);

        return ("Data modified");
    }

    public function deleteArticle(Request $request)
    {
        DB::table('article')->where('ART_Code', $request->id)->delete();
        return ("Data Deleted");
    }

    public function getCodeTest(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        return response()->json($connection->table('article_code_bar')->get());
    }

    public function getCodeBarPagination(Request $request)
    {
        $data = $request->json()->all();
        $limit = $request->input('limit');
        $connection = DatabaseConnection::setConnection($data);
        $articleCodeBar=$connection->table('article_code_bar')->paginate($limit);

        return response()->json($articleCodeBar);
    }

    public function updateOrCreate($connection, $data, $table, $whereClauses)
    {
        $exists = $connection->table($table)->where($whereClauses)->first();

        if ($exists) {
            $connection->table($table)->where($whereClauses)->delete();
        }

        return $connection->table($table)->insert($data);
    }

    public function ArticleWithTransaction(Request $request)
    {
        Storage::append('articlesTransaction.txt', $request);
        $date = date('Y-m-d H:i:s');
        $data = $request->json()->all();
        $data1 = $request->json()->all();
        $object = $data["object"];
        $articles = $object['articles'];
        $articleCodeBars = $object['articleCodeBars'];
        $tailles = $object['tailles'];
        $couleurs = $object['couleurs'];
        $articleClasseRemises = $object['articleClasseRemises'];
        $classeRemises = $object['classeRemises'];
        $familles = $object['familles'];
        $marques = $object['marques'];
        $unites = $object['unites'];
        $uniteArticles = $object['uniteArticles'];

        $connection = DatabaseConnection::setConnection($data);

        $insertTaille = $this->addTailleMobile($request);
        $insertUnite = app('App\Http\Controllers\UniteController')->addUniteMobile($request);
        $insertClasseRemise = app('App\Http\Controllers\ClasseRemiseController')->addClasseRemiseMobile($request);
        $insertFamille = app('App\Http\Controllers\FamilleWSMobile')->addFamilleMobile($request);

        $insertMarque = $this->addMarqueMobile($request);
        $insertCouleur = app('App\Http\Controllers\CouleurController')->addcouleurMobile($request);
        $insertArticle = $this->addArticleMobile($request);
        $insertArticleClasseRemise = app('App\Http\Controllers\ArticleClasseRemiseController')->addArticleClasseRemiseMobile($request);
        $insertUniteArticle = app('App\Http\Controllers\UniteArticleController')->addUniteArticleMobile($request);
        $insertArticleCodeBar = app('App\Http\Controllers\ArticleCodeBarWSMobile')->addArticleCodeBarMobile($request);

        if (!$insertTaille || !$insertUnite || !$insertClasseRemise || !$insertFamille || !$insertMarque || !$insertCouleur || !$insertArticle || !$insertArticleClasseRemise || !$insertUniteArticle || !$insertArticleCodeBar) { //if( !$insertTaille || !$insertUnite )
            $connection->rollback();
            return response()->json(false);
        } else {
            $connection->commit();

            return response()->json(true);
        }
    }

    public function addTailleMobile(Request $request)
    {
        $items = $request->json()->all();

        $connection = DatabaseConnection::setConnection($items);
        $f = null;
        if (!empty($items)) {
            // $f = $connection->table('famille')->insert($data);
            foreach ($items["object"]['tailles'] as $data) {
                if ($data["DDm"] == "NULL") {
                    $data["DDm"] = null;
                }

                if ($data["export"] == "NULL") {
                    $data["export"] = null;
                }

                if ($data["T_export"] == "NULL") {
                    $data["T_export"] = null;
                }

                if ($data["T_DDm"] == "NULL") {
                    $data["T_DDm"] = null;
                }

                $data["T_DDm"] = null;
                $exist = $connection->table('Taille')
                    ->where('TAI_Taille', '=', $data['TAI_Taille'])
                    ->get();
                if (is_null($exist) || $exist->isEmpty()) {
                    $f = $this->updateOrCreate($connection, $data, 'Taille', [['TAI_Taille', '=', $data['TAI_Taille']]]);
                } else {
                    $f = $connection->table('Taille')
                        ->where('TAI_Taille', $data['TAI_Taille'])
                        ->update([
                            'DDm' => $data['DDm'],
                            'export' => $data['export'],
                            'TAI_Station' => $data['TAI_Station'],
                            'TAI_User' => $data['TAI_User'],
                            'T_user' => $data['T_user'],
                            'T_station' => $data['T_station'],
                        ]);
                }

            }

            return response()->json($f);
        } else {
            return (false);
        }
    }

    public function addMarqueMobile(Request $request)
    {
        $items = $request->json()->all();

        $connection = DatabaseConnection::setConnection($items);

        if (!empty($items)) {
            // $f = $connection->table('marque_Mobile')->insert($data);
            foreach ($items["object"]["marques"] as $data) {
                if ($data["MAR_export"] == "NULL") {
                    $data["MAR_export"] = null;
                }

                if ($data["MAR_DDm"] == "NULL") {
                    $data["MAR_DDm"] = null;
                }

                $exist = $connection->table('marque')
                    ->where('MAR_Code', '=', $data['MAR_Code'])
                    ->get();
                if (is_null($exist) || $exist->isEmpty()) {
                    $f = $connection->table('marque')->insert($data);
                } else {
                    $f = $connection->table('marque')
                        ->where('MAR_Code', $data['MAR_Code'])
                        ->update([
                            'MAR_DDm' => $data['MAR_DDm'],
                            'MAR_Designation' => $data['MAR_Designation'],
                            'MAR_export' => $data['MAR_export'],
                            'MAR_Station' => $data['MAR_Station'],
                            'MAR_User' => $data['MAR_User'],
                        ]);
                }
            }
            return response()->json($f);
        } else {
            return (false);
        }
    }


    public function addArticleMobileV2(Request $request)
    {

        $items = $request->json()->all();

        if (!empty ($items)) {
            $connection = DatabaseConnection::setConnection($items);
            $result = [];
            foreach ($items["object"] as $data) {
                $connection->beginTransaction();
                try {

                    //InsertorUpdate Table article
                    $exist = $connection->table('article')
                        ->where("ART_Code", $data["ART_Code"])
                        ->get();
                    $existCodeAndDate = $connection->table('article')
                        ->where("ART_Code", $data["ART_Code"])
                        ->where("ddm", $data["ddm"])
                        ->get();


                    if (!is_null($data["ART_DateCr"])) {
                        $data["ART_DateCr"] = AppHelper::setDateFormat($data["ART_DateCr"]);
                    }
                    if (!is_null($data["ddm"])) {
                        $data["ddm"] = AppHelper::setDateFormat($data["ddm"]);
                    }
                    $qte = $data["ART_QteStock"];
                    unset($data["ART_QteStock"]);
                    unset($data["FAM_Lib"]);
                    unset($data["SART_Qte"]);
                    unset($data["FAM_Code"]);
                    unset($data["UNITE_ARTICLE_CodeUnite"]);
                    unset($data["MAR_Designation"]);
                    if ($exist == null || empty($exist) || count($exist) <= 0) {
                        $connection->table('article')->insert($data);
                        // Insert  Article_classe_remise
                        $connection->table('Article_classe_remise')
                            ->insert([
                                'ART_CLASS_REM_Code' => 0,
                                'ART_CLASS_REM_ArtCode' => $data["ART_Code"],
                                'ART_CLASS_REM_export' => 'false'
                            ]);
                        // Insert article_code_bar
                        $connection->table('article_code_bar')
                            ->insert([
                                'Parent_CodeBar' => $data["ART_CodeBar"],
                                'Fils_CodeBar' => $data["ART_CodeBar"],
                            ]);
                        //Insert Unite_article
                        $connection->table('Unite_article')
                            ->insert([
                                'UNITE_ARTICLE_CodeUnite' => 'Pièce',
                                'UNITE_ARTICLE_CodeArt' => $data["ART_Code"],
                                'UNITE_ARTICLE_PrixVenteTTC' => $data['ART_EPrixTTC']
                            ]);

                    } else if ($existCodeAndDate == null || empty($existCodeAndDate) || count($existCodeAndDate) <= 0) {
                        $connection->table('article')->where("ART_Code", $data["ART_Code"])
                            ->update($data);
                        //  Update Article_classe_remise
                        $connection->table('Article_classe_remise')->where("ART_CLASS_REM_Code", 0)
                            ->where("ART_CLASS_REM_ArtCode", $data["ART_Code"])
                            ->update([
                                'ART_CLASS_REM_Code' => 0,
                                'ART_CLASS_REM_ArtCode' => $data["ART_Code"],
                                'ART_CLASS_REM_export' => 'false'
                            ]);
                        // Update article_code_bar
                        $connection->table('article_code_bar')->where("Parent_CodeBar", $data["ART_CodeBar"])
                            ->where("Fils_CodeBar", $data["ART_CodeBar"])
                            ->update([
                                'Parent_CodeBar' => $data["ART_CodeBar"],
                                'Fils_CodeBar' => $data["ART_CodeBar"],
                            ]);
                        // Update Unite_article
                        $connection->table('Unite_article')->where("UNITE_ARTICLE_CodeUnite", 'Pièce')
                            ->where("UNITE_ARTICLE_CodeArt", $data["ART_Code"])
                            ->update([
                                'UNITE_ARTICLE_CodeUnite' => 'Pièce',
                                'UNITE_ARTICLE_CodeArt' => $data["ART_Code"],
                                'UNITE_ARTICLE_PrixVenteTTC' => $data['ART_EPrixTTC']
                            ]);
                    }

                    //InsertorUpdate in table StationArticle
                    $exist = $connection->table('StationArticle')
                        ->where(
                            'SART_CodeArt', '=', $data["ART_Code"])
                        ->where('SART_CodeSatation', '=', $data["ART_Station"])->first();

                    !!$exist ?
                        $connection->table('StationArticle')->where("SART_CodeArt", $data["ART_Code"])
                            ->update([
                                'SART_CodeArt' => $data["ART_Code"]
                            ]) :
                        $connection->table('StationArticle')
                            ->insert([
                                'SART_CodeArt' => $data["ART_Code"],
                                'SART_CodeSatation' => $data["ART_Station"],
                                'SART_Qte' => $qte,
                                'SART_QteDeclaree' => $data["ART_QteDeclaree"],
                                'SART_QTEmin' => $data["ART_QTEmin"],
                                'SART_QTEmax' => $data["ART_QTEmax"],
                                'SART_export' => $data["ART_export"],
                            ]);

                    $connection->commit();
                    $result[] = $data;

                } catch (\Exception $e) {
                    //   app('sentry')->captureException($e);
                    $connection->rollback();
                    //return  $e->getMessage();
                }

            }
            return response()->json($result);
        } else {
            return response()->json(null);
        }


    }

    public function getArticleEnRuptureDeStock(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $famille = $request->input('famille');
        $offset = $request->input('offset');
        $limit = $request->input('limit');
        $station = $request->input('station');
        $filterDesignation = $request->input('filterDesignation');
        $query = $connection->table('Rpt_View_Articlrupture')
            ->join("StationArticle", 'Rpt_View_Articlrupture.ART_Code', '=', 'StationArticle.SART_CodeArt')
            ->join("station", 'StationArticle.SART_CodeSatation', '=', 'station.STAT_Code')
            ->where(function ($query) {
                $query->where('StationArticle.SART_Qte', '<=', 0)
                    ->orWhere("StationArticle.SART_Qte", '<=', DB::raw('CAST(Rpt_View_Articlrupture.ART_QTEmin AS nvarchar)'));

            });
        if (!!$station) {
            $query->where('STAT_Code', '=', $station);
            if (!!$famille) {
                $query->where('ART_Famille', '=', $famille);
                if (!!$filterDesignation) {
                    $query->where('ART_Designation', 'LIKE', '%' . $filterDesignation . '%');
                }
            } else {
                if (!!$filterDesignation) {

                    $query->where('ART_Designation', 'LIKE', '%' . $filterDesignation . '%');
                }
            }
        } else {
            if (!!$famille) {
                $query->where('ART_Famille', '=', $famille);
                if (!!$filterDesignation) {
                    $query->where('ART_Designation', 'LIKE', '%' . $filterDesignation . '%');
                }
            } else {
                if (!!$filterDesignation) {

                    $query->where('ART_Designation', 'LIKE', '%' . $filterDesignation . '%');
                }
            }
        }

        $result = $query->toSql();
        $columns = ['ART_Code', 'ART_CodeBar', 'ART_Designation',
            'ART_QTEmin', 'SART_Qte', 'ART_Famille', 'STAT_Desg'];
        $count = $query->get($columns)->count();
        $paginate = $query->offset($offset)->limit($limit)->get($columns);

        $final_result = ["records" => $paginate,
            "count" => $count
        ];
        return response()->json($final_result);
    }

    public function DisplayArticleEnReptureStockByStation(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $station = $data["object"]["station"];

        $result = $connection->select("Set Language French  SET DATEFORMAT 'ymd' SELECT *
                      FROM  Rpt_View_Articlrupture INNER JOIN
                      StationArticle ON Rpt_View_Articlrupture.ART_Code = StationArticle.SART_CodeArt
                      WHERE (StationArticle.SART_Qte <= 0) 
                      AND (StationArticle.SART_CodeSatation = $station) 
                      OR (StationArticle.SART_Qte <= Rpt_View_Articlrupture.ART_QTEmin) AND (StationArticle.SART_CodeSatation = $station)");

        return response()->json($result);
    }

    public function DisplayTopArticleByStation(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $station = $data["object"]["station"];
        $date_from = AppHelper::setDateFormat($data["object"]["date_from"]);
        $date_to = AppHelper::setDateFormat($data["object"]["date_to"]);

        $result = $connection->table('View_B9')
            ->groupBy('ART_Designation', 'LT_CodArt', 'STAT_Code')
            ->selectRaw('ART_Designation,sum(LT_MtTTC) as LT_MtTTC,LT_CodArt,STAT_Code')
            ->whereBetween('TIK_DateHeureTicket', [$date_from, $date_to])
            ->where('STAT_Code', "=", $station)
            ->orderby('LT_MtTTC', 'DESC')
            ->get();

        /* $result =  $connection->select("SELECT SUM(LT_MtTTC) AS LT_MtTTC, STAT_Code, ART_Designation, LT_CodArt
                                          FROM View_B9
                                          WHERE TIK_DateHeureTicket  BETWEEN '$date_from' AND '$date_to' AND (STAT_Code = '$station')
                                          GROUP BY STAT_Code, ART_Designation, LT_CodArt
                                          ORDER BY LT_MtTTC DESC");*/

        return response()->json($result);

    }

    public function getArticleActif(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $station = $request->input('station');
        $famille = $request->input('famille');
        $offset = $request->input('offset');
        $limit = $request->input('limit');
        $filterDesignation = $request->input('filterDesignation');
        $columns = ['View_B9.ART_Designation', 'ART_Famille', 'STAT_Desg', 'STAT_Code', DB::raw('sum(LT_MtTTC) as LT_MtTTC'), 'LT_CodArt'];
        $query = $connection->table('View_B9')
            ->join('article', 'ART_Code', '=', 'LT_CodArt')
            ->select($columns)
            ->groupBy('View_B9.ART_Designation', 'LT_CodArt', 'STAT_Code', 'ART_Famille', 'STAT_Desg');
        //->orderby('LT_MtTTC', 'DESC');
        if (!!$station) {
            $result = $query->having('STAT_Code', "=", $station);
            if (!!$famille) {
                $result = $result->having('ART_Famille', "=", $famille);

            }
            if (!!$filterDesignation) {
                $result->having('View_B9.ART_Designation', 'LIKE', '%' . $filterDesignation . '%');
            }
        } else {
            if (!!$famille) {
                $result = $query->having('ART_Famille', "=", $famille);
            } else {
                $result = $query;
            }
            if (!!$filterDesignation) {
                $result->having('View_B9.ART_Designation', 'LIKE', '%' . $filterDesignation . '%');
            }

        }

        $count = $result->get()->count();
        $paginate = $result->offset($offset)->limit($limit)->get();

        $final_result = [
            "records" => $paginate,
            "count" => $count
        ];

        return response()->json($final_result);

    }

    public function DisplayValeurStockByAllStation(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $result = $connection->table('stock_par_station_valeur_achat')
            ->selectRaw('sum(val_Achat) as val_Achat , sum(val_Vente) as val_Vente')
            ->first();
        return response()->json($result);
    }


    public function DisplayTotalValeurStockByStation(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $station = $data["object"]["station"];
        $famille = $request->input('famille');
        if (!!$famille) {
            $result = $connection->table('stock_par_station_valeur_achat')
                ->selectRaw('sum(val_Achat) as val_Achat , sum(val_Vente) as val_Vente')
                ->where('STAT_Code', '=', $station)
                ->where('FAM_Code', '', $famille)
                ->first();
        } else {
            $result = $connection->table('stock_par_station_valeur_achat')
                ->selectRaw('sum(val_Achat) as val_Achat , sum(val_Vente) as val_Vente')
                ->where('STAT_Code', '=', $station)
                ->first();
        }


        return response()->json($result);

    }

    public function getProducts(Request $request)
    {
        $data = $request->json()->all();
        $station = $request->input('station');
        $famille = $request->input('famille');
        $offset = $request->input('offset');
        $limit = $request->input('limit');
        $filterDesignation = $request->input('filterDesignation');
        $connection = DatabaseConnection::setConnection($data);


        if (!!$station) {
            $columns = ['ART_Code', 'ART_Designation',
                'ART_CodeBar', 'ART_PrixUnitaireHT', 'PrixSolde', 'ART_DernPrixHT', 'ART_QteStock',
                'UniteArt', 'SART_CodeSatation', 'ART_Famille'];
            $result = $connection->table('View_getProducts')
                ->where('View_getProducts.SART_CodeSatation', '=', $station)
                ->select($columns);

            if (!!$famille) {
                $result->where('View_getProducts.ART_Famille', '=', $famille);
            }
            if (!!$filterDesignation) {
                $result->where('View_getProducts.ART_Designation', 'LIKE', '%' . $filterDesignation . '%');
            }
            if (!!$filterDesignation) {
                $result->where('View_getProducts.ART_Designation', 'LIKE', '%' . $filterDesignation . '%');
            }
        } else {
            $columns = ['ART_Code', 'ART_Designation',
                'ART_CodeBar', 'ART_PrixUnitaireHT', 'PrixSolde', 'ART_DernPrixHT', 'ART_QteStock',
                'UniteArt', 'ART_Famille'];
            $result = $connection->table('View_getAllArticles')
                ->select($columns);
            if (!!$famille) {
                $result->where('View_getAllArticles.ART_Famille', '=', $famille);
            }
            if (!!$filterDesignation) {
                $result->where('View_getAllArticles.ART_Designation', 'LIKE', '%' . $filterDesignation . '%');
            }
        }

        $count = $result->get()->count();
        $paginate = $result->offset($offset)->limit($limit)->get();

        $final_result = ["records" => $paginate,
            "count" => $count
        ];
        return response()->json($final_result);


    }

    public function DisplayProduitByStation(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $station = $data["object"]["station"];
        $result = $connection->table('article')
            ->leftJoin('ArticleStation', 'ArticleStation.ART_Code', '=', 'article.ART_Code')
            ->select('article.*')
            ->where('ArticleStation.STAT_Code', '=', $station)
            ->get();
        return response()->json($result, 201);

    }

    public function getArticleAnomalie(Request $request)
    {
        $data = $request->json()->all();
        $station = $request->input('station');
        $famille = $request->input('famille');
        $offset = $request->input('offset');
        $limit = $request->input('limit');
        $filterDesignation = $request->input('filterDesignation');
        $connection = DatabaseConnection::setConnection($data);
        $query = $connection->table('View_getArticleAnomalie')
            ->where(DB::raw("ISNULL(View_getArticleAnomalie.is_bloquer, 'False')"), '!=', 'True')
            ->where('View_getArticleAnomalie.UNITE_ARTICLE_IsUnitaire', '=', 'True')
            ->where(DB::raw("ISNULL(View_getArticleAnomalie.UNITE_ARTICLE_PrixVenteTTC, 0)"), '<=',
                DB::raw("ISNULL(View_getArticleAnomalie.ART_PrixUnitaireHT, 0) * (1 + View_getArticleAnomalie.ART_TVA / 100)"));


        if (!!$station) {
            $result = $query->where('View_getArticleAnomalie.SART_CodeSatation', '=', $station);
            if (!!$famille) {
                $result = $result->where('View_getArticleAnomalie.SART_CodeSatation', '=', $station)
                    ->where('View_getArticleAnomalie.ART_Famille', '=', $famille);
            }
            if (!!$filterDesignation) {
                $result->where('View_getArticleAnomalie.ART_Designation', 'LIKE', '%' . $filterDesignation . '%');
            }

        } else {
            $result = $query;
            if (!!$famille) {
                $result = $result
                    ->where('View_getArticleAnomalie.ART_Famille', '=', $famille);
            }
            if (!!$filterDesignation) {
                $result->where('View_getArticleAnomalie.ART_Designation', 'LIKE', '%' . $filterDesignation . '%');
            }

        }
        $columns = ['UNITE_ARTICLE_PrixVenteTTC', 'ART_Code', 'ART_Designation',
            'ART_PrixUnitaireHT', 'ART_CodeBar', 'PrixSolde', 'ART_Famille', 'SART_CodeSatation'];
        $count = $result->get($columns)->count();
        $paginate = $result->offset($offset)->limit($limit)->get($columns);

        $final_result = ["records" => $paginate,
            "count" => $count
        ];
        return response()->json($final_result);
    }


    public function getArticleClientPrix(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $result = $connection->select("Set Language French select * from article_client_prix");

        return response()->json($result);
    }

    public function getArticleFamille(Request $request)
    {

        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $result = $connection->select("Set Language French select * from famille");

        return response()->json($result);
    }

    public function get_counts_of_products(Request $request)
    {
        $count_products = $this->getProducts($request)->getData()->count;
        $count_products_actif = $this->getArticleActif($request)->getData()->count;
        $count_products_reptures = $this->getArticleEnRuptureDeStock($request)->getData()->count;
        $count_products_annomalies = $this->getArticleAnomalie($request)->getData()->count;
        $result =
            [
                "count products" => $count_products,
                "count products actifs" => $count_products_actif,
                "count products reptures" => $count_products_reptures,
                "count products annomalies" => $count_products_annomalies

            ];
        return response()->json($result);
    }

    public function get_count_getProducts(Request $request)
    {
        $count_products = $this->getProducts($request)->getData()->count;
        return ["count products" => $count_products];
    }

    public function get_count_getArticleActif(Request $request)
    {
        $count_products = $this->getArticleActif($request)->getData()->count;
        return ["count products actifs" => $count_products];
    }

    public function get_count_getArticleEnRuptureDeStock(Request $request)
    {
        $count_products = $this->getArticleEnRuptureDeStock($request)->getData()->count;
        return ["count products reptures" => $count_products];
    }

    public function get_count_getArticleAnomalie(Request $request)
    {
        $count_products = $this->getArticleAnomalie($request)->getData()->count;
        return ["count products annomalies" => $count_products];
    }

    public function getArticleByChunk(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $limit = $request->input('limit');
        $result = array();
        $connection->table('View_GetArticleNonMouvemente')
            ->orderBy('DDm')
            ->chunk($limit, function ($articles) use (&$result) {
                $result[] = $articles;
            });

        return response()->json($result);
    }

    public function getArticleWithPagination(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $article = new article();
        $Article_Site = $connection->table('ville')->pluck('Article_Site')->first();
        $station = $request->input('station');
        $offset = $request->input('page');
        $limit = $request->input('limit');
        $ddm = $request->input('ddm');
        $result = array();
        if (!!Request::createFromGlobals()->hasHeader('user')) {
            $idUser = $request->header('user');
            $autorisation_article_mouvemente = $connection->table('AutorisationUser')
                ->where('AutoCodeUt', '=', $idUser)
                ->where("AutoCodeAu", "=", "820000000")
                ->pluck('AutEtat')
                ->first();
            if (!$station) {

                //article non Mouvementé
                $result = $connection->table('View_GetArticleNonMouvemente')
                    ->leftJoin('dbo.Unite_article', 'View_GetArticleNonMouvemente.ART_Code', '=', 'dbo.Unite_article.UNITE_ARTICLE_CodeArt')
                    ->select('View_GetArticleNonMouvemente.*', 'dbo.Unite_article.UNITE_ARTICLE_PrixVenteTTC') // Sélectionnez uniquement les colonnes nécessaires de la table dbo.Unite_article
                    ->paginate($limit);

            } else {

                $type_user = $connection->table('utilisateur')->where('Code_Ut', '=', $idUser)->pluck('Type_user')->first();


                if (strtolower($type_user) === 'operateur patrimoine') {
                    $articles = array_values($article->getPatrimoine($request, $connection));

                    $resultsCollection = collect($articles);
                    $page = LengthAwarePaginator::resolveCurrentPage();
                    $result = new LengthAwarePaginator(
                        $resultsCollection->forPage($page, $limit)->values(),
                        $resultsCollection->count(),
                        $limit,
                        $page,
                        ['path' => LengthAwarePaginator::resolveCurrentPath()]
                    );

                } else {
                    //Type User Vendeur
                    //Article Site

                    if ((bool)$Article_Site) {
                        $query = $article->articleStation($request, $ddm);
                        //filter by station
                        $query .= " AND (dbo.ArticleStation.STAT_Code = " . $station . ") ";
                        $resultsFromSql = $connection->select($query);
                        $articles = $article->PrixSiteOrPrixPublique($resultsFromSql, $connection, $station);
                        $resultsCollection = collect($articles);
                        $page = LengthAwarePaginator::resolveCurrentPage();
                        $result = new LengthAwarePaginator(
                            $resultsCollection->forPage($page, $limit)->values(),
                            $resultsCollection->count(),
                            $limit,
                            $page,
                            ['path' => LengthAwarePaginator::resolveCurrentPath()]
                        );

                    } else {
                        //Station Article
                        //if ((bool)$autorisation_article_mouvemente) {
                        $query = $article->ArticleMVT() . "AND (LOWER(dbo.article.Type_Produit) != 'patrimoine')";
                        //filter by station
                        $query .= " AND (dbo.StationArticle.SART_CodeSatation = " . $station . ") ";
                        /* if ($ddm) {
                             $query .= "AND dbo.article.ddm >= '" . $ddm . "'";
                         }*/

                        $resultsFromSql = $connection->select($query);
                        $resultsCollection = collect($resultsFromSql);
                        $page = LengthAwarePaginator::resolveCurrentPage();
                        $result = new LengthAwarePaginator(
                            $resultsCollection->forPage($page, $limit)->values(),
                            $resultsCollection->count(),
                            $limit,
                            $page,
                            ['path' => LengthAwarePaginator::resolveCurrentPath()]
                        );


                    }
                }
            }


        }

        return response()->json($result);

    }

}
