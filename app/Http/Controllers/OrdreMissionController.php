<?php

namespace App\Http\Controllers;

use App\Helpers\AppHelper;
use Illuminate\Http\Request;
use App\Helpers\DatabaseConnection;
use Carbon\Carbon;

class OrdreMissionController extends Controller
{

    public function DisplayEtatOrdreMission(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);

        $result = $connection->table('EtatOrdreMission')
            ->select('EtatOrdreMission.*')
            ->get();
        return response()->json($result, 201);
    }

    public function DisplayOrdre(Request $request)
    {
        $results = array();
        $data = $request->json()->all();
        $ORD_vendeur = $data["object"]["ORD_vendeur"];
        $ORD_Date = $data["object"]["ORD_Date"];

        $connection = DatabaseConnection::setConnection($data);
        $user = $connection->table('Utilisateur')->where('Code_Ut', $ORD_vendeur)->first();
        $ordres = $connection->table('Ordre_mission')
            ->select('Ordre_mission.*')
            ->where('Ordre_mission.ORD_vendeur', "=", $ORD_vendeur)
            ->whereDate('Ordre_mission.ORD_date', "=", $ORD_Date)
            ->get();

        if (strtolower($user->Type_user) === "chef zone") {
            $ordres = $connection->table('Ordre_mission')
                ->select('Ordre_mission.*')
                ->where('Ordre_mission.ORD_vendeur', "=", $ORD_vendeur)
                ->where(strtolower('Ordre_mission.ORD_Type'), "=", 'tournechef')
                ->whereDate('Ordre_mission.ORD_date', "=", $ORD_Date)
                ->get();
        }


        for ($index = 0; $index < sizeof($ordres); $index++) {
            $item = array();
            $item["ordreMission"] = $ordres[$index];
            $lignes = $connection->table('Ligne_OrdreMission')
                ->select('Ligne_OrdreMission.*')
                ->where('Ligne_OrdreMission.LIGOR_Code', '=', $ordres[$index]->ORD_Code)
                ->get();
            $item["ligneOrdreMission"] = $lignes;
            $results[] = $item;
        }

        return response()->json($results);

    }

    public function BatchUpdateOrdreMission(Request $request)
    {

        $data = $request->json()->all();
        $result = array();

        foreach ($data['object'] as $item) {
            $result[] = $this->UpdateEtatOrdreMission($request, $item);
        }

        return response()->json($result);

    }

    public function UpdateEtatOrdreMission(Request $request, $array = [])
    {
        $data = $request->json()->all();
        $item = empty($array) ? $data['object'] : $array;
        $connection = DatabaseConnection::setConnection($data);
        $updated = $connection->table('Ligne_OrdreMission')
            ->where('LIGOR_Code', '=', $item["LIGOR_Code"])
            ->where('LIGOR_Exer', '=', $item["LIGOR_Exer"])
            ->where('LIGOR_Clt', '=', $item["LIGOR_Clt"])
            ->update([
                'LIGOR_Etat' => $item["LIGOR_Etat"],
                "LIGOR_Latitude" => $item["LIGOR_Latitude"] ?? 0,
                "LIGOR_Longitude" => $item["LIGOR_Longitude"] ?? 0,
                "LIGOR_Date" => $item["LIGOR_Date"] ?? null,
                "LIGOR_Note" => $item["LIGOR_Note"] ?? null
            ]);
        if ($updated) {
            // Number of mission order lines that are not completed.
            // If the number == 0, then all the lines are completed, and the mission order is considered completed.
            $line = $connection->table('Ligne_OrdreMission')
                ->select('Ligne_OrdreMission.*')
                ->where('Ligne_OrdreMission.LIGOR_Etat', '<>', 'ET000002')
                ->where('Ligne_OrdreMission.LIGOR_Code', '=', $item["LIGOR_Code"])
                ->get();

            if (count($line) == 0) {
                $connection->table('Ordre_mission')
                    ->where('ORD_Code', $item["LIGOR_Code"])
                    ->update(['ORD_etat' => 'ET000002']);
                $result = $this->setGlobalResult('ORD_Code', $item["LIGOR_Code"], 'THE MISSION ORDER IS COMPLETED');
            } else {
                $result = $this->setGlobalResult('ORD_Code', $item["LIGOR_Code"], 'THE MISSION ORDER IS NOT COMPLETED');
            }
        } else {
            $result = $this->setGlobalResult('ORD_Code', $item["LIGOR_Code"], 'THE MISSION ORDER IS NOT UPDATED');
        }
        return $result;
    }

    public function AddOrdreMission(Request $request)
    {
        $items = $request->json()->all();
        $connection = DatabaseConnection::setConnection($items);
        $date = Carbon::now()->toDateString();
        $idUser = $request->header('user');

        if (!empty($items)) {
            foreach ($items["object"] as $data) {

                //ORD CODE
                $ordCode = (new PrefixWSMobile())->getPrefixOrdreMission($connection,
                    'Ordre_mission',
                    $data['ORD_exercice'],
                    $data['ORD_station'],
                    $idUser);

                //EXIST
                $exist = $connection->table('Ordre_mission')
                    ->whereDate('ORD_date', '=', $date)->first();

                //End OF TOUR TIME
                $time = $connection->table('Utilisateur')
                    ->where('Code_Ut', '=', $idUser)
                    ->value('HeurFintourne');

                //ORD FINAL DATE
                $ORD_dateFin = date('Y-m-d H:i:s', strtotime($date . $time));

                if (!$exist) {
                    $inserted = $connection->table('Ordre_mission')->insert([
                        'ORD_Code' => $ordCode,
                        'ORD_exercice' => $data['ORD_exercice'],
                        'ORD_date' => Carbon::now()->toDateString(),
                        'ORD_vendeur' => $idUser,
                        'ORD_note' => $data['ORD_note'],
                        'ORD_etat' => 'ET000001',
                        'ORD_user' => $idUser,
                        'ORD_DDM' => $data['ORD_DDM'],
                        'ORD_circuit' => $data['ORD_circuit'],
                        'ORD_station' => $data['ORD_station'],
                        'export' => $data['export'],
                        'DDm' => $data['DDm'],
                        'exportM' => $data['exportM'],
                        'DDmM' => $data['DDmM'],
                        'ORD_stationDepart' => $data['ORD_station'],
                        'ORD_dateDebut' => $date,
                        'ORD_dateFin' => $ORD_dateFin,
                        'ORD_Session' => $data['ORD_Session'],
                    ]);
                    if ($inserted) {
                        return $this->setGlobalResult("ORD_Code", $ordCode, "INSERTED");
                    }
                } else {
                    return $this->setGlobalResult("ORD_Code", $exist->ORD_Code, "ALREADY EXIST");
                }
            }

        } else {
            return response()->json("EMPTY OBJECT");
        }

    }


}







