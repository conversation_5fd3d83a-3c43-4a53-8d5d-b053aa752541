<?php

namespace App\Http\Controllers;

use App\Helpers\AppHelper;
use App\Helpers\DatabaseConnection;
use Carbon\Carbon;
use Illuminate\Http\Request;

/**
 * @OA\Tag(
 *     name="Bons de Retour Mobile",
 *     description="Gestion des bons de retour depuis l'application mobile ProCaisse"
 * )
 */
class BonRetourWSMobile extends Controller
{
    /**
     * @OA\Post(
     *     path="/BonRetour/getBonRetour",
     *     tags={"Bons de Retour Mobile"},
     *     summary="Récupérer les bons de retour",
     *     description="Retourne la liste complète des bons de retour",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Paramètres de connexion",
     *         @OA\JsonContent(ref="#/components/schemas/StandardRequest")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Liste des bons de retour récupérée avec succès",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/BonRetour")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function getBonRetour(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('bon_retour')->get());
    }

    public function addBatchBonRetour(Request $request)
    {
        $items = $request->json()->all();
        if (!empty($items)) {
            $connection = DatabaseConnection::setConnection($items);
            $result = [];
            foreach ($items["object"] as $data) {
                $item = $data;


                $item["parent"]['BOR_date'] = AppHelper::setDateFormat($data["parent"]['BOR_date']);
                $item["parent"]["BOR_Mnt_Fodec"] = floatval($data["parent"]["BOR_Mnt_Fodec"]);
                $item["parent"]["BOR_Mnt_HT"] = floatval($data["parent"]["BOR_Mnt_HT"]);
                $item["parent"]["BOR_Mnt_Tva"] = floatval($data["parent"]["BOR_Mnt_Tva"]);
                $item["parent"]["BOR_Mnt_TTC"] = floatval($data["parent"]["BOR_Mnt_TTC"]);


                $exist =
                //    (isset($data["parent"]['BOR_Numero_M']) && $data["parent"]['BOR_Numero_M'] != 'empty') &&
                    sizeof(collect($connection->select("select BOR_Numero from Bon_Retour where 
                        BOR_Numero = '" . $data["parent"]['BOR_Numero'] . "'")));

                if (!$exist) {
                    $item["parent"]["BOR_Numero"] = (new PrefixWSMobile)->getRetourPrefix(
                        $items,
                        "Bon_Retour",
                        $item["parent"]['BOR_Exercice'],
                        $item["parent"]['BOR_Station'],
                        "Bon_retour_vente",
                        $item["parent"]["Observation"]
                    );

                    $data["parent"]["BOR_Numero"] = $item["parent"]["BOR_Numero"];
                }
              /*  $item["parent"]["BOR_Numero_M"] = Carbon::now();
                $data["parent"]['BOR_Numero_M'] = Carbon::now();*/
                $lignesBC = $data["children"];
                $lignesBC = $this->chanLineNumWithParentNum($lignesBC, $data["parent"]['BOR_Numero']);

                $connection->table('Bon_Retour')
                    ->where('BOR_Numero', '=', $data["parent"]['BOR_Numero'])
                    ->delete();

                $connection->table('Bon_Retour')->insert($item["parent"]);
                $connection->table('ligne_bon_retour')
                    ->where('NumBon_Retour', '=', $data["parent"]['BOR_Numero'])
                    ->delete();
                foreach ($lignesBC as $ligneBC) {
                    $ligneBC['LIG_BonEntree_DDm'] = AppHelper::setDateFormat($ligneBC['LIG_BonEntree_DDm']);
                    $ligneBC["LIG_BonEntree_MntNetHt"] = floatval($ligneBC["LIG_BonEntree_MntNetHt"]);
                    $ligneBC["LIG_BonEntree_MntTva"] = floatval($ligneBC["LIG_BonEntree_MntTva"]);
                    $ligneBC["LIG_BonEntree_MntTTC"] = floatval($ligneBC["LIG_BonEntree_MntTTC"]);
                    $ligneBC["LIG_BonEntree_Remise"] = floatval($ligneBC["LIG_BonEntree_Remise"]);
                    $connection->table('ligne_bon_retour')->insert($ligneBC);
                }
                    array_push($result, $data["parent"]);
            }
        } else {
            return response()->json(null);
        }

        return response()->json($result);
    }

    public function chanLineNumWithParentNum($lignesBC, $data)
    {
        for ($i = 0; $i < count($lignesBC); $i++) {
            $lignesBC[$i]["NumBon_Retour"] = $data;
        }
        return $lignesBC;
    }

    public function addBatchBonLivraisonMobile(Request $request)
    {
        $items = $request->json()->all();

        if (!empty($items)) {
            $connection = DatabaseConnection::setConnection($items);
            $result = null;
            foreach ($items["object"] as $data) {
                if ($data["BON_Trans_Vehicule"] == "NULL" || $data["BON_Trans_Vehicule"] == null) {
                    $data["BON_Trans_Vehicule"] = null;
                }

                if ($data["BON_Trans_obs"] == "NULL" || $data["BON_Trans_obs"] == null) {
                    $data["BON_Trans_obs"] = null;
                }

                if ($data["Num_Manuelle"] == "NULL" || $data["Num_Manuelle"] == null) {
                    $data["Num_Manuelle"] = null;
                }

                if ($data["Observation"] == "NULL" || $data["Observation"] == null) {
                    $data["Observation"] = null;
                }

                if ($data["BON_Trans_Mnt_HT"] == "NULL" || $data["BON_Trans_Mnt_HT"] == null) {
                    $data["BON_Trans_Mnt_HT"] = "NULL";
                }

                if ($data["BON_Trans_Mnt_TTC"] == "NULL" || $data["BON_Trans_Mnt_TTC"] == null) {
                    $data["BON_Trans_Mnt_TTC"] = "NULL";
                }

                if ($data["BON_Trans_Etat_Saisie"] == "NULL" || $data["BON_Trans_Etat_Saisie"] == null) {
                    $data["BON_Trans_Etat_Saisie"] = null;
                }

                $data['DDm'] = null;
                $data['BON_Trans_DDm'] = null;
                if (array_key_exists("BON_ENT_SYNC", $data)) {
                    unset($data["BON_ENT_SYNC"]);
                }
                $this->updateOrCreate($connection, $data, 'bon_transfert', [['BON_Trans_Num', '=', $data['BON_Trans_Num']], ['BON_Trans_Exerc', '=', $data['BON_Trans_Exerc']]]);
                $c = $connection->insert("Set Language French  SET DATEFORMAT 'ymd' 
                insert into [bon_transfert] ([BON_Trans_DDm], [BON_Trans_Date], [BON_Trans_Etat], [BON_Trans_Etat_Saisie],
                [BON_Trans_Exerc], [BON_Trans_export], [BON_Trans_Mnt_HT], [BON_Trans_Mnt_TTC], [BON_Trans_Num], [BON_Trans_obs],
                 [BON_Trans_Stat], [BON_Trans_StatDest], [BON_Trans_StatSource], [BON_Trans_Transporteur], [BON_Trans_User], [BON_Trans_Vehicule],
                  [DDm], [Declaree], [Exportation], [Num_Manuelle], [Observation])

                 values (
                     '" . $data['BON_Trans_DDm'] . "',
                  '" . $data["BON_Trans_Date"] . "',
                   '" . $data["BON_Trans_Etat"] . "',
                    '" . $data["BON_Trans_Etat_Saisie"] . "',
                 '" . $data["BON_Trans_Exerc"] . "',
                  '" . $data["BON_Trans_export"] . "',
                   " . $data["BON_Trans_Mnt_HT"] . ",
                    " . $data["BON_Trans_Mnt_TTC"] . ",
                     '" . $data["BON_Trans_Num"] . "',
                     '" . $data["BON_Trans_obs"] . "',
                 '" . $data["BON_Trans_Stat"] . "',
                  '" . $data["BON_Trans_StatDest"] . "',
                   '" . $data["BON_Trans_StatSource"] . "',
                   '" . $data["BON_Trans_Transporteur"] . "',
                    '" . $data["BON_Trans_User"] . "',
                     '" . $data["BON_Trans_Vehicule"] . "',
                 '" . $data["DDm"] . "',
                  '" . $data["Declaree"] . "',
                   '" . $data["Exportation"] . "',
                    '" . $data["Num_Manuelle"] . "',
                    '" . $data["Observation"] . "')");
                if ($c) {
                    $result[] = $data;
                } else {
                    $result[] = null;
                }
            }
        } else {
            return response()->json(null);
        }

        return response()->json($result);
    }


    public function updateOrCreate($connection, $data, $table, $whereClauses)
    {
        $exists = $connection->table($table)->where($whereClauses)->first();
        if ($exists) {
            return $connection->table($table)->where($whereClauses)->update($data);
        }
        return $connection->table($table)->insert($data)->setlocale('fr');
    }
}
