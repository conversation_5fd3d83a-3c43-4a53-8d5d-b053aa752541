<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests;
use Illuminate\Http\Response;
use App\Helpers\DatabaseConnection;

/**
 * @OA\Tag(
 *     name="Carnets Mobile",
 *     description="Gestion des carnets de tickets depuis l'application mobile ProCaisse"
 * )
 */
class CarnetWSMobile extends Controller
{
    /**
     * @OA\Post(
     *     path="/Carnet/getCarnetByID",
     *     tags={"Carnets Mobile"},
     *     summary="Récupérer un carnet par son ID",
     *     description="Retourne les détails d'un carnet spécifique en utilisant son identifiant",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Paramètres de connexion et ID du carnet",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="connexion",
     *                 ref="#/components/schemas/DatabaseConnection"
     *             ),
     *             @OA\Property(
     *                 property="object",
     *                 type="string",
     *                 example="CARNET001",
     *                 description="ID du carnet à rechercher"
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Carnet trouvé avec succès",
     *         @OA\JsonContent(ref="#/components/schemas/Carnet")
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Carnet non trouvé",
     *         @OA\JsonContent(type="null")
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
	public function getCarnetByID(Request $request)
	 {
		  $data = $request->json()->all();

	 	$connection = DatabaseConnection::setConnection($data);
		   return response()->json($connection->table('Carnet')->where('CAR_IdCarnet', $data["object"])->get()->first());
	 }
	


	
}





