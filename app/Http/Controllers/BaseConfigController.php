<?php

namespace App\Http\Controllers;

use App\Helpers\MCrypt;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;

/**
 * @OA\Tag(
 *     name="Configuration Base",
 *     description="Gestion de la configuration de base et des licences ProCaisse"
 * )
 */
class BaseConfigController extends Controller
{
    /**
     * @OA\Post(
     *     path="/License/getLicensesUrl",
     *     tags={"Configuration Base"},
     *     summary="Récupérer les URLs des services de licences",
     *     description="Retourne la liste des URLs des services de licences depuis le fichier de configuration",
     *     @OA\Response(
     *         response=200,
     *         description="URLs des licences récupérées avec succès",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/LicenseUrl")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function get_licenses_services_url(): array
    {
        /*$client = new \GuzzleHttp\Client();
        $response =  $client->get('http://***********:8000/api/getKeyCloak');
        response()->json($response->getBody()->getContents());*/
        $json = File::get("../database/data/LicenseDataBase.json");
        $licenses_urls = (array)json_decode($json, false);
        $result = array();
        foreach ($licenses_urls['data'] as $license)
        {
            $license_url = new \App\Models\LicenseUrl();
            $license_url->forceFill((array)$license);
            $result[] = $license_url;
        }

        return $result;
    }
    public  function put_url_licenses(Request $request)
    {
        $data = $request->json()->all();
        $json = File::get("../database/data/LicenseDataBase.json");
        $licenses_urls = (array)json_decode($json, false);
        unset($licenses_urls["data"]);
        //encode array to json and save to file
        file_put_contents('../database/data/LicenseDataBase.json', json_encode($licenses_urls));
        file_put_contents("../database/data/LicenseDataBase.json",json_encode($data));

        return $this->get_licenses_services_url();
    }
    public function get_backup_url(): array
    {
        $json = File::get("../database/data/Backup_url.json");
        $licenses_urls = (array)json_decode($json, false);
        $result = array();
        foreach ($licenses_urls['data'] as $license)
        {
            $license_url = new \App\Models\LicenseUrl();
            $license_url->forceFill((array)$license);
            $result[] = $license_url;
        }

        return $result;
    }
    public function encrypt_baseConfig(Request $request)
    {
        $data = $request->json()->all();
        $result = array();
        $mcrypt = new MCrypt();
        foreach ($data as $item=>$value)
        {
            $result[$item] = $mcrypt->encrypt($value);
        }
        return response()->json($result);
    }
}
