<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests;
use Illuminate\Http\Response;
use App\Helpers\DatabaseConnection;

class LigneTicketWSMobile extends Controller
{


    /*fonction get data from database*/

    public function getLigneTicket(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('LigneTicket')->get());
    }

    public function getLigneTicketByCarnetId(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('LigneTicket')->where('LT_IdCarnet', $data["object"])->get());

    }


    public function getLigneTicketByTicket(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('LigneTicket')
            ->where('LT_NumTicket', $data["object"]["TIK_NumTicket"])
            ->where('LT_IdCarnet', $data["object"]["TIK_IdCarnet"])
            ->where('LT_Exerc', $data["object"]["TIK_Exerc"])
            ->get());

    }

    public function getLigneTicketByTickets(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        $items = null;

        foreach ($data["object"] as &$item) {
            $items[] = $connection->table('LigneTicket')
                ->where('LT_NumTicket', $item["TIK_NumTicket"])
                ->where('LT_IdCarnet', $item["TIK_IdCarnet"])
                ->where('LT_Exerc', $item["TIK_Exerc"])
                ->get();


        }


        return response()->json($items);
    }


    public function getLigneTicketByX(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('LigneTicket')->where($request->field, $request->value)->get());

    }


    public function addLigneTicket(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        $data = $data["object"];
        if (!empty ($data)) {
            foreach ($data as $ligneTicket) {
                $ligneTicket["LT_QtePiece"] = "1";
                $ligneTicket["LT_MtHT"] = floatval($ligneTicket["LT_MtHT"]);
                $ligneTicket["LT_MtTTC"] = floatval($ligneTicket["LT_MtTTC"]);
                $ligneTicket["LT_PACHAT"] = floatval($ligneTicket["LT_PACHAT"]);
                $ligneTicket["LT_PrixEncaisse"] = floatval($ligneTicket["LT_PrixEncaisse"]);
                $ligneTicket["LT_Remise"] = floatval($ligneTicket["LT_Remise"]);
                $ligneTicket["LT_PrixVente"] = floatval($ligneTicket["LT_PrixVente"]);
                $t = $connection->table('LigneTicket')->insert($ligneTicket);

            }

            return response()->json($t);

        } else {
            return (false);
        }

    }


    public function updateLigneTicket(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);

        if (!empty ($data)) {

            $response = $connection->table('LigneTicket')->where('LT_NumTicket', $data["LT_NumTicket"])->where('LT_IdCarnet', $data["LT_IdCarnet"])->where('LT_Exerc', $data["LT_Exerc"])->where('LT_CodArt', $data["LT_CodArt"])->where('LT_Unite', $data["LT_Unite"])->update($data);


            return response()->json(($response == 1) ? true : false);

        } else {
            return response()->json(false);
        }


    }


    public function deleteLigneTicket(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        if (!empty ($data)) {
            $data["LT_Annuler"] = true;

            $response = $connection->table('LigneTicket')->where('LT_NumTicket', $data["LT_NumTicket"])->where('LT_IdCarnet', $data["LT_IdCarnet"])->where('LT_Exerc', $data["LT_Exerc"])->where('LT_CodArt', $data["LT_CodArt"])->where('LT_Unite', $data["LT_Unite"])->update($data);


            return response()->json($response == 1);

        } else {
            return response()->json(false);
        }
    }


}





