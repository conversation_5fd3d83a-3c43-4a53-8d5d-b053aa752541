<?php

namespace App\Http\Controllers;

use App\Helpers\DatabaseConnection;
use App\Helpers\MCrypt;
use App\Models\ZoneUtilisateur;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Schema;

class UtilisateurWSMobile extends Controller
{


    public function getUtilisateur(Request $request)
    {
        $data = $request->json()->all();

        $connection = DatabaseConnection::setConnection($data);
        return response()->json($connection->table('Utilisateur')->where('Etat', 'Active')->get());

    }

    public function Authentification(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);

        $user = $connection->table('Utilisateur')->where('Login', $data["object"]['Login'])
            ->where('Passe', $data["object"]['Passe'])
            ->where('Etat', 'active')->first();


        try {

            if (!!$user) {

                $header = $request->header('Application-name');
                $listLicence = explode(";", $header);
                $allAutorisations = [];

                foreach ($listLicence as $item) {
                    $item = str_replace(' ', '', $item);

                    $res = $connection->table('AutorisationUser')
                        ->join('Autorisation', 'Autorisation.AutoCode', '=', 'AutorisationUser.AutoCodeAu')
                        ->where('Autorisation.AutoTypeMenu', '=', $item)
                        ->where("AutoCodeUt", $user->Code_Ut)
                        ->get();
                    $allAutorisations = array_merge($allAutorisations, $res->toArray());
                }
                $user->autorisationUser = $allAutorisations;

                $user->zones = array_column(ZoneUtilisateur::query()
                    ->join('Zone', 'ZoneUtilisateur.CodeZone', '=', 'Zone.CodeZone')
                    ->where('Zone.EtatZone', '=', true)
                    ->where('ZoneUtilisateur.CodeUtilisateur', '=', $user->Code_Ut)
                    ->select('ZoneUtilisateur.CodeZone')
                    ->get()
                    ->toArray(), 'CodeZone');

            }
        } catch (\Exception $e) {
            return response()->json($e->getMessage());
        }

        return $user != null ? response()->json($user) : response()->json(null);
    }


    public function getCaissier(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $station = $request->input('station');
        if (!!$station) {
            $result = $connection->table('Utilisateur')
                ->leftJoin('station', 'station.STAT_Code', '=', 'Utilisateur.Station')
                ->where('station.STAT_Code', '=', $station)
                ->select('Utilisateur.Code_Ut', 'Utilisateur.Nom', 'Utilisateur.Prenom', 'Utilisateur.Station', 'station.STAT_Desg', 'Utilisateur.Code_Caisse')
                ->get();

        } else {
            $result = $connection->table('Utilisateur')
                ->leftJoin('station', 'station.STAT_Code', '=', 'Utilisateur.Station')
                ->select('Utilisateur.Code_Ut', 'Utilisateur.Nom', 'Utilisateur.Prenom', 'Utilisateur.Station', 'station.STAT_Desg', 'Utilisateur.Code_Caisse')
                ->get();
        }

        return response()->json($result);
    }


    public function getAll(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        $result = $connection->table('Utilisateur')->where("Etat", "=", "Active")->get();
        return response()->json($result);
    }

    public function insertUserToken(Request $request)
    {
        $data = $request->json()->all();
        $connection = DatabaseConnection::setConnection($data);
        if (!!Request::createFromGlobals()->hasHeader('user')) {
            if (Schema::connection("onthefly")->hasColumn('Utilisateur', 'token')) {

                $idUser = $request->header('user');
                $token = $request->input('token');

                $user = $connection->table('Utilisateur')->where('Code_Ut', '=', $idUser)->update(['token' => $token]);

                return response()->json(true);
            } else {
                return response()->json(false);
            }
        }

    }

    public function DcryptBaseConfig(Request $request)
    {
        $data = $request->json()->all();
        $connection = $data["connexion"];


        $result = [
            "dbIpAddress" => MCrypt::decrypt($connection["dbIpAddress"]),
            "dbName" => MCrypt::decrypt($connection["dbName"]),
            "username" => MCrypt::decrypt($connection["username"]),
            "password" => MCrypt::decrypt($connection["password"]),

        ];

        return $result;

    }

}
