<?php

namespace App\Http\Controllers;

use App\Helpers\DatabaseConnection;
use Illuminate\Http\Request;
use App\Http\Requests;
use Illuminate\Http\Response;
use DB;

class ArticleCodeBarWSMobile extends Controller
{

    public function getArticleCodeBarByCode(Request $request)
    {
        return response()->json(DB::table('article_code_bar')->where('Parent_CodeBar', $request->Parent_CodeBar)->where('Fils_CodeBar', $request->Fils_CodeBar)->first());
    }

    public function getArticleCodeBar()
    {
        $article = DB::table('article_code_bar')->get();
        //afficher tous les lignes
        foreach ($article as $a) {
            $data[] = ['Parent_CodeBar' => $a->Parent_CodeBar,
                'Fils_CodeBar' => $a->Fils_CodeBar,
                'cod_b_user' => $a->cod_b_user,
                'cod_b_station' => $a->cod_b_station
            ];
        }

        return $data;
    }


    public function getArticleCodeBarByX(Request $request)
    {
        return response()->json(DB::table('article_code_bar')->where($request->field, $request->value)->get());

    }


    public function addArticleCodeBar(Request $request)
    {
        $data = $request->json()->all();


        if (!empty ($data)) {
            return response()->json(DB::table('article_code_bar')->insert($data));
        } else {
            return (false);
        }


    }

    public function addArticleCodeBarMobile(Request $request)
    {
        $items = $request->json()->all();
        $connection = DatabaseConnection::setConnection($items);

        if (!empty ($items)) {
            foreach ($items["object"]["articleCodeBars"] as $data) {
                if ($data["cod_b_export"] == "NULL")
                    $data["cod_b_export"] = NULL;

                $exist = $connection->table('article_code_bar')
                    ->where('Parent_CodeBar', '=', $data['Parent_CodeBar'])
                    ->where('Fils_CodeBar', '=', $data['Fils_CodeBar'])
                    ->get();
                if (is_null($exist) || $exist->isEmpty()) {
                    $f = $connection->table('article_code_bar')->insert($data);
                } else {
                    $f = $connection->table('article_code_bar')
                        ->where('Parent_CodeBar', '=', $data['Parent_CodeBar'])
                        ->where('Fils_CodeBar', '=', $data['Fils_CodeBar'])
                        ->update([
                            'cod_b_export' => $data['cod_b_export'],
                        ]);
                }

            }
            return response()->json($f);

        } else {
            return (false);
        }


    }


    public function updateArticleCodeBar(Request $request)
    {
        $data = $request->json()->all();
        DB::table('article_code_bar')->where('Parent_CodeBar', $data["Parent_CodeBar"])->where('Fils_CodeBar', $data["Fils_CodeBar"])->update($data);
        return ("Data modified");

    }


    public function deleteArticleCodeBar(Request $request)
    {
        DB::table('article_code_bar')->where('Parent_CodeBar', $request->Parent_CodeBar)->where('Fils_CodeBar', $request->Fils_CodeBar)->delete();
        return ("Data Deleted");
    }

    public function updateOrCreate($connection, $data, $table, $whereClauses)
    {
        $exists = $connection->table($table)->where($whereClauses)->first();

        if ($exists) {
            $connection->table($table)->where($whereClauses)->delete();
        }

        return $connection->table($table)->insert($data);

    }
}





