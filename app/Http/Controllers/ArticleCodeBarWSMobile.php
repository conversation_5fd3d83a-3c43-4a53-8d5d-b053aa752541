<?php

namespace App\Http\Controllers;

use App\Helpers\DatabaseConnection;
use Illuminate\Http\Request;
use App\Http\Requests;
use Illuminate\Http\Response;
use DB;

/**
 * @OA\Tag(
 *     name="Article Code Barres Mobile",
 *     description="Gestion des codes à barres d'articles depuis l'application mobile ProCaisse"
 * )
 */
class ArticleCodeBarWSMobile extends Controller
{

    /**
     * @OA\Post(
     *     path="/ArticleCodebar/getArticleCodeBarByCode",
     *     tags={"Article Code Barres Mobile"},
     *     summary="Récupérer un code à barres par codes parent et fils",
     *     description="Retourne un code à barres d'article spécifique en utilisant les codes parent et fils",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Codes parent et fils du code à barres",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="Parent_CodeBar", type="string", example="PARENT001", description="Code à barres parent"),
     *             @OA\Property(property="Fils_CodeBar", type="string", example="FILS001", description="Code à barres fils")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Code à barres trouvé avec succès",
     *         @OA\JsonContent(ref="#/components/schemas/ArticleCodeBar")
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Code à barres non trouvé",
     *         @OA\JsonContent(type="null")
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function getArticleCodeBarByCode(Request $request)
    {
        return response()->json(DB::table('article_code_bar')->where('Parent_CodeBar', $request->Parent_CodeBar)->where('Fils_CodeBar', $request->Fils_CodeBar)->first());
    }

    /**
     * @OA\Get(
     *     path="/ArticleCodebar/getArticleCodeBar",
     *     tags={"Article Code Barres Mobile"},
     *     summary="Récupérer tous les codes à barres d'articles",
     *     description="Retourne la liste complète des codes à barres d'articles avec leurs informations",
     *     @OA\Response(
     *         response=200,
     *         description="Liste des codes à barres récupérée avec succès",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/ArticleCodeBarSimple")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function getArticleCodeBar()
    {
        $article = DB::table('article_code_bar')->get();
        //afficher tous les lignes
        foreach ($article as $a) {
            $data[] = ['Parent_CodeBar' => $a->Parent_CodeBar,
                'Fils_CodeBar' => $a->Fils_CodeBar,
                'cod_b_user' => $a->cod_b_user,
                'cod_b_station' => $a->cod_b_station
            ];
        }

        return $data;
    }


    /**
     * @OA\Post(
     *     path="/ArticleCodebar/getArticleCodeBarByX",
     *     tags={"Article Code Barres Mobile"},
     *     summary="Rechercher des codes à barres par champ dynamique",
     *     description="Permet de rechercher des codes à barres en spécifiant un champ et une valeur",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Champ et valeur de recherche",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="field", type="string", example="Parent_CodeBar", description="Nom du champ à rechercher"),
     *             @OA\Property(property="value", type="string", example="PARENT001", description="Valeur à rechercher")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Codes à barres trouvés avec succès",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/ArticleCodeBar")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function getArticleCodeBarByX(Request $request)
    {
        return response()->json(DB::table('article_code_bar')->where($request->field, $request->value)->get());

    }


    /**
     * @OA\Post(
     *     path="/ArticleCodebar/addArticleCodeBar",
     *     tags={"Article Code Barres Mobile"},
     *     summary="Ajouter un nouveau code à barres d'article",
     *     description="Crée un nouveau code à barres d'article dans la base de données",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Données du code à barres à créer",
     *         @OA\JsonContent(ref="#/components/schemas/ArticleCodeBarInput")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Code à barres créé avec succès",
     *         @OA\JsonContent(
     *             type="boolean",
     *             example=true
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Données invalides ou manquantes",
     *         @OA\JsonContent(
     *             type="boolean",
     *             example=false
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function addArticleCodeBar(Request $request)
    {
        $data = $request->json()->all();


        if (!empty ($data)) {
            return response()->json(DB::table('article_code_bar')->insert($data));
        } else {
            return (false);
        }


    }

    /**
     * @OA\Post(
     *     path="/ArticleCodebar/addArticleCodeBarMobile",
     *     tags={"Article Code Barres Mobile"},
     *     summary="Ajouter ou mettre à jour des codes à barres depuis mobile",
     *     description="Permet d'ajouter de nouveaux codes à barres ou de mettre à jour ceux existants depuis l'application mobile",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Données des codes à barres à ajouter/modifier",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="connexion",
     *                 ref="#/components/schemas/DatabaseConnection"
     *             ),
     *             @OA\Property(
     *                 property="object",
     *                 type="object",
     *                 @OA\Property(
     *                     property="articleCodeBars",
     *                     type="array",
     *                     @OA\Items(ref="#/components/schemas/ArticleCodeBarInput")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Codes à barres ajoutés/mis à jour avec succès",
     *         @OA\JsonContent(
     *             type="boolean",
     *             example=true
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Données invalides ou manquantes",
     *         @OA\JsonContent(
     *             type="boolean",
     *             example=false
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function addArticleCodeBarMobile(Request $request)
    {
        $items = $request->json()->all();
        $connection = DatabaseConnection::setConnection($items);

        if (!empty ($items)) {
            foreach ($items["object"]["articleCodeBars"] as $data) {
                if ($data["cod_b_export"] == "NULL")
                    $data["cod_b_export"] = NULL;

                $exist = $connection->table('article_code_bar')
                    ->where('Parent_CodeBar', '=', $data['Parent_CodeBar'])
                    ->where('Fils_CodeBar', '=', $data['Fils_CodeBar'])
                    ->get();
                if (is_null($exist) || $exist->isEmpty()) {
                    $f = $connection->table('article_code_bar')->insert($data);
                } else {
                    $f = $connection->table('article_code_bar')
                        ->where('Parent_CodeBar', '=', $data['Parent_CodeBar'])
                        ->where('Fils_CodeBar', '=', $data['Fils_CodeBar'])
                        ->update([
                            'cod_b_export' => $data['cod_b_export'],
                        ]);
                }

            }
            return response()->json($f);

        } else {
            return (false);
        }


    }


    /**
     * @OA\Post(
     *     path="/ArticleCodebar/updateArticleCodeBar",
     *     tags={"Article Code Barres Mobile"},
     *     summary="Mettre à jour un code à barres d'article",
     *     description="Met à jour les informations d'un code à barres d'article existant",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Données du code à barres à mettre à jour",
     *         @OA\JsonContent(ref="#/components/schemas/ArticleCodeBarInput")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Code à barres mis à jour avec succès",
     *         @OA\JsonContent(
     *             type="string",
     *             example="Data modified"
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function updateArticleCodeBar(Request $request)
    {
        $data = $request->json()->all();
        DB::table('article_code_bar')->where('Parent_CodeBar', $data["Parent_CodeBar"])->where('Fils_CodeBar', $data["Fils_CodeBar"])->update($data);
        return ("Data modified");

    }


    /**
     * @OA\Post(
     *     path="/ArticleCodebar/deleteArticleCodeBar",
     *     tags={"Article Code Barres Mobile"},
     *     summary="Supprimer un code à barres d'article",
     *     description="Supprime définitivement un code à barres d'article de la base de données",
     *     @OA\RequestBody(
     *         required=true,
     *         description="Codes parent et fils du code à barres à supprimer",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="Parent_CodeBar", type="string", example="PARENT001", description="Code à barres parent"),
     *             @OA\Property(property="Fils_CodeBar", type="string", example="FILS001", description="Code à barres fils")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Code à barres supprimé avec succès",
     *         @OA\JsonContent(
     *             type="string",
     *             example="Data Deleted"
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Erreur serveur",
     *         @OA\JsonContent(ref="#/components/schemas/ErrorResponse")
     *     )
     * )
     */
    public function deleteArticleCodeBar(Request $request)
    {
        DB::table('article_code_bar')->where('Parent_CodeBar', $request->Parent_CodeBar)->where('Fils_CodeBar', $request->Fils_CodeBar)->delete();
        return ("Data Deleted");
    }

    public function updateOrCreate($connection, $data, $table, $whereClauses)
    {
        $exists = $connection->table($table)->where($whereClauses)->first();

        if ($exists) {
            $connection->table($table)->where($whereClauses)->delete();
        }

        return $connection->table($table)->insert($data);

    }
}





