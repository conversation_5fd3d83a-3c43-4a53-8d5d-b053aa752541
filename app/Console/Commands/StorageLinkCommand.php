<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class StorageLinkCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:link';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {

        /*   if (file_exists(env('FILE_RESOURCES', 'C:\\asm\\ressources'))) {
               return $this->error('The ['.env('FILE_RESOURCES', 'C:\\asm\\ressources').'] directory already exists.');
           }*/

        $this->laravel->make('files')->link(
            storage_path('app/public'), env('FILE_RESOURCES', 'C:\\asm\\ressources')
        );

        $this->info('The [' . env('FILE_RESOURCES', 'C:\\asm\\ressources') . '] directory has been linked.');
    }
}
