<?php

namespace App\Traits;

use App\Exceptions\BaseException;
use App\Helpers\Enum;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Symfony\Component\HttpFoundation\Response;

/*
|--------------------------------------------------------------------------
| Api Responser Trait
|--------------------------------------------------------------------------
|
| This trait will be used for any response we sent to clients.
|
*/

trait ApiResponser
{
    /**
     * Return a success JSON response.
     *
     * @param array|string $data
     * @param string $message
     * @param int|null $code
     * @return \Illuminate\Http\JsonResponse
     */
    protected function successResponse($data = null, string $message = null, int $code = Response::HTTP_OK)
    {
        return response()->json([
            'status' => 'success',
            'error' => false,
            'message' => $message,
            'data' => $data
        ], $code, ['Content-type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }

    /**
     * Return an error JSON response.
     *
     * @param string $message
     * @param mixed|int $code
     * @param mixed|int $code_error
     * @param array|string|null $data
     * @return \Illuminate\Http\JsonResponse
     */
    protected function errorResponse(string $message = null, $data = null, $code_error = null, $code = Response::HTTP_UNPROCESSABLE_ENTITY)
    {
        return response()->json([
            'status' => $code,
            'error' => true,
            'code' => $code_error,
            'message' => $message,
            'data' => $data
        ], $code, ['Content-type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE)
            ->setStatusCode($code);
    }


    /**
     * Return an error JSON response.
     *
     * @param string $message
     * @param mixed|int $code
     * @param mixed|int $code_error
     * @param array|string|null $data
     * @return \Illuminate\Http\JsonResponse
     */
    protected function exceptionResponse(BaseException $exception)
    {
        return response()->json([
            'status' => $exception->getStatus(),
            'error' => true,
            'code' => $exception->getCode(),
            'message' => $exception->getMessage(),
        ], $exception->getStatus(), ['Content-type' => 'application/json; charset=utf-8'], JSON_UNESCAPED_UNICODE);
    }

    /**
     * Sends a json with a collection of data with a 200 http code as default
     *
     * @param \Illuminate\Support\Collection $collection
     * @param int $code
     * @return \Illuminate\Http\JsonResponse
     */
    protected function showAll(Collection $collection, $code = Response::HTTP_OK)
    {
        return $this->successResponse(['data' => $collection], $code);
    }

    /**
     * sends a json response with only one result
     *
     * @param \Illuminate\Database\Eloquent\Model $instance
     * @param int $code
     * @return \Illuminate\Http\JsonResponse
     */
    protected function showOne(Model $instance, $code = Response::HTTP_UNPROCESSABLE_ENTITY)
    {
        return $this->successResponse(['data' => $instance], $code);
    }

    protected function setResult($TIK_NumTicket, $TIK_IdCarnet, $TIK_Exerc, $TIK_NumTicket_M, $TIK_NumeroBL, $CodeClient
        ,                        $SoldeClient, $Debit, $Credit, $DEV_Observation, $message, $code
    ): array
    {
        return [
            "TIK_NumTicket" => $TIK_NumTicket,
            "TIK_IdCarnet" => $TIK_IdCarnet,
            "TIK_Exerc" => $TIK_Exerc,
            "TIK_NumTicket_M" => $TIK_NumTicket_M,
            "TIK_NumeroBL" => $TIK_NumeroBL,
            "CodeClient" => $CodeClient,
            "SoldeClient" => $SoldeClient,
            "Debit" => $Debit,
            "Credit" => $Credit,
            "DEV_Observation" => $DEV_Observation,
            "message" => $message,
            "code" => $code,
        ];
    }


    public function setResultBC($DEV_Num, $DEV_Exerc,$DEV_StationOrigine, $DEV_Station, $DEV_User, $DEV_Code_M, $DEV_info3, $message, $code, $lignes = array()): array
    {
        return [
            "DEV_Num" => $DEV_Num,
            "DEV_Exerc" => $DEV_Exerc,
            "DEV_StationOrigine" => $DEV_StationOrigine,
            "DEV_Station" => $DEV_Station,
            "DEV_User" => $DEV_User,
            "DEV_Code_M" => $DEV_Code_M,
            "DEV_info3" => $DEV_info3,
            "message" => $message,
            "code" => $code,
            "lignes" => $lignes,
        ];
    }

    public function setResultLigneBC($ligne, $code, $message)
    {
        $array = [
            "code" => $code,
            "message" => $message
        ];
        return array_merge($ligne, $array);
    }

    protected function setResultBonTransfert($BON_Trans_Num, $BON_Trans_Exerc, $message, $code,$BON_Trans_Num_M=null
    ): array
    {
        return [
            "BON_Trans_Num" => "" . $BON_Trans_Num,
            "BON_Trans_Num_M" => "" . $BON_Trans_Num_M,
            "BON_Trans_Exerc" => $BON_Trans_Exerc,
            "message" => $message,
            "code" => $code
        ];
    }

    protected function setRegelement($REGC_Code, $REGC_IdSCaisse, $REGC_Exercice, $REGC_Code_M, $CodeClient, $SoldeClient, $Debit, $Credit, $message, $code): array
    {
        return [
            "REGC_Code" => $REGC_Code,
            "REGC_IdSCaisse" => $REGC_IdSCaisse,
            "REGC_Exercice" => $REGC_Exercice,
            "REGC_Code_M" => $REGC_Code_M,
            "CodeClient" => $CodeClient,
            "SoldeClient" => $SoldeClient,
            "Debit" => $Debit,
            "Credit" => $Credit,
            "message" => $message,
            "code" => $code
        ];
    }

    public function setControlePatrimoine($message, $code)
    {
        return [
            "message" => $message,
            "code" => $code
        ];
    }

    public function setBonEntree($BON_ENT_Num,$BON_ENT_Num_M, $BON_ENT_Exer,$errors, $message, $code = Enum::Success_Code)
    {
        return [
            "BON_ENT_Num" => $BON_ENT_Num,
            "BON_ENT_Num_M" =>$BON_ENT_Num_M,
            "BON_ENT_Exer" => $BON_ENT_Exer,
            "errors" => $errors,
            "message" => $message,
            "code" => $code
        ];
    }

    public function controlCalculateBC($orderLine, $message, $amountCalculated,$code = Enum::Success_Code)
    {
        return [
            "ORDER_LIG" => $orderLine,
            "message" => $message,
            "code" => $code,
            "amount_calculated"=>$amountCalculated
        ];
    }

    public function setGlobalResult($key, $value, $message, $code = Enum::Success_Code)
    {
        return [
            $key => $value,
            "message" => $message,
            "code" => $code
        ];
    }


    public function setResultFamille($FAM_Code, $message, $code = Enum::Success_Code)
    {
        return [
            "FAM_Code" => $FAM_Code,
            "message" => $message,
            "code" => $code
        ];
    }

    protected function setResultFacture($TIK_NumTicket, $TIK_IdCarnet, $TIK_Exerc, $TIK_NumTicket_M, $TIK_NumeroBL, $CodeClient
        ,                        $SoldeClient, $Debit, $Credit, $DEV_Observation, $message, $code,$facture
    ): array
    {
        return [
            "TIK_NumTicket" => $TIK_NumTicket,
            "TIK_IdCarnet" => $TIK_IdCarnet,
            "TIK_Exerc" => $TIK_Exerc,
            "TIK_NumTicket_M" => $TIK_NumTicket_M,
            "TIK_NumeroBL" => $TIK_NumeroBL,
            "CodeClient" => $CodeClient,
            "SoldeClient" => $SoldeClient,
            "Debit" => $Debit,
            "Credit" => $Credit,
            "DEV_Observation" => $DEV_Observation,
            "message" => $message,
            "code" => $code,
            "facture"=>$facture
        ];
    }
}
