<?php

namespace App\Providers;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        //
        if (env('IS_HTTPS')) {
            \URL::forceScheme('https');
        }
        Response::macro('partialSuccess', function ($updatedAndCreatedObjects, $errors) {
            return response()->json([
                'status' => 'partial_success',
                'data' => $updatedAndCreatedObjects,
                'errors' => $errors
            ], 207);
        });

        Response::macro('errors', function ($errors) {
            return response()->json([
                'status' => 'error',
                'errors' => $errors
            ], 500);
        });

        Response::macro('success', function ($data = null) {
            return response()->json([
                'status' => 'success',
                'data' => $data
            ], 200);
        });

        Response::macro('error', function ($e) {
            Log::error('Error get: ' . $e->getMessage(), ['$e' => $e]);
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        });

        Response::macro('modelNotFound', function ($id) {
            return response()->json([
                'status' => 'error',
                'message' => 'Record with id ' . $id . ' not found',
            ], 404);
        });
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }
}
