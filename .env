APP_NAME=ProCaisse
APP_ENV=local
APP_KEY=base64:qZ/wCH5vU0DYdqenEoR82x0AOON9/SuUbH7MeA7gOhs=
APP_DEBUG=false
APP_URL=http://localhost
IS_HTTPS=false

LOG_CHANNEL=daily
LOG_LEVEL=debug
DB_CONNECTION=sqlsrv
DB_HOST=*************
DB_PORT=1433
DB_DATABASE=chahia
DB_USERNAME=sa
DB_PASSWORD=ASMpass2024+*
DB_FROM_ENV=false

BROADCAST_DRIVER=log
CACHE_DRIVER=none
CACHE_EXPIRE=-1
FILESYSTEM_DRIVER=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=null
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

APP_LOG=daily

L5_SWAGGER_GENERATE_ALWAYS=true
L5_SWAGGER_CONST_HOST='http://127.0.0.1:8000/api'

MIN_DB_VERSION=1.0.0
MIN_VERSION_CODE=1.0.0

SENTRY_LARAVEL_DSN=https://<EMAIL>/6079608

SENTRY_TRACES_SAMPLE_RATE=1.0

WITH_MOUVEMENT=true

DATE_FORMAT="m/d/Y H:i:s"
#DATE_FORMAT = "Y-m-d H:i:s"
FORCED_DATE_FORMAT=FALSE

#CONFIG_BATCH_FILE=C:\ASMWS\www\ProCaisse\config.bat
#SERVER_PORT=8020
#PROJECT_PATH="C:/ASMWS/www/ProCaisse/public"
#PROJECT_ALIAS="procaisse.asm"
