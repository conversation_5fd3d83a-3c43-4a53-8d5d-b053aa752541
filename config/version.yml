mode: absorb
version_source: config
current:
  label: v
  major: 1
  minor: 0
  patch: 0
  prerelease: beta
  buildmetadata: '4'
  commit:
    length: 6
    increment-by: 1
    mode: git-local
  git_absorb: git-local
  format: '{$major}.{$minor}.{$patch}'
  timestamp:
    year: 2022
    month: 7
    day: 15
    hour: 16
    minute: 34
    second: 51
    timezone: Africa/Tunis
git:
  mode: git-local
  from: local
  commit:
    local: 'git rev-parse --verify HEAD'
  branch: refs/heads/master
  repository: ''
  version:
    local: 'git describe'
    matcher: '(.*?)'
  timestamp:
    local: 'git show -s --format=%ci'
    remote: 'git show -s --format=%ci origin/master'
format:
  major: '{$major}'
  minor: '{$minor}'
  patch: '{$patch}'
  build: 1.0.1
  version: '{$major}.{$minor}.{$patch} {YYYYMMDD}'
  full: 'version {$major}.{$minor}.{$patch} (build {$timestamp.year}{$timestamp.month}{$timestamp.day}-{$timestamp.hour}{$timestamp.minute})'
  compact: '{$major}.{$minor}.{$patch}-{YYYYMMDD}'
