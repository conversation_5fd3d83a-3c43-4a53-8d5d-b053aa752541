<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateUtilisateurTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('Utilisateur', 'ClotSessAuto')) {

            Schema::table('Utilisateur', function ($table) {
                $table->boolean('ClotSessAuto')->nullable();
            });
        }
        if (!Schema::hasColumn('Utilisateur', 'CrtourneAuto')) {
            Schema::table('Utilisateur', function ($table) {
                $table->boolean('CrtourneAuto')->nullable();
            });
        }
        if (!Schema::hasColumn('Utilisateur', 'HeurFintourne')) {
            Schema::table('Utilisateur', function ($table) {
                $table->dateTime('HeurFintourne')->nullable();

            });
        }

        if (!Schema::hasColumn('Utilisateur', 'CltEquivalent')) {
            Schema::table('Utilisateur', function ($table) {
                $table->string('CltEquivalent')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {

    }
}
