<?php

use App\Helpers\DB;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

/**
 * Class CreateSettingsTable.
 */
class CreateSettingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('settings_mobile')) {
            Schema::create('settings_mobile', function (Blueprint $table) {
                $table->bigInteger('id')->primary();
                $table->string('name');
                $table->string('type');
                $table->boolean('isActive')->default(true);
                $table->timestamps();
            });

            DB::table('settings_mobile')->insert([
                'id' => 1001540883,
                'name' => 'inventory_drawer_item',
                'type' => 'main_menu',
                'isActive' => true,
            ]);

            DB::table('settings_mobile')->insert([
                'id' => 1201584442,
                'name' => 'transfert_drawer_item',
                'type' => 'main_menu',
                'isActive' => true,
            ]);

            DB::table('settings_mobile')->insert([
                'id' => 1306420261,
                'name' => 'purchase_drawer_item',
                'type' => 'main_menu',
                'isActive' => true,
            ]);

            DB::table('settings_mobile')->insert([
                'id' => 1402573242,
                'name' => 'scanner_drawer_item',
                'type' => 'main_menu',
                'isActive' => true,
            ]);

            DB::table('settings_mobile')->insert([
                'id' => 1503142697,
                'name' => 'article_drawer_item',
                'type' => 'main_menu',
                'isActive' => true,
            ]);

            DB::table('settings_mobile')->insert([
                'id' => 1604405923,
                'name' => 'ticket_rayon_drawer_item',
                'type' => 'main_menu',
                'isActive' => true,
            ]);
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('settings');
    }
}
