<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

class UpdateUserTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('Utilisateur', 'Autorise_Clt')) {
            Schema::table('Utilisateur', function ($table) {
                $table->boolean('Autorise_Clt')->default(false);
            });
        }
        if (!Schema::hasColumn('Utilisateur', 'Autorise_Tour')) {
            Schema::table('Utilisateur', function ($table) {
                $table->boolean('Autorise_Tour')->default(false);
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
}
