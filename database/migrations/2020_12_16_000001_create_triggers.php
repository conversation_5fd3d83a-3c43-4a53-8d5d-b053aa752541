<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;


class CreateTriggers extends Migration
{

    protected $tables = ["article", "depence_caisse"];

    /**
     * Run the migrations.
     *
     * @return void
     */

    public function up()
    {
        if (!Schema::hasTable('Logs')) {
            Schema::create('Logs', function (Blueprint $table) {
                $table->text('tableName');
                $table->text('operation');
                $table->timestamp('created_at')->useCurrent();
            });
        }
        try {
            foreach ($this->tables as $table) {

                DB::unprepared("CREATE TRIGGER TR_" . $table . " ON " . $table . " FOR INSERT, UPDATE, DELETE
AS
DECLARE @Operation VARCHAR(15)

IF EXISTS (SELECT 0 FROM inserted)
BEGIN
   IF EXISTS (SELECT 0 FROM deleted)
   BEGIN
      SELECT @Operation = 'UPDATE'
   END ELSE
   BEGIN
      SELECT @Operation = 'INSERT'
   END
END ELSE
BEGIN
   SELECT @Operation = 'DELETE'
END
INSERT INTO dbo.Logs
(created_at, tableName, operation)
VALUES(CAST( GETDATE() AS datetime ), '" . $table . "', @Operation)");

            }
        } catch (Exception $e) {

        }


    }

}



