<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

class UpdateBonEntreeMobileTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('bon_entree_mobile', 'BON_ENT_NumPiece')) {
            Schema::table('bon_entree_mobile', function ($table) {
                $table->string('BON_ENT_NumPiece')->nullable();
            });
        }
        if (!Schema::hasColumn('bon_entree_mobile', 'BON_ENT_TypePiece')) {
            Schema::table('bon_entree_mobile', function ($table) {
                $table->string('BON_ENT_TypePiece')->nullable();
            });
        }
    }
    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {

    }
}
