<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

class UpdateStationTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('station', 'STATMetrageM')) {
            Schema::table('station', function ($table) {
                $table->float('STATMetrageM')->nullable();
            });
        }
        if (!Schema::hasColumn('station', 'STATSecondeM')) {
            Schema::table('station', function ($table) {
                $table->float('STATSecondeM')->nullable();
            });
        }

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {

    }
}
