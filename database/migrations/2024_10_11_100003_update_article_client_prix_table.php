<?php

use App\Helpers\DB;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

class UpdateArticleClientPrixTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('article_client_prix', 'ART_TRemise')) {
            Schema::table('article_client_prix', function ($table) {
                $table->double('ART_TRemise');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public
    function down()
    {
    }
}

