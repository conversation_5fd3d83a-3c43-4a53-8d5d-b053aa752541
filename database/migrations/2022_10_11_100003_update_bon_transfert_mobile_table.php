<?php

use App\Helpers\DB;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

class UpdateBonTransfertMobileTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

                DB::table('Prefixe')
                    ->where('PRE_Id_table', 'bon_transfert')
                    ->update(array('Pre_Préfixe' => "BT\\"));


    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public
    function down()
    {
    }
}

