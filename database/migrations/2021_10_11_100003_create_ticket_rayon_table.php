<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTicketRayonTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('ticket_rayon_mobile')) {
            Schema::create('ticket_rayon_mobile', function (Blueprint $table) {
                $table->string('ART_Code')->nullable();
                $table->string('ART_Designation')->nullable();
                $table->dateTime('ddm')->nullable();
                $table->boolean('ART_SYNC')->default(false);
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ticket_rayon_mobile');
    }
}
