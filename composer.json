{"name": "laravel/laravel", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "type": "project", "require": {"php": ">=7.1", "ext-json": "*", "ext-pdo": "*", "alexusmai/laravel-file-manager": "^2.5", "arcanedev/log-viewer": "3.4.0", "chumper/zipper": "^1.0", "darkaonline/l5-swagger": "^5.8", "doctrine/dbal": "2.*", "dompdf/dompdf": "^1.1", "earlpeterg/php-password-decipher": "^0.1.0", "geo-sot/laravel-env-editor": "^0.9.12", "guzzlehttp/guzzle": "^7.5", "jdavidbakr/laravel-cache-garbage-collector": "*", "laravel/framework": "^5.8.0", "laravel/tinker": "~1.0", "moontoast/math": "^1.2", "phpseclib/mcrypt_compat": "^2.0", "pragmarx/version": "^1.3", "rap2hpoutre/laravel-log-viewer": "^1.7", "sentry/sentry-laravel": "^2.12", "youthage/laravel-3des": "^4.0"}, "require-dev": {"brainmaestro/composer-git-hooks": "^2.8", "fzaninotto/faker": "~1.4", "krlove/eloquent-model-generator": "^1.3", "mockery/mockery": "0.9.*", "phpunit/phpunit": "~5.7"}, "autoload": {"classmap": ["database"], "psr-4": {"App\\": "app/"}, "files": ["app/Helpers/GenerateId.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-root-package-install": ["php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["php artisan key:generate"], "post-install-cmd": ["Illuminate\\Foundation\\ComposerScripts::postInstall"], "post-update-cmd": ["Illuminate\\Foundation\\ComposerScripts::postUpdate"]}, "extra": {"hooks": {"pre-push": ["echo \"The most recent tag $tag does not match the application version $appver\\n\""]}}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true, "disable-tls": false, "secure-http": false, "allow-plugins": {"composer/package-versions-deprecated": true, "php-http/discovery": true}}}