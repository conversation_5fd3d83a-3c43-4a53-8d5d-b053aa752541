server {
    listen 80;
    index index.php index.html;
    error_log  /var/log/nginx/error.log;
    access_log /var/log/nginx/access.log;
    client_max_body_size 300M;
    root /var/www/public;
    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass php:9000;
        fastcgi_index index.php;
        fastcgi_read_timeout 6000s;
        fastcgi_connect_timeout 300s;
        fastcgi_send_timeout 300s;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
        proxy_connect_timeout 6000s;
        proxy_send_timeout 6000s;
        proxy_read_timeout 6000s;
        send_timeout 6000s;
        proxy_set_header   X-Real-IP $remote_addr;
        proxy_set_header   Host      $http_host;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
    }
    location / {
        try_files $uri $uri/ /index.php?$query_string;
        gzip_static on;
        proxy_connect_timeout 6000s;
        proxy_send_timeout 6000s;
        proxy_read_timeout 6000s;
        send_timeout 6000s;
        proxy_set_header   X-Real-IP $remote_addr;
        proxy_set_header   Host      $http_host;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
    }


}
