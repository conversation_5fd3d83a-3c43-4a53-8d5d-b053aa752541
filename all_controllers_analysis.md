# Analyse Complète des Contrôleurs dans web.php

## Contrôleurs Identifiés (par ordre d'apparition)

### 1. **ClientWSMobile** ✅ (Déjà documenté)
- addBatchClient, getClient, getClientByCode, getClientByX, addClient, updateClient, deleteClient, AffectCodeBareBatiment

### 2. **TicketWSMobile** ✅ (Partiellement documenté)
- addBatchTicketWithLignesTicketAndPayment, getTicket, getCAparMarque, getCAparFamille, getCAparFournisseur, getCAparClient, getMaxNumTicket, getCAparArticle, getTicketByX, addTicketWithLignesTicket, addTicketWithLignesTicketAndPayment, getTicketsByCaisseId, addTicket, deleteTicket, updateTicket, DisplayNombreTicket, DisplayNombreTicketByCaissier, DisplayDetailArticleSupprimee, DisplayDetailArticleSupprimeeByCaissier, DisplayDetailTicketValidee, DisplayDetailTicketAnnule

### 3. **BonLivraisonController** 🔄 (À documenter)
- addBatchBonLivraison, addBatchBonLivraisonWithLines, getBonLivraison, getBonRetour

### 4. **BonEntreeWSMobile** 🔄 (À documenter)
- addBatchBonEntreesWithLines, addBatchBonEntrees, getBonEntrees

### 5. **ReglementCaisseWSMobile** 🔄 (À documenter)
- addBatchPayments, getReglementCaisseByTickets, getReglementCaisseByTicket, getReglementCaisseBySession

### 6. **LigneBonCommandeWSMobile** 🔄 (À documenter)
- addBatchLigneBonCommande, addBatchLigneBonCommandeTest, getLigneBonCommande, upload, getImages

### 7. **LigneBonRetourWSMobile** 🔄 (À documenter)
- addBatchLigneBonRetour, getLigneBonRetour

### 8. **BonCommandeWSMobile** ✅ (Partiellement documenté)
- addBatchBonCommande, getBonCommande, getBonCommandeWithPagination, controle_inventaire, getAllTypeMouvement, getAllDeplacememntOutByUser

### 9. **BonRetourWSMobile** 🔄 (À documenter)
- addBatchBonRetour, getBonRetour

### 10. **LigneBonEntreeWSMobile** 🔄 (À documenter)
- addBatchLigneBonEntrees, getLigneBonEntrees, updateBatchLigneBonEntrees, deleteBatchLigneBonEntrees, test

### 11. **FactureMobileController** 🔄 (À documenter)
- addBatchFactureWithLines, getFacture

### 12. **LigneBonLivraisonController** 🔄 (À documenter)
- addBatchLigneBonLivraison, addBatchLigneBonLivraisonMobile, getLigneBonLivraison, updateBatchLigneBonLivraison, deleteBatchLigneBonLivraison

### 13. **InventaireWSMobile** 🔄 (À documenter)
- addBatchInventaires, addBatchInventairesWithLines, getInventaires

### 14. **LigneInventaireWSMobile** 🔄 (À documenter)
- addBatchLigneInventaires, getLigneInventaires, updateBatchLigneInventaires, deleteBatchLigneInventaires

### 15. **OrdreMissionController** 🔄 (À documenter)
- AddOrdreMission, DisplayEtatOrdreMission, DisplayOrdre, UpdateEtatOrdreMission, BatchUpdateOrdreMission

### 16. **VConcuController** 🔄 (À documenter)
- addBatchDataVConcu, getDataVConcu, getDataVConcuByCode, addDataVConcu, deleteDataVConcu, getVCListeConcurrent, getVCTypeCommunication, getVCImage, upload, getImages

### 17. **CouleurWSMobile** 🔄 (À documenter)
- getCouleur, getCouleurByCode, getCouleurByX, addCouleur, updateCouleur, deleteCouleur

### 18. **ArticleCodeBarWSMobile** ✅ (Déjà documenté)
- addArticleCodeBarMobile

### 19. **ArticleWSMobile** ✅ (Partiellement documenté)
- getCodeTest, getCodeBarPagination, getArticleClientPrix, getArticleByBarCode, getArticleByName, getArticle, getArticleWithPagination, getCountArticle, ArticleWithTransaction, getArticles, addArticleMobile, getArticlesByUser, getArticleByStation, getArticleByCode, getArticleByX, addArticle, updateArticle, deleteArticle, addArticleMobileV2, getArticleFamille, addArticleMobileV3, getArticleActif, DisplayTopArticleByStation, getArticleEnRuptureDeStock, DisplayArticleEnReptureStockByStation, DisplayTotalValeurStockByStation, DisplayValeurStockByAllStation, getProducts, DisplayProduitByStation, getArticleAnomalie, get_counts_of_products, get_count_getArticleAnomalie, get_count_getArticleEnRuptureDeStock, get_count_getProducts, get_count_getArticleActif, getArticleByChunk

### 20. **Article_Code_barWSMobile** 🔄 (À documenter)
- getArticle_Code_bar, getArticle_Code_barByCode, getArticle_Code_barByX, addArticle_Code_bar, updateArticle_Code_bar, deleteArticle_Code_bar

## TOTAL: 50+ contrôleurs identifiés
## DOCUMENTÉS: ~8 contrôleurs (16%)
## À DOCUMENTER: ~42 contrôleurs (84%)
